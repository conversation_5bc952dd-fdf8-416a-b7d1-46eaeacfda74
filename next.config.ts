import type { NextConfig } from "next";
import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin();

const nextConfig: NextConfig = {
  reactStrictMode: false,
  /* config options here */
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com",
      },
      {
        protocol: "https",
        hostname: "s3.us-east-2.amazonaws.com",
      },
      {
        protocol: "https",
        hostname: "media.theresanaiforthat.com",
      },
      {
        protocol: "https",
        hostname: "cdn.aimlapi.com",
      },
      {
        protocol: "https",
        hostname: "cdn.gptgirlfriend.online",
      },
      {
        protocol: "https",
        hostname: "nsfw.tools",
      },
    ],
  },
};

export default withNextIntl(nextConfig);
