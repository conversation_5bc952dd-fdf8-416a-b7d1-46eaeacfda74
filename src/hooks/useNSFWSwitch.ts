import React, { useEffect, useState } from "react";
import { apiService } from "@/service/Api";
import { useUserStores } from "@/stores/UserStores";

export const useNSFWSwitch = () => {
  const [openNSFWDialog, setOpenNSFWDialog] = useState<boolean>(false);
  const { setGlobalNSFWSwitch, globalNSFWStatus } = useUserStores();

  useEffect(() => {
    const globalNSFWSwitch = localStorage.getItem("global_nsfw_switch");
    if (globalNSFWSwitch) {
      setGlobalNSFWSwitch(globalNSFWSwitch === "true");
    }
  }, []);

  const handleChangeNSFW = async () => {
    const nsfw_status = !globalNSFWStatus;
    if (nsfw_status) {
      setOpenNSFWDialog(true);
      return;
    }
    handleConfirmNSFW(nsfw_status);
  };

  const handleConfirmNSFW = async (nsfw_status: boolean) => {
    setGlobalNSFWSwitch(nsfw_status);
    setOpenNSFWDialog(false);

    const resp = await apiService.updateUserPreferences({
      preferences_config: { nsfw_switch: nsfw_status },
    });
    if (resp.code === 200) {
      localStorage.setItem("global_nsfw_switch", nsfw_status.toString());
    } else {
      setGlobalNSFWSwitch(!nsfw_status);
    }
  };
  return {
    globalNSFWStatus,
    openNSFWDialog,
    setOpenNSFWDialog,
    handleChangeNSFW,
    handleConfirmNSFW,
  };
};
