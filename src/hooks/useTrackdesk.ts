/* eslint-disable prefer-rest-params */

import { useUserStores } from "@/stores/UserStores";
import { useState, useEffect } from "react";

declare global {
  interface Window {
    TrackdeskObject?: string[];
    trackdesk?: (
      tenantId: string,
      action: string,
      options: { externalCid: string; revenueOriginId: string }
    ) => void;
  }
}

const useTrackdesk = () => {
  const { userEntity } = useUserStores();
  const [scriptLoaded, setScriptLoaded] = useState(false);

  const initializeTrackdesk = () => {
    if (typeof window === "undefined" || !scriptLoaded) return;

    (function (t: any, d: any, k: any) {
      (t[k] = t[k] || []).push(d);
      t[d] =
        t[d] ||
        t[k].f ||
        function () {
          (t[d].q = t[d].q || []).push(arguments);
        };
    })(window, "trackdesk", "TrackdeskObject");

    if (window.trackdesk && userEntity?.email) {
      window.trackdesk("sweetaichat", "externalCid", {
        externalCid: userEntity?.email,
        revenueOriginId: "58dbd9f0-f8c6-4071-b9ba-30d8b390f2b8",
      });
    }
  };

  useEffect(() => {
    if (typeof window === "undefined") return;

    const script = document.createElement("script");
    script.src = "//cdn.trackdesk.com/tracking.js";
    script.async = true;
    script.onload = () => setScriptLoaded(true);
    // script.onerror = () => console.error("Failed to load trackdesk script");
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  useEffect(() => {
    if (userEntity?.email && !userEntity.guest_flag && scriptLoaded) {
      initializeTrackdesk();
    }
  }, [userEntity?.email, scriptLoaded]);

  return { initializeTrackdesk, scriptLoaded };
};

export default useTrackdesk;
