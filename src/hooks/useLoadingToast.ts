import toast from "react-hot-toast";

interface LoadingToastOptions {
  message?: string;
  position?:
    | "top-left"
    | "top-center"
    | "top-right"
    | "bottom-left"
    | "bottom-center"
    | "bottom-right";
}

export const useLoadingToast = () => {
  const showLoading = ({
    message = "Loading...",
    position = "top-center",
  }: LoadingToastOptions = {}) => {
    return toast.loading(message, {
      position,
      style: {
        borderRadius: "50px",
        background: "#171719",
        color: "#fff",
        fontSize: "14px",
        width: "123px",
        padding: "4px 8px",
      },
      iconTheme: {
        primary: "var(--primary-color)",
        secondary: "#333",
      },
    });
  };

  const dismissLoading = (toastId?: string) => {
    if (toastId) {
      toast.dismiss(toastId);
    } else {
      toast.dismiss();
    }
  };

  return {
    showLoading,
    dismissLoading,
  };
};
