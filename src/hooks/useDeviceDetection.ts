import { useState, useEffect } from "react";

interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  isSafari: boolean;
}

export const useDeviceDetection = (): DeviceInfo => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isIOS: false,
    isAndroid: false,
    isSafari: false,
  });

  useEffect(() => {
    if (typeof window === "undefined") return;

    const userAgent = navigator.userAgent;

    // 检测移动设备
    const isMobile =
      /Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        userAgent
      );

    // 检测平板设备
    const isTablet = /iPad|Android(?!.*Mobile)/i.test(userAgent);

    // 检测桌面设备
    const isDesktop = !isMobile && !isTablet;

    // 检测 iOS
    const isIOS = /iPad|iPhone|iPod/.test(userAgent);

    // 检测 Android
    const isAndroid = /Android/i.test(userAgent);

    // 检测 Safari
    const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);

    setDeviceInfo({
      isMobile,
      isTablet,
      isDesktop,
      isIOS,
      isAndroid,
      isSafari,
    });
  }, []);

  return deviceInfo;
};

// 视频自动播放策略
export const getVideoAutoPlayStrategy = (deviceInfo: DeviceInfo) => {
  // 在移动设备上禁用自动播放
  if (deviceInfo.isMobile || deviceInfo.isTablet) {
    return {
      autoPlay: false,
      shouldAttemptPlay: false,
    };
  }

  // 在 Safari 上谨慎处理自动播放
  if (deviceInfo.isSafari) {
    return {
      autoPlay: false,
      shouldAttemptPlay: true,
    };
  }

  // 在桌面设备上允许自动播放
  return {
    autoPlay: false,
    shouldAttemptPlay: true,
  };
};
