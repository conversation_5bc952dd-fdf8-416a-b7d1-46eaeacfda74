"use client";
import { SOURCE_ID_KEY } from "@/constant";
import { INVITE_CODE_KEY } from "@/constant";
import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useUserStores } from "@/stores/UserStores";

export const useInitOpt = () => {
  const searchParams = useSearchParams();
  const { initUserInfo } = useUserStores();

  useEffect(() => {
    initUserInfo();
  }, []);

  useEffect(() => {
    const inviteCode = searchParams.get(INVITE_CODE_KEY) || "newbee";
    const sourceId = searchParams.get(SOURCE_ID_KEY);
    localStorage.setItem(INVITE_CODE_KEY, inviteCode);
    if (sourceId) {
      localStorage.setItem(SOURCE_ID_KEY, sourceId);
    }
  }, []);

  useEffect(() => {
    const setRealVh = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty("--vh", `${vh}px`);
    };

    setRealVh();

    window.addEventListener("resize", () => {
      setRealVh();
    });

    return () => {
      window.removeEventListener("resize", () => {
        setRealVh();
      });
    };
  }, []);
};
