import { driver } from "driver.js";
import "driver.js/dist/driver.css";
import { useTranslations } from "next-intl";
import { useEffect } from "react";
import "./styles.css";

export default function useModelSwitcher(className: string) {
  const t = useTranslations("guideContent.aiFeatures");
  useEffect(() => {
    const showCount = parseInt(
      localStorage.getItem("modelSwitcherGuideCount") || "0"
    );

    if (showCount >= 3) {
      return;
    }

    const driverObj = driver({
      popoverClass: "driverjs-theme",
    });
    driverObj.highlight({
      element: className,
      popover: {
        title: t("title"),
        description: t("description"),
      },
    });

    localStorage.setItem("modelSwitcherGuideCount", (showCount + 1).toString());

    const handleClick = () => {
      driverObj.destroy();
    };
    document.addEventListener("click", handleClick);

    return () => {
      driverObj.destroy();
      document.removeEventListener("click", handleClick);
    };
  }, []);
}
