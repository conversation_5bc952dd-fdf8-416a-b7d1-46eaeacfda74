.driver-popover.driverjs-theme {
  background-color: #272943;
  color: white;
  border: 1px solid rgba(145, 112, 240, 0.2);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

.driver-popover.driverjs-theme .driver-popover-title {
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.driver-popover.driverjs-theme .driver-popover-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  line-height: 1.5;
}

.driver-popover.driverjs-theme .driver-popover-progress-text {
  color: rgba(255, 255, 255, 0.6);
}

.driver-popover.driverjs-theme .driver-popover-navigation-btns {
  gap: 8px;
}

.driver-popover.driverjs-theme .driver-popover-next-btn,
.driver-popover.driverjs-theme .driver-popover-prev-btn,
.driver-popover.driverjs-theme .driver-popover-close-btn {
  background: #9170f0;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s;
}

.driver-popover.driverjs-theme .driver-popover-next-btn:hover,
.driver-popover.driverjs-theme .driver-popover-prev-btn:hover,
.driver-popover.driverjs-theme .driver-popover-close-btn:hover {
  background: rgba(145, 112, 240, 0.8);
}

.driver-popover.driverjs-theme .driver-popover-arrow {
  border-color: #272943;
}

.driver-overlay.driverjs-theme {
  background: rgba(0, 0, 0, 0.75);
}
