import { geolocation } from "@vercel/functions";
import createMiddleware from "next-intl/middleware";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { routing } from "./i18n/routing";

const intlMiddleware = createMiddleware(routing);
const BLOCKED_COUNTRY = "CN";

export default async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  request.headers.set("x-pathname", pathname);
  const parsedUrl = new URL(request.url);
  const searchParams = new URLSearchParams(parsedUrl.search);
  request.cookies.set("cookie_searchParams", searchParams.toString());

  // 地理位置
  const country = geolocation(request).country || "US";
  if (country === BLOCKED_COUNTRY) {
    return NextResponse.rewrite(new URL("/en/not-found", request.url));
  }

  // 路由
  const hasLangPrefix = /^\/(en|fr|pt|ko|ja|es)(\/|$)/.test(pathname);
  if (!hasLangPrefix) {
    const newPath = `/en${pathname}`;
    const parsedUrl = new URL(request.url);
    const searchParams = new URLSearchParams(parsedUrl.search);
    const response = NextResponse.rewrite(new URL(newPath, request.url));
    response.cookies.set("x-pathname", newPath);
    response.cookies.set("cookie_searchParams", searchParams.toString());
    return response;
  }

  return intlMiddleware(request);
}

export const config = {
  matcher: ["/", "/(en|fr|pt|ko|ja|es)/:path*", "/((?!api/|_next|.*\\..*).*)"],
};
