import { create } from "zustand";
import {
  APP_ID_DELORIS,
  INVITE_CODE_KEY,
  SOULMATE_PAGE_SIZE,
  STORAGE_CHAT_NSFW,
  STORAGE_TOKEN,
  STORAGE_USER_INFO,
  USER_UUID,
} from "@/constant";
import {
  AuthRequest,
  AuthRequestAttribution,
  AuthResponse,
  BotTemplate,
  ImageGenStyleItem,
  LoginResponse,
  UserEntity,
} from "@/types";
import { apiService, clearToken, updateToken } from "@/service/Api";
import { showToast } from "@/utils/toast";
import { clearSoulChatStore } from "./SoulChatStore";

type Action = {
  changeLoginDialogState: (open: boolean) => void;
  authGoogle: (
    token: string,
    attribution?: AuthRequestAttribution
  ) => Promise<AuthResponse>;
  initUserInfo: () => void;
  fetchUserInfo: () => Promise<UserEntity>;
  logout: () => void;
  updateUserInfo: (userEntity: UserEntity) => void;
  resetRefresh: () => void;
  getCustomTemplate: (page?: number, pageSize?: number) => Promise<void>;
  fetchBookmarkTemplate: (page?: number, pageSize?: number) => Promise<void>;
  setGlobalNSFWSwitch: (nsfwStatus: boolean) => void;
  fetchInterestInList: () => Promise<void>;
};

type State = {
  loading: boolean;
  userEntity: UserEntity | null;
  needRefresh: boolean;
  customTemplate: {
    list: BotTemplate[];
    totalCount: number;
  };
  bookmarkTemplate: {
    list: BotTemplate[];
    totalCount: number;
  };
  loginDialogOpen: boolean;
  globalNSFWStatus: boolean;
  interestInList: ImageGenStyleItem[];
};

const initStatus: State = {
  loading: false,
  userEntity: null,
  needRefresh: true,
  customTemplate: {
    list: [],
    totalCount: 0,
  },
  bookmarkTemplate: {
    list: [],
    totalCount: 0,
  },
  loginDialogOpen: false,
  globalNSFWStatus: false,
  interestInList: [],
};

export const useUserStores = create<Action & State>((set, get) => ({
  ...JSON.parse(JSON.stringify(initStatus)),
  changeLoginDialogState(open: boolean) {
    set(() => ({
      loginDialogOpen: open,
    }));
  },
  async authGoogle(token: string, attribution?: AuthRequestAttribution) {
    set(() => ({
      loading: true,
    }));
    const inviteCode = localStorage.getItem(INVITE_CODE_KEY);
    const authReq = {
      token,
      channel: "google",
      inviteCode,
      app_id: APP_ID_DELORIS,
      attribution,
    } as AuthRequest;

    if (get().userEntity?.guest_flag) {
      authReq.guest_user_id = get().userEntity?.id || "";
    }
    await apiService
      .Auth(authReq)
      .then((authResponse) => {
        if (authResponse.code == 200) {
          updateToken(authResponse.data.token);
          return apiService.Login();
        } else {
          set(() => ({
            loading: false,
          }));
          showToast("Auth Failed", "error");
        }
      })
      .then((loginResponse) => {
        if (loginResponse?.code == 200) {
          set(() => ({
            userEntity: loginResponse.data,
          }));
          localStorage.setItem(
            STORAGE_USER_INFO,
            JSON.stringify(loginResponse.data)
          );
          localStorage.setItem(
            STORAGE_CHAT_NSFW,
            JSON.stringify(loginResponse.data?.check_nsfw)
          );
          clearSoulChatStore();
          localStorage.removeItem(USER_UUID);
          showToast("Login Success", "success");
        } else {
          showToast("Login Failed", "error");
        }
      })
      .catch(() => {
        showToast("Auth Failed", "error");
      })
      .finally(() => {
        set(() => ({
          loading: false,
        }));
      });
  },
  initUserInfo() {
    const userInfoJson = localStorage.getItem(STORAGE_USER_INFO);
    const userToken = localStorage.getItem(STORAGE_TOKEN);
    if (userToken) {
      updateToken(userToken);
    }
    if (userInfoJson) {
      const userEntity: UserEntity = JSON.parse(userInfoJson);
      set(() => ({
        userEntity,
      }));
    }
  },
  async fetchUserInfo() {
    return new Promise((resolve) => {
      apiService.Login().then((resp: LoginResponse) => {
        if (resp.code == 200 && resp.data) {
          set(() => ({
            needRefresh: false,
            userEntity: resp.data,
          }));
          localStorage.setItem(STORAGE_USER_INFO, JSON.stringify(resp.data));
          localStorage.setItem(
            STORAGE_CHAT_NSFW,
            JSON.stringify(resp.data?.check_nsfw)
          );
          resolve(resp.data);
        }
      });
    });
  },
  async fetchInterestInList() {
    const resp = await apiService.getInterestInList();

    if (resp.code === 200 && resp.data) {
      set(() => ({
        interestInList: resp.data.interested_in_list.map((item) => ({
          index: item.index,
          name: item.title,
          example_image_url: item.image_url,
        })),
      }));
    }
  },
  resetRefresh() {
    set(() => ({
      needRefresh: true,
    }));
  },
  async getCustomTemplate(
    page: number = 1,
    pageSize: number = SOULMATE_PAGE_SIZE
  ) {
    const resp = await apiService.getCustomTemplate(page, pageSize);
    set(() => ({
      customTemplate: {
        list: resp?.data?.bot_templates || [],
        totalCount: resp?.data?.total_count || 0,
      },
    }));
  },
  // bookmark
  async fetchBookmarkTemplate(
    page: number = 1,
    pageSize: number = SOULMATE_PAGE_SIZE
  ) {
    const resp = await apiService.getBookmarkTemplate(page, pageSize);
    set(() => ({
      bookmarkTemplate: {
        list: resp?.data?.bot_templates || [],
        totalCount: resp?.data?.total_count || 0,
      },
    }));
  },
  updateUserInfo(userEntity: UserEntity) {
    set(() => ({
      userEntity,
    }));
    localStorage.setItem(STORAGE_USER_INFO, JSON.stringify(userEntity));
  },
  setGlobalNSFWSwitch(nsfwStatus: boolean) {
    set(() => ({
      globalNSFWStatus: nsfwStatus,
    }));
  },
  logout() {
    localStorage.removeItem(STORAGE_USER_INFO);
    clearToken();
    clearSoulChatStore();
    set(() => ({
      userEntity: null,
    }));
  },
}));

export const clearUserLogout = useUserStores.getState().logout;
export const changeLoginDialogState =
  useUserStores.getState().changeLoginDialogState;

export const updateUserInfo = useUserStores.getState().updateUserInfo;
