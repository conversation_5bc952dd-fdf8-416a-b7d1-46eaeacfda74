import { PROJECT_STATUS } from "@/constant";
import { apiService } from "@/service/Api";
import { StyleItem } from "@/types/createImageType";
import { showToast } from "@/utils/toast";
import { create } from "zustand";

interface state {
  createImageState: {
    prompt: string;
    isNegativePromptOpen: boolean;
    negativePrompt: string;
    autoEnhance: boolean;
    safeMode: boolean;
    selectedStyle: StyleItem | null;
    imageCount: number;
    creativity: number;
    isShake: boolean;
  };
  timerId: NodeJS.Timeout | null;
  generateImageLoading: boolean;
  photo_list: string[];
  isShowExample: boolean;
  hasStartGenerate: boolean;
  openCommonMemberModal: boolean;
}

type Actions = {
  setCreateImageState: (newState: Partial<state["createImageState"]>) => void;
  setIsShowExample: (isShowExample: boolean) => void;
  generateImage: ({ faceModelId }: { faceModelId?: string | null }) => void;
  resetCreateState: () => void;
  setOpenCommonMemberModal: (openCommonMemberModal: boolean) => void;
};

const initialState = {
  createImageState: {
    prompt: "",
    isNegativePromptOpen: false,
    negativePrompt: "",
    autoEnhance: true,
    safeMode: true,
    selectedStyle: null,
    imageCount: 1,
    creativity: 0.5,
    isShake: false,
  },
  timerId: null,
  generateImageLoading: false,
  photo_list: [],
  isShowExample: true,
  hasStartGenerate: false,
  openCommonMemberModal: false,
};

export const useCreateImageStore = create<state & Actions>((set, get) => ({
  ...initialState,
  setOpenCommonMemberModal: (openCommonMemberModal: boolean) =>
    set({ openCommonMemberModal }),
  setCreateImageState: (newState) =>
    set((prev) => ({
      createImageState: { ...prev.createImageState, ...newState },
    })),
  setIsShowExample: (isShowExample) => set({ isShowExample }),
  generateImage: async ({ faceModelId }: { faceModelId?: string | null }) => {
    // 获取进行轮询的id
    const {
      imageCount,
      prompt,
      negativePrompt,
      creativity,
      autoEnhance,
      safeMode,
      selectedStyle,
    } = get().createImageState;
    try {
      const response = await apiService.CreateTaskV2({
        project_novel_text: prompt,
        negative_prompt: negativePrompt,
        auto_enhance: autoEnhance,
        safe_mode: safeMode,
        style: selectedStyle?.internalName,
        face_model_id: faceModelId || undefined,
        number_of_images: imageCount,
        creativity: creativity,
        type: "image",
      });
      if (response.code === 200) {
        set({
          generateImageLoading: true,
          hasStartGenerate: true,
          isShowExample: false,
        });
      } else {
        // set({ openCommonMemberModal: true });
        showToast(response.message, "error");
        return;
      }
      const project_id = response.data.id;
      if (project_id) {
        // 进行轮询调用
        const timer_id = get().timerId;
        if (timer_id) clearInterval(timer_id);

        const timerId = setInterval(async () => {
          const response = await apiService.QueryTask({ project_id });
          if (response?.data?.project_status && response?.code === 200) {
            if (response.data.project_status[1] === PROJECT_STATUS.GENERATED) {
              const photo_list = response.data.project_segments.map(
                (item) => item.image_url
              ) as string[];
              set({
                photo_list: [...photo_list, ...get().photo_list],
                generateImageLoading: false,
              });
              // 清除定时器
              clearInterval(timerId);
              return;
            }
          } else {
            showToast(`Error: Fetch TaskDetail Info Error`, "error");
          }
        }, 4000);
      }
    } catch (error) {
      showToast(`Error: Generate Image Error`, "error");
      set({ generateImageLoading: false });
    }
  },
  resetCreateState: () =>
    set({ createImageState: initialState.createImageState }),
}));
