import { apiService } from "@/service/Api";
import { create } from "zustand";

type Action = {
  fetchTemplateTags: ({ locale }: { locale: string }) => void;
};

type State = {
  templateTags: string[];
};

const initStatus = {
  templateTags: [],
};

export const useOtherStores = create<Action & State>((set, get) => ({
  ...JSON.parse(JSON.stringify(initStatus)),
  async fetchTemplateTags({ locale }: { locale: string }) {
    const res = await apiService.getTemplateTags({ locale });
    const tags = Object.keys(res?.data?.template_id_list || {}) || [];
    set({ templateTags: tags });
  },
}));
