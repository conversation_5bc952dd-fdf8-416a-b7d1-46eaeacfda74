import { apiService } from "@/service/Api";
import { CustomTag } from "@/types/createCustomChar";
import { create } from "zustand";

type State = {
  tags: CustomTag[];
};

type Actions = {
  getTags: (locale: string) => void;
};

const initialState: State = {
  tags: [],
};

export const useCreateCustomCharacter = create<State & Actions>((set) => ({
  ...initialState,
  getTags: async (locale: string) => {
    const res = await apiService.getCustomTags(locale);
    set({ tags: res.data });
  },
}));
