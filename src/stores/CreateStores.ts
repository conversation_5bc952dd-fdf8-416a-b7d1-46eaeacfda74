import { create } from "zustand";
import { FaceModel, UploadFaceCreateEntity } from "@/types/soulchat";
import { apiService } from "@/service/Api";
import { showToast } from "@/utils/toast";
import { FaceModelStatus, STORAGE_FACE_MODEL_LIST } from "@/constant";
import { firebaseLogEvent } from "@/utils/utils";
import { ACTION_FACE_EDIT_MODEL_UPLAOD } from "@/utils/event";

type Actions = {
  uploadFace: (file: File) => void;
  showHint(msg: string): void;
  queryAnalysisResult: () => void;
  fetchFaces: () => void;
  addFaceModelFiled: () => void;
  cancelFaceModel: () => void;
  setUploadFaceCreateEntity: (entity: FaceModel) => void;
};

type State = {
  showUploadingDialog: boolean;
  uploadingDialogTitle: string;
  faceModelLoopQuery: boolean;
  faces: FaceModel[];
  uploadingFaceModel: FaceModel | null;
  uploadFaceCreateEntity: Partial<UploadFaceCreateEntity> | null;
};

const initStatus: State = {
  showUploadingDialog: false,
  uploadingDialogTitle: "",
  faceModelLoopQuery: false,
  faces: [],
  uploadingFaceModel: null,
  uploadFaceCreateEntity: null,
};

export const useCreateStores = create<Actions & State>((set, get) => ({
  ...JSON.parse(JSON.stringify(initStatus)),
  uploadFace: (file: File) => {
    set(() => ({
      showUploadingDialog: true,
      uploadingDialogTitle: "Uploading...",
    }));
    apiService
      .UploadFace({
        file: file,
      })
      .then((resp) => {
        if (resp.code == 200) {
          firebaseLogEvent(ACTION_FACE_EDIT_MODEL_UPLAOD, {});

          const uploadingFaceModel = resp.data;
          if (
            uploadingFaceModel &&
            uploadingFaceModel.status &&
            uploadingFaceModel.status[0] === FaceModelStatus.uploaded
          ) {
            set(() => ({
              uploadingFaceModel: resp.data,
              uploadingDialogTitle: "Analysis in progress...",
            }));
          } else {
            showToast("Face Model Upload Failed", "error");
            set(() => ({
              showUploadingDialog: false,
            }));
          }
        } else {
          showToast("Add Face Model Failed", "error");
          set(() => ({
            showUploadingDialog: false,
          }));
        }
      })
      .catch(() => {
        showToast("Add Face Model Failed", "error");
        set(() => ({
          showUploadingDialog: false,
        }));
      });
  },
  fetchFaces: () => {
    apiService
      .GetFaces()
      .then((response) => {
        if (response.code === 200) {
          response.data.map((item, index) => {
            item.index = index;
          });
          set(() => ({
            faces: response.data,
          }));
          localStorage.setItem(
            STORAGE_FACE_MODEL_LIST,
            JSON.stringify(response.data)
          );
        }
      })
      .catch(() => {});
  },
  async queryAnalysisResult() {
    const retryCount = 0;
    const faceModel = get().uploadingFaceModel;
    if (faceModel && faceModel.id) {
      set(() => ({
        faceModelLoopQuery: true,
      }));
    }
    while (retryCount < 10) {
      const loopQuery = get().faceModelLoopQuery;
      if (!loopQuery) {
        return;
      }
      if (faceModel && faceModel.id) {
        apiService
          .QueryFaces({ face_model_id: faceModel?.id })
          .then((response) => {
            const faceModel = response.data;
            if (response.data && faceModel.status && response.code === 200) {
              if (faceModel.status[0] == FaceModelStatus.face_detected) {
                //当前face model通过检测状态
                if (faceModel.detection_result) {
                  showToast("Face Model Upload Success", "success");
                  set(() => ({
                    showUploadingDialog: false,
                    uploadingFaceModel: null,
                    faceModelLoopQuery: false,
                    uploadingDialogTitle: "Uploading...",
                  }));
                  get().fetchFaces();
                } else {
                  set(() => ({
                    showUploadingDialog: false,
                    uploadingFaceModel: null,
                    faceModelLoopQuery: false,
                    uploadingDialogTitle: "Uploading...",
                  }));
                  showToast("Face Detection Failed", "error");
                  return;
                }
                return;
              }
            }
          })
          .catch(() => {});
      }
      await new Promise((r) => setTimeout(r, 5000)); //
    }
    get().addFaceModelFiled();
  },
  addFaceModelFiled: () => {
    set(() => ({
      showUploadingDialog: false,
    }));
    showToast("Add Face Model Failed", "error");
  },
  showHint: (msg) => {
    showToast(msg, "error");
  },
  cancelFaceModel: () => {
    const uploadingFaceModel = get().uploadingFaceModel;
    if (uploadingFaceModel) {
      if (
        uploadingFaceModel &&
        uploadingFaceModel.status &&
        uploadingFaceModel.status[0] == FaceModelStatus.uploaded
      ) {
        set(() => ({
          showUploadingDialog: false,
          faceModelLoopQuery: false,
        }));
      } else {
        set(() => ({
          showUploadingDialog: false,
        }));
      }
    } else {
      set(() => ({
        showUploadingDialog: false,
      }));
    }
  },
  setUploadFaceCreateEntity: (entity: FaceModel) => {
    set(() => ({
      uploadFaceCreateEntity: {
        ...get().uploadFaceCreateEntity,
        faceModel: entity,
      },
    }));
  },
}));
