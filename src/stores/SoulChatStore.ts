import { create } from "zustand";
import { apiService, baseURL } from "../service/Api";
import {
  APP_ID_DELORIS,
  AUTO_PLAY_KEY,
  BUY_TEXT,
  MESSAGE_TYPE,
  MESSAGE_TYPE_API,
  NEED_SUB_TEXT,
  PROJECT_STATUS,
  SIGN_UP_TEXT,
  STORAGE_TOKEN,
} from "@/constant";
import { getReaderText } from "@/utils/utils";
import {
  AddSoulmateDialogDataType,
  BotTemplate,
  VoiceIdListVoice,
  Chat,
  CreateCharacterParams,
  CreateMessageRequest,
  Message,
  CreateTabEntity,
  CreateV2Request,
  InteractList,
} from "@/types";
import { showToast } from "@/utils/toast";

type State = {
  soulmateList: BotTemplate[];
  voiceIdList: VoiceIdListVoice[];
  addSoulmateDialogData: AddSoulmateDialogDataType;
  characterTimerId: NodeJS.Timeout | null;
  chat_id: string;
  createProcessLoading: boolean;
  createProcessEnd: boolean;
  historyChatList: Chat[];
  historyChatListHasMore: boolean;
  chatDetail: Partial<Message>[];
  currentChat: Partial<
    Omit<BotTemplate, "photo_array"> & { photo_array: string[] }
  > | null;
  canSendMessage: boolean;
  template_id: string | null;
  currentAudio: HTMLAudioElement | null;
  playingMessageId: string | null;
  loadingMessageId: string | null;
  isPlaying: boolean;
  playHistoryInfo: {
    id: string;
    blobUrl: string;
  }[];
  chat_timerId: NodeJS.Timeout | null;
  face_model_id: string;
  activeChatId: string | null; // 当前活跃的聊天id
  streamController: AbortController | null; // 中断流式响应的控制器
  pic_generate_loading: boolean;
  isLoadingMore: boolean;
  isCanScroll: boolean;
  isStopLoadMore: boolean;
  offset: number;
  tabs: CreateTabEntity[];
  // suggestReply
  suggestReplyInfo: {
    content: string;
    loading: boolean;
    isRequesting?: boolean;
  };
  interactList: InteractList[];
  suggestImageTemplates: {
    id: number;
    title: string;
    emoji: string;
  }[];
};

type Action = {
  setSoulmateList: (soulmateList: BotTemplate[]) => void;
  createPicID: (params: Partial<CreateV2Request>) => Promise<string>;
  createChatPic: (params: Partial<CreateV2Request>) => void;
  getPicByID: (project_id: string) => void;
  setAddSoulmateDialogData: (data: Partial<AddSoulmateDialogDataType>) => void;
  customCharacter: (params: CreateCharacterParams) => void;
  getInitData: () => void;
  getHistoryChatList: () => void;
  getChatDetail: (chat_id: string, offset?: number) => Promise<void>;
  sendMessage: (
    req: CreateMessageRequest,
    isAskPic?: boolean,
    picParams?: Partial<CreateV2Request>
  ) => void;
  handleVideoGenerate: ({
    msgId,
    video_type,
  }: {
    msgId: string;
    video_type: string;
  }) => void;
  cleatVideoChatDetail: ({ msgId }: { msgId: string }) => void;
  getGreetings: (chat_id: string) => void;
  deleteCharacter: (chat_id: string) => boolean;
  deleteChatHistory: (chat_id: string) => void;
  setHistoryChatList: (list: Chat[]) => void;
  playVoice: ({
    voice_id,
    isAudioPlaying,
    currentIsPlayingId,
    message_voice_url,
  }: {
    voice_id?: string;
    isAudioPlaying?: boolean;
    currentIsPlayingId?: string;
    message_voice_url?: string;
  }) => Promise<HTMLAudioElement>;
  playSingleMessageVoice: (chat_id: string, message_voice_url?: string) => void;
  initAudio: () => void;
  cleanUpOpt: () => void;
  handleLoadMore: (chat_id: string) => void;
  queryMaterial: (scene?: string) => void;
  querySuggestReply: (chat_id: string) => Promise<boolean>;
  changeSuggestReplyInfo: (info: Partial<State["suggestReplyInfo"]>) => void;
  fetchSuggestImageTemplate: () => Promise<void>;
  fetchInteractList: () => Promise<void>;
  handleRegenerate: (chat_id: string) => void;
  changeHistoryChatListHasMore: (hasMore: boolean) => void;
  clearStore: () => void;
  clearAllStore: () => void;
};

const initialState: State = {
  soulmateList: [], // 灵魂伴侣列表
  voiceIdList: [], // 语音ID列表
  characterTimerId: null, // 定时器id
  chat_id: "", // 聊天窗口ID
  createProcessLoading: false, // 创建角色进度loading
  createProcessEnd: false, // 创建角色进度结束
  addSoulmateDialogData: {
    // 创建灵魂伴侣的对话框数据
    selectedOtherTags: [], // 选中的其他标签
    personalityAndHobbiesTags: [], // 选中的性格和爱好标签
    create_four_pics_UrlList: [], // 创建四张图片的url列表
    create_four_pics_timerId: null, // 创建四张图片的定时器id
    soulmate_age: 18, // 创建的灵魂伴侣的年龄
    soulmate_name: "", // 创建的灵魂伴侣的名字
    selectedFace: null, // 选中的人脸图片信息
    voice_id: "", // 选中的语音ID
    group_id: "", // 选中的群组ID
  },
  historyChatList: [], // 历史人设记录列表
  historyChatListHasMore: true, // 历史人设记录列表是否还有更多
  chatDetail: [], // 聊天详情
  currentChat: null, // 当前聊天
  canSendMessage: true, // 是否可以发送消息
  template_id: null, // 当前模板id
  currentAudio: null,
  playingMessageId: null,
  loadingMessageId: null,
  playHistoryInfo: [],
  isPlaying: false,
  chat_timerId: null,
  face_model_id: "",
  activeChatId: null, // 当前活跃的聊天ID
  streamController: null, // 流式响应控制器
  pic_generate_loading: false,
  isLoadingMore: false,
  isCanScroll: true,
  isStopLoadMore: false,
  offset: 0,
  tabs: [],
  suggestReplyInfo: {
    content: "",
    loading: false,
    isRequesting: false,
  },
  interactList: [],
  suggestImageTemplates: [],
};

export const useSoulChatStore = create<State & Action>()((set, get) => ({
  ...JSON.parse(JSON.stringify(initialState)),
  setAddSoulmateDialogData: (data: Partial<AddSoulmateDialogDataType>) => {
    set({
      addSoulmateDialogData: {
        ...get().addSoulmateDialogData,
        ...data,
      },
    });
  },
  handleLoadMore: (chat_id: string) => {
    const { chatDetail, getChatDetail, isLoadingMore, isStopLoadMore, offset } =
      get();
    if (chatDetail.length >= 20 && !isLoadingMore && !isStopLoadMore) {
      set({
        isLoadingMore: true,
        isCanScroll: false,
        offset: offset + 20,
      });
      getChatDetail(chat_id, offset + 20);
    }
  },
  setSoulmateList: (list: BotTemplate[]) => {
    set({
      soulmateList: list,
    });
  },
  createPicID: (params: Partial<CreateV2Request>) => {
    return new Promise((resolve, reject) => {
      apiService.CreateTaskV2(params).then((response) => {
        if (response.code === 200) {
          if (response.data) {
            resolve(response.data.id);
            set({
              face_model_id: response.data?.project_config?.face_model_id || "",
            });
          }
        } else {
          showToast(`${response.message}`, "error");
          reject(response.message);
        }
      });
    });
  },
  getPicByID: async (project_id: string) => {
    const addSoulmateDialogData = get().addSoulmateDialogData;
    const timer_id = addSoulmateDialogData.create_four_pics_timerId;
    if (timer_id) clearInterval(timer_id);

    const timerId = setInterval(async () => {
      const response = await apiService.QueryTask({ project_id });
      if (response?.data?.project_status && response?.code === 200) {
        if (response.data.project_status[1] === PROJECT_STATUS.GENERATED) {
          const photo_array = response.data.project_segments.map(
            (item) => item.image_url
          ) as string[];
          const currentChat = get().currentChat;
          set({
            addSoulmateDialogData: {
              ...addSoulmateDialogData,
              create_four_pics_UrlList: photo_array,
              create_four_pics_timerId: null,
            },
            currentChat: currentChat
              ? {
                  ...currentChat,
                  photo_array: photo_array || [],
                }
              : {
                  photo_array: photo_array || [],
                },
            pic_generate_loading: false,
          });
          // 清除定时器
          clearInterval(timerId);
          // 图片生成后调用修改模板api
          const template_id = get().template_id;
          if (template_id) {
            apiService.editCharacter({
              template_id,
              photo_array: JSON.stringify(photo_array),
            });
          }
          return;
        }
      } else {
        showToast(`Error: Fetch TaskDetail Info Error`, "error");
      }
    }, 4000);
    set({
      addSoulmateDialogData: {
        ...addSoulmateDialogData,
        create_four_pics_timerId: timerId,
      },
      pic_generate_loading: true,
    });
  },
  getInitData: async () => {
    const resp = await Promise.all([
      apiService.getVoiceIdList(),
      apiService.getGroups(),
    ]);
    set({
      voiceIdList: resp[0]?.data?.voices || [],
      addSoulmateDialogData: {
        ...get().addSoulmateDialogData,
        voice_id: resp[0]?.data?.voices?.[0]?.id || "",
        group_id:
          resp[1]?.data?.find((item) => item.title === APP_ID_DELORIS)?.id ||
          "",
      },
    });
  },
  customCharacter: async (params: CreateCharacterParams) => {
    set({
      createProcessLoading: true,
    });
    // 1. 调用创建角色api获取message_id和id
    const resp = await apiService.createCharacter(params);
    if (resp.code === 200 && resp.data.message_id) {
      set({
        template_id: resp.data.id,
      });
      // 2. 调用创建聊天窗口api获取chat_id
      apiService.createChat({ bot_template: resp.data.id }).then((res) => {
        if (res.code === 200 && res.data.id) {
          set({
            chat_id: res.data.id,
          });
        }
      });
      // 2并行. 调用get_seo_article api获取人设描述和人设prompt
      const characterTimerId = get().characterTimerId;
      if (characterTimerId) clearInterval(characterTimerId);

      const timerId = setInterval(async () => {
        const resp2 = await apiService.queryCharacterDescriptionAndPrompt({
          message_id: resp.data.message_id,
        });
        if (resp2.data.content) {
          clearInterval(timerId);
          const cont = resp2.data.content.split("\n\n");
          set({
            characterTimerId: null,
          });
          // 3. 获取到人设描述和人设prompt后，调用编辑角色api，传入description、sys_prompt、template_id
          await apiService.editCharacter({
            description: cont[0],
            sys_prompt: cont[1],
            template_id: resp.data.id,
          });
          set({
            createProcessLoading: false,
            createProcessEnd: true,
          });
          get().getHistoryChatList();
        }
      }, 2000);

      set({
        characterTimerId: timerId,
      });
    }
  },
  getHistoryChatList: async () => {
    const { historyChatList, historyChatListHasMore } = get();
    if (!historyChatListHasMore) return;

    let last_seen_create_time = "";
    if (historyChatList.length > 0) {
      last_seen_create_time =
        historyChatList[historyChatList.length - 1].create_time;
    }

    const resp = await apiService.queryHistoryChatList({
      last_seen_create_time,
    });

    set((state) => ({
      historyChatList: state.historyChatList?.concat(resp?.data?.chats || []),
      historyChatListHasMore: resp?.data?.has_more,
    }));
  },
  getChatDetail: async (chat_id: string, offset?: number) => {
    // 中断之前的流式响应
    const { streamController, currentAudio } = get();
    if (streamController) {
      streamController.abort();
    }

    // 清理音频状态
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0; // 重置播放位置
    }

    // 设置当前活跃的聊天ID并清理音频相关状态
    set({
      activeChatId: chat_id,
      streamController: null,
      playingMessageId: null,
      loadingMessageId: null,
      isPlaying: false,
    });

    const resp = await apiService.queryChatDetail(chat_id, offset);
    const bot_template = resp?.data?.bot_template;
    let photo_array = [];
    try {
      photo_array = bot_template?.photo_array
        ? JSON.parse(bot_template.photo_array)
        : [];
    } catch {}
    let messages = [];
    const resp_messages = resp?.data?.messages?.messages?.reverse();
    if (offset) {
      messages = resp_messages.concat(get().chatDetail as Message[]) || [];
    } else {
      messages = resp_messages || [];
      set({
        isCanScroll: true,
      });
    }
    set({
      chatDetail: messages,
      currentChat: {
        ...bot_template,
        photo_array,
        chat_id: resp?.data?.chat_id,
      },
      isLoadingMore: false,
      isStopLoadMore: resp_messages?.length < 20,
    });
  },
  sendMessage: async (
    req: CreateMessageRequest,
    isAskPic: boolean = false,
    picParams: Partial<CreateV2Request>
  ) => {
    // 每次发送消息后，切换suggestReplyInfo的loading状态
    set({
      suggestReplyInfo: {
        ...get().suggestReplyInfo,
        loading: false,
      },
    });
    const currentAudio = get().currentAudio;
    if (currentAudio) {
      currentAudio.muted = false;
    }

    // 创建新的 AbortController 用于这次请求
    const controller = new AbortController();
    const currentChatId = req.chat;

    // 中断之前的流式响应
    const { streamController } = get();
    if (streamController) {
      streamController.abort();
    }

    // 设置新的控制器和活跃聊天ID
    set({
      streamController: controller,
      activeChatId: currentChatId,
    });

    const chatDetail = get().chatDetail;
    const type = isAskPic
      ? "image"
      : req.message_type === MESSAGE_TYPE_API.shortcuts_mind_reading
      ? MESSAGE_TYPE.mind_reading
      : req.message_type === MESSAGE_TYPE_API.shortcuts_video
      ? MESSAGE_TYPE.video
      : "";
    const newChatDetail = [
      ...chatDetail,
      ...(req.is_hide_user_message
        ? []
        : [
            {
              id: Date.now().toString() + Math.random(),
              content: req.content,
              is_from_bot: false,
            },
          ]),
      {
        id: Date.now().toString() + Math.random(),
        content: "",
        is_from_bot: true,
        loading: true,
        type,
      },
    ];
    set({
      chatDetail: newChatDetail,
      canSendMessage: false,
      isCanScroll: true,
    });
    if (isAskPic) {
      get().createChatPic(picParams);
      return;
    }
    try {
      const { is_hide_user_message, video_type = "", ...rest } = req;
      const params = {
        ...rest,
        save_user_message: !is_hide_user_message,
      };
      const response = await fetch(`${baseURL}/v1/chats/message`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem(STORAGE_TOKEN)}`,
        },
        body: JSON.stringify(params),
        signal: controller.signal, // 添加 AbortController 信号
      });

      if (!response.ok || !response.body) {
        throw new Error("Network response was not ok");
      }

      const reader = response.body
        ?.pipeThrough(new TextDecoderStream())
        .getReader();
      let accumulatedMessage = "";
      let id;
      while (reader !== null) {
        const { done, value } = await reader.read();
        if (done) {
          // 检查当前聊天是否仍然活跃
          if (get().activeChatId === currentChatId) {
            if (![MESSAGE_TYPE.video].includes(type)) {
              get().playVoice({
                isAudioPlaying: localStorage.getItem(AUTO_PLAY_KEY) === "true",
                currentIsPlayingId: id,
              });
            }
            set({
              canSendMessage: true,
              streamController: null, // 清除控制器
            });
          }
          break;
        }
        if (value) {
          const { matchStr, msgId, type, status } = getReaderText(value);
          if (
            matchStr ||
            req.message_type === MESSAGE_TYPE_API.shortcuts_mind_reading ||
            req.message_type === MESSAGE_TYPE_API.shortcuts_video
          ) {
            // 检查当前聊天是否仍然活跃，如果不是则忽略这个响应
            if (get().activeChatId !== currentChatId) {
              continue;
            }

            const chatDetail = get().chatDetail;
            id = msgId || Date.now().toString() + Math.random();
            accumulatedMessage += matchStr;
            if (type === MESSAGE_TYPE.mind_reading && status !== "finished") {
              continue;
            }

            if (type === MESSAGE_TYPE.video && status === "finished") {
              get().handleVideoGenerate({ msgId, video_type });
              return;
            }

            chatDetail.pop();
            const is_need_subscribe =
              accumulatedMessage.includes(NEED_SUB_TEXT) ||
              accumulatedMessage.includes(BUY_TEXT);
            const is_need_signup = accumulatedMessage.includes(SIGN_UP_TEXT);
            if (is_need_subscribe || is_need_signup) {
              set({
                chatDetail: [
                  ...chatDetail,
                  {
                    id,
                    content: accumulatedMessage,
                    is_from_bot: true,
                    loading: false,
                    is_need_subscribe,
                    is_need_signup,
                  },
                ],
                canSendMessage: true,
                streamController: null, // 清除控制器
              });
              return;
            }
            set({
              chatDetail: [
                ...chatDetail,
                {
                  id,
                  content: accumulatedMessage,
                  is_from_bot: true,
                  type,
                },
              ],
            });
          }
        }
      }
    } catch (error) {
      // 如果是 AbortError，说明请求被中断，不需要处理
      if (error instanceof Error && error.name === "AbortError") {
        return;
      }

      // 只有当前聊天仍然活跃时才更新状态
      if (get().activeChatId === currentChatId) {
        set({
          canSendMessage: true,
          streamController: null,
        });
      }
    }
  },
  handleVideoGenerate: async ({
    msgId,
    video_type,
  }: {
    msgId: string;
    video_type: string;
  }) => {
    const { currentChat } = get() || {};
    const currentChatId = currentChat?.chat_id;

    if (msgId) {
      try {
        const res = await apiService.imageToVideoInChat({
          template_id: currentChat?.id,
          image_url: currentChat?.icon,
          video_type,
          message_id: msgId,
        });
        if (res.data.video_url) {
          // 检查当前聊天是否仍然活跃
          if (get().activeChatId === currentChatId) {
            const chatDetail = get().chatDetail;
            chatDetail.pop();
            set({
              chatDetail: [
                ...chatDetail,
                {
                  id: msgId,
                  content: `{"video_url": "${res.data.video_url}","status": {"1":"generated"}}`,
                  is_from_bot: true,
                  type: MESSAGE_TYPE.video,
                },
              ],
              canSendMessage: true,
            });
          }
        } else {
          // 轮询接口
          const pollVideo = async () => {
            const pollInterval = setInterval(async () => {
              try {
                // 检查当前聊天是否仍然活跃，如果不是则停止轮询
                if (get().activeChatId !== currentChatId) {
                  clearInterval(pollInterval);
                  return;
                }

                const pollRes = await apiService.pollGenerateVideoInChat(
                  res.data.template_video_id
                );
                if (pollRes.data.status === "error") {
                  clearInterval(pollInterval);
                  // 再次检查当前聊天是否仍然活跃
                  if (get().activeChatId === currentChatId) {
                    get().cleatVideoChatDetail({ msgId });
                    showToast(`Error: generate video failed`, "error");
                  }
                  return;
                }
                if (
                  pollRes.data.status === "finish" &&
                  pollRes.data.video_url
                ) {
                  clearInterval(pollInterval);
                  // 检查当前聊天是否仍然活跃
                  if (get().activeChatId === currentChatId) {
                    const chatDetail = get().chatDetail;
                    chatDetail.pop();
                    set({
                      chatDetail: [
                        ...chatDetail,
                        {
                          id: msgId,
                          content: `{"status": {"1":"generated"},"video_url": "${pollRes.data.video_url}"}`,
                          is_from_bot: true,
                          type: MESSAGE_TYPE.video,
                        },
                      ],
                      canSendMessage: true,
                    });
                  }
                }
              } catch (error) {
                // 检查当前聊天是否仍然活跃
                if (get().activeChatId === currentChatId) {
                  get().cleatVideoChatDetail({
                    msgId,
                  });
                }
              }
            }, 6000);
          };
          pollVideo();
        }
      } catch (error) {
        // 检查当前聊天是否仍然活跃
        if (get().activeChatId === currentChatId) {
          get().cleatVideoChatDetail({
            msgId,
          });
        }
      }
    }
  },
  cleatVideoChatDetail: ({ msgId }: { msgId: string }) => {
    const chatDetail = get().chatDetail;
    chatDetail.pop();
    set({
      canSendMessage: true,
      chatDetail: [
        ...chatDetail,
        {
          id: msgId,
          content: "",
          is_from_bot: true,
          type: MESSAGE_TYPE.video_error,
        },
      ],
    });
  },
  getGreetings: async (chat_id: string) => {
    const currentAudio = get().currentAudio;
    if (currentAudio) {
      currentAudio.muted = false;
    }

    // 创建新的 AbortController 用于这次请求
    const controller = new AbortController();

    // 中断之前的流式响应
    const { streamController } = get();
    if (streamController) {
      streamController.abort();
    }

    // 设置新的控制器和活跃聊天ID
    set({
      streamController: controller,
      activeChatId: chat_id,
    });

    try {
      set({
        chatDetail: [
          {
            id: Date.now().toString(),
            content: "",
            is_from_bot: true,
            loading: true,
          },
        ],
        canSendMessage: false,
      });
      const response = await fetch(
        `${baseURL}/v1/chats/welcome_message?chat_id=${chat_id}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem(STORAGE_TOKEN)}`,
          },
          signal: controller.signal, // 添加 AbortController 信号
        }
      );

      if (!response.ok || !response.body) {
        throw new Error("Network response was not ok");
      }

      const reader = response.body
        ?.pipeThrough(new TextDecoderStream())
        .getReader();
      let greeting_msg = "";
      let id;
      while (reader !== null) {
        const { done, value } = await reader.read();
        if (done) {
          // 检查当前聊天是否仍然活跃
          if (get().activeChatId === chat_id) {
            get().getHistoryChatList();
            get().playVoice({
              isAudioPlaying: localStorage.getItem(AUTO_PLAY_KEY) === "true",
              currentIsPlayingId: id,
            });
            set({
              canSendMessage: true,
              streamController: null, // 清除控制器
            });
          }
          break;
        }
        if (value) {
          const { matchStr, msgId } = getReaderText(value);
          id = msgId || Date.now().toString() + Math.random();
          greeting_msg += matchStr;
          if (greeting_msg) {
            // 检查当前聊天是否仍然活跃，如果不是则忽略这个响应
            if (get().activeChatId === chat_id) {
              set({
                chatDetail: [
                  {
                    id,
                    content: greeting_msg,
                    is_from_bot: true,
                  },
                ],
              });
            }
          }
        }
      }
    } catch (error) {
      // 如果是 AbortError，说明请求被中断，不需要处理
      if (error instanceof Error && error.name === "AbortError") {
        return;
      }

      // 只有当前聊天仍然活跃时才更新状态
      if (get().activeChatId === chat_id) {
        set({
          canSendMessage: true,
          streamController: null,
        });
      }
    }
  },
  // 初始化音频
  initAudio: () => {
    const audio = new Audio();
    audio.muted = true;
    audio.addEventListener("play", () => {
      set({ isPlaying: true, loadingMessageId: null });
    });
    audio.addEventListener("ended", () => {
      set({ playingMessageId: null, isPlaying: false });
    });
    audio.addEventListener("error", (e) => {
      const audio = e.target as HTMLAudioElement;

      switch (audio.error?.code) {
        case MediaError.MEDIA_ERR_ABORTED: // 1
          break;
        case MediaError.MEDIA_ERR_NETWORK: // 2
          break;
        case MediaError.MEDIA_ERR_DECODE: // 3
          break;
        case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED: // 4
          break;
      }

      set({
        loadingMessageId: null,
        playingMessageId: null,
        isPlaying: false,
      });
    });
    set({ currentAudio: audio });
  },
  // 语音播放
  playVoice: async ({
    voice_id,
    isAudioPlaying = false,
    currentIsPlayingId,
    message_voice_url,
  }: {
    voice_id?: string;
    isAudioPlaying?: boolean;
    currentIsPlayingId?: string;
    message_voice_url?: string;
  }) => {
    const voiceId = voice_id || get().currentChat?.voice_id || "";
    if (!isAudioPlaying) return;

    // 记录当前聊天ID，用于后续验证
    const currentChatId = get().activeChatId;
    const currentBotTemplateId = get().currentChat?.id;

    set({
      loadingMessageId: currentIsPlayingId,
      playingMessageId: currentIsPlayingId || "",
    });

    const currentAudio = get().currentAudio;

    if (currentAudio) {
      currentAudio.pause();
      // 先从历史记录中获取blobUrl
      const srcUrl =
        get().playHistoryInfo.find((item) => item.id === currentIsPlayingId)
          ?.blobUrl ||
        message_voice_url ||
        "";
      if (srcUrl) {
        // 验证当前聊天是否仍然活跃
        if (get().activeChatId === currentChatId) {
          currentAudio.src = srcUrl;
          try {
            currentAudio.play();
          } catch {}
        }
        return;
      }

      try {
        const blobUrl = await apiService.getBlobSrc({
          voice_id: voiceId,
          message_id: currentIsPlayingId || "",
          bot_template_id: currentBotTemplateId || "",
        });

        // 验证当前聊天是否仍然活跃，如果不是则不播放
        if (get().activeChatId !== currentChatId) {
          // 清理加载状态
          set({
            loadingMessageId: null,
          });
          return;
        }

        currentAudio.src = blobUrl; // 设置音频的源为 Blob URL
        set({
          playHistoryInfo: [
            ...get().playHistoryInfo,
            {
              id: currentIsPlayingId || "",
              blobUrl,
            },
          ],
        });
        try {
          currentAudio.play();
        } catch {}
      } catch (error) {
        // 音频加载失败时清理状态
        if (get().activeChatId === currentChatId) {
          set({
            loadingMessageId: null,
            playingMessageId: null,
            isPlaying: false,
          });
        }
      }
    }
  },
  // 点击单条播放
  playSingleMessageVoice: async (
    chat_id: string,
    message_voice_url?: string
  ) => {
    const { playingMessageId, currentAudio, activeChatId } = get();
    if (currentAudio) {
      currentAudio.muted = false;
    }

    // 检查当前是否有活跃聊天
    if (!activeChatId) {
      return;
    }

    // 如果点击的是当前正在播放的消息
    if (playingMessageId === chat_id && currentAudio) {
      if (!currentAudio?.paused) {
        // 暂停时记住当前播放位置
        currentAudio?.pause();
        set({ isPlaying: false });
      } else {
        // 从暂停的位置继续播放
        try {
          currentAudio.play();
        } catch {}
        set({ isPlaying: true });
      }
      return;
    }

    get().playVoice({
      isAudioPlaying: true,
      currentIsPlayingId: chat_id,
      message_voice_url,
    });
  },
  deleteChatHistory: async (chat_id: string) => {
    const resp = await apiService.deleteChatHistory(chat_id);
    if (resp.code === 200) {
      get().getChatDetail(chat_id);
      const currentAudio = get().currentAudio;
      if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0; // 重置播放位置
        currentAudio.muted = true;
      }
    }
  },
  setHistoryChatList: (list: Chat[]) => {
    set({
      historyChatList: list,
    });
  },
  deleteCharacter: async (chat_id: string) => {
    const resp = await apiService.deleteCharacter({ chat_id });
    if (resp.code === 200) {
      get().getHistoryChatList();
      return true;
    }
    return false;
  },
  cleanUpOpt: () => {
    const currentAudio = get().currentAudio;
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0; // 重置播放位置
    }
    set({
      face_model_id: "",
      createProcessEnd: false,
      offset: 0,
      isLoadingMore: false,
      isStopLoadMore: false,
      isCanScroll: true,
      loadingMessageId: null,
      playingMessageId: null,
      isPlaying: false,
      canSendMessage: true,
      suggestReplyInfo: {
        content: "",
        loading: false,
        isRequesting: false,
      },
    });
  },
  // 聊天窗口生成图片
  createChatPic: async (params: Partial<CreateV2Request>) => {
    const currentChatId = params?.chat_id;

    try {
      const pic_id = await get().createPicID(params);
      const chat_timerId = get().chat_timerId;
      if (chat_timerId) clearInterval(chat_timerId);

      const timerId = setInterval(async () => {
        // 检查当前聊天是否仍然活跃，如果不是则停止轮询
        if (get().activeChatId !== currentChatId) {
          clearInterval(timerId);
          return;
        }

        const response = await apiService.QueryTask({ project_id: pic_id });
        if (response?.data?.project_status && response?.code === 200) {
          if (response.data.project_status[1] === PROJECT_STATUS.GENERATED) {
            const photos = response.data.project_segments.map(
              (item) => item.image_url
            ) as string[];

            // 再次检查当前聊天是否仍然活跃
            if (get().activeChatId === currentChatId) {
              const chatDetail = get().chatDetail;
              chatDetail.pop();
              // // 调用接口保存本次生成的图片
              // apiService.saveChatHistory({
              //   content: photos[0],
              //   chat: params?.project_config?.chat_id || "",
              //   is_from_bot: true,
              // })
              set({
                chatDetail: [
                  ...chatDetail,
                  {
                    id: Date.now().toString() + Math.random(),
                    content: photos[0],
                    is_from_bot: true,
                    type: "image",
                  },
                ],
                canSendMessage: true,
              });
            }
            // 清除定时器
            clearInterval(timerId);
          }
        } else {
          // 只有当前聊天仍然活跃时才更新状态
          if (get().activeChatId === currentChatId) {
            set({
              canSendMessage: true,
            });
            showToast(`Error: Fetch TaskDetail Info Error`, "error");
          }
        }
      }, 2000);

      set({
        chat_timerId: timerId,
      });
    } catch {
      // 只有当前聊天仍然活跃时才更新状态
      if (get().activeChatId === currentChatId) {
        set({
          canSendMessage: true,
        });
      }
    }
  },
  queryMaterial: async (scene?: string) => {
    const materialReq = { scene };
    apiService.QueryMaterial(materialReq).then((response) => {
      if (response.code === 200) {
        set(() => ({
          tabs: response.data.tabs,
        }));
      }
    });
  },
  // 获取SuggestReply
  querySuggestReply: async (chat_id: string) => {
    if (!chat_id) return;

    // 如果已经在请求中，直接返回
    const { isRequesting } = get().suggestReplyInfo;
    if (isRequesting) return;

    // 设置请求状态为 true
    set((state) => ({
      suggestReplyInfo: {
        ...state.suggestReplyInfo,
        isRequesting: true,
      },
    }));
    const { suggestReplyInfo } = get();

    try {
      const resp = await apiService.getSuggestReply({ chat_id });
      if (resp.code === 200) {
        if (!resp.data.error_code) {
          set({
            suggestReplyInfo: {
              content: resp.data.reply,
              loading: suggestReplyInfo.loading,
              isRequesting: false,
            },
          });
          return true;
        }
      }
      return false;
    } catch (error) {
      // 请求失败时也要重置状态
      set((state) => ({
        suggestReplyInfo: {
          ...state.suggestReplyInfo,
          loading: suggestReplyInfo.loading,
          isRequesting: false,
        },
      }));
      return false;
    }
  },
  // 修改suggestReplyInfo
  changeSuggestReplyInfo: (info: Partial<State["suggestReplyInfo"]>) => {
    set((state) => ({
      suggestReplyInfo: {
        ...state.suggestReplyInfo,
        ...info,
      },
    }));
  },
  async fetchSuggestImageTemplate() {
    const res = await apiService.getSuggestImageTemplate();
    if (res.code === 200) {
      set({
        suggestImageTemplates: res.data.templates,
      });
    }
  },
  async fetchInteractList() {
    const res = await apiService.getInteractList();
    if (res.code === 200) {
      set({
        interactList: res.data.interact_list,
      });
    }
  },
  // 处理重新生成逻辑
  async handleRegenerate(chat_id: string) {
    if (!chat_id) return;

    const chatDetail = get().chatDetail;
    const lastMessage = chatDetail.slice(-1);
    if (lastMessage[0].type === "image") {
      showToast("Cannot regenerate image", "error");
      return;
    }
    const newChatDetail = chatDetail.slice(0, -1);
    set({
      chatDetail: [...newChatDetail],
    });
    const resp = await apiService.deleteLastMessages({
      chat_id,
      message_id: lastMessage[0].id || "",
    });
    if (resp.code === 200) {
      get().sendMessage({
        content:
          chatDetail.findLast((chat) => !chat.is_from_bot)?.content || "",
        chat: chat_id,
        is_hide_user_message: true,
      });
    }
  },
  changeHistoryChatListHasMore: (hasMore: boolean) => {
    set({
      historyChatListHasMore: hasMore,
    });
  },
  clearStore: () => {
    const currentAudio = get().currentAudio;
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0; // 重置播放位置
    }

    // 中断正在进行的流式响应
    const { streamController } = get();
    if (streamController) {
      streamController.abort();
    }

    set((state) => ({
      ...initialState,
      historyChatList: state.historyChatList,
      historyChatListHasMore: state.historyChatListHasMore,
    }));
  },
  clearAllStore: () => {
    const currentAudio = get().currentAudio;
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0; // 重置播放位置
    }

    // 中断正在进行的流式响应
    const { streamController } = get();
    if (streamController) {
      streamController.abort();
    }

    set(initialState);
  },
}));

export const clearSoulChatStore = useSoulChatStore.getState().clearStore;

export const clearAllSoulChatStore = useSoulChatStore.getState().clearAllStore;
