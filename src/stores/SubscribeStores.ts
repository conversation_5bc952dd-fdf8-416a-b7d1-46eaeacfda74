import { create } from "zustand";
import { apiService } from "../service/Api";

import {
  CreatePaymentRequest,
  CreatePaymentResponse,
  GetProductResponse,
  GetProductResponseForLoginUser,
  ProductEntity,
  SubscriptionChannel,
} from "@/types";
import { showToast } from "@/utils/toast";
import { APP_ID_DELORIS, SUBSCRIPTION_TYPE } from "@/constant";
import { updateUserInfo } from "./UserStores";

type Action = {
  selectProduct: (product: ProductEntity) => void;
  createPayment: (origin: string, locale: string, isUpgrade?: boolean) => void;
  clearPaymentStatus: () => void;
  changeProductLoading: (loading: boolean) => void;
  fetchProducts: (
    subscription_type: (typeof SUBSCRIPTION_TYPE)[keyof typeof SUBSCRIPTION_TYPE]
  ) => void;
  fetchProductsForLoginUser: (
    subscription_type: (typeof SUBSCRIPTION_TYPE)[keyof typeof SUBSCRIPTION_TYPE]
  ) => void;
  resetRefresh: () => void;
};
type State = {
  products: ProductEntity[];
  selectProductId: string;
  needRefreshProduct: boolean;
  paymentStatus: "idle" | "creating" | "success" | "failed";
  fetchStatus: "idle" | "fetching" | "success" | "failed";
  createResp: CreatePaymentResponse | undefined;
  memberUpgrading: boolean;
  productLoading: boolean;
};

const initStatus: State = {
  products: [],
  selectProductId: "1",
  paymentStatus: "idle",
  fetchStatus: "idle",
  createResp: undefined,
  needRefreshProduct: true,
  memberUpgrading: false,
  productLoading: false,
};

export const useSubscribeStores = create<Action & State>((set, get) => ({
  ...JSON.parse(JSON.stringify(initStatus)),
  selectProduct(product) {
    set(() => ({
      selectProductId: product.id,
    }));
  },
  clearPaymentStatus() {
    set(() => ({
      createResp: undefined,
      paymentStatus: "idle",
    }));
  },
  createPayment(origin: string, locale: string, isUpgrade?: boolean) {
    const selectProductId = get().selectProductId;
    const req: CreatePaymentRequest = {
      product_id: selectProductId,
      channel: SubscriptionChannel.Stripe,
      success_url: `${origin}/${locale}/payment-result?result=success`,
      failed_url: `${origin}/${locale}/payment-result?result=failed`,
      app: APP_ID_DELORIS,
    };
    set(() => ({
      paymentStatus: "creating",
    }));
    apiService
      .CreatePayment(req)
      .then((resp: CreatePaymentResponse) => {
        if (resp && resp.code == 200) {
          set(() => ({
            createResp: resp,
            paymentStatus: "success",
          }));
          if (isUpgrade) {
            let attempts = 1;
            const maxAttempts = 5;
            set(() => ({
              memberUpgrading: true,
            }));
            const checkUpgradeStatus = () => {
              const delay = (attempts + 1) * 1000;
              setTimeout(() => {
                apiService.Login().then((resp) => {
                  if (resp && resp.code == 200) {
                    if (resp.data.entitlement?.member_level?.[0] === 2) {
                      showToast(
                        "Your Subscription Has Been Upgraded! 🎉You can now enjoy Deluxe.",
                        "success"
                      );
                      updateUserInfo(resp.data);
                      set(() => ({
                        memberUpgrading: false,
                      }));
                      return;
                    } else if (attempts < maxAttempts) {
                      attempts++;
                      checkUpgradeStatus();
                    } else {
                      set(() => ({
                        memberUpgrading: false,
                      }));
                      showToast(
                        "Subscription Upgrade Failed – Please Update Your Payment Information.Please check your email for more details on why the payment failed.",
                        "error"
                      );
                    }
                  }
                });
              }, delay);
            };

            checkUpgradeStatus();
          }
        } else {
          showToast(`Create Subscription Failed: ${resp.message}`, "error");
        }
      })
      .catch(() => {
        showToast("Create Subscription Failed", "error");
        set(() => ({
          paymentStatus: "idle",
        }));
      });
  },
  changeProductLoading(loading: boolean) {
    set(() => ({
      productLoading: loading,
    }));
  },
  fetchProducts(
    subscription_type: (typeof SUBSCRIPTION_TYPE)[keyof typeof SUBSCRIPTION_TYPE]
  ) {
    apiService
      .GetProducts(subscription_type)
      .then((resp: GetProductResponse) => {
        if (resp && resp.code == 200) {
          const products = resp.data;
          const selectProductId = get().selectProductId;
          const selectInProducts = products.some(
            (item) => item.id === selectProductId
          );
          set(() => ({
            selectProductId: selectInProducts
              ? selectProductId
              : products[0].id,
            products,
            needRefreshProduct: false,
          }));
        }
      })
      .catch(() => {})
      .finally(() => {
        set(() => ({
          productLoading: false,
        }));
      });
  },

  fetchProductsForLoginUser(
    subscription_type: (typeof SUBSCRIPTION_TYPE)[keyof typeof SUBSCRIPTION_TYPE]
  ) {
    apiService
      .getEhentaiProductsForLoginUser(subscription_type)
      .then((resp: GetProductResponseForLoginUser) => {
        if (resp && resp.code == 200) {
          const products = resp.data.products;
          const selectProductId = get().selectProductId;
          const selectInProducts = products.some(
            (item) => item.id === selectProductId
          );
          if (resp.data.is_upgrade) {
            products.forEach((product) => {
              apiService.getPreviewProration(product.id).then((resp) => {
                if (resp && resp.code == 200) {
                  product.ProratedCharge = resp.data.price_difference;
                  set(() => ({
                    selectProductId: selectInProducts
                      ? selectProductId
                      : products[0].id,
                    products,
                    needRefreshProduct: false,
                  }));
                }
              });
            });
          } else {
            set(() => ({
              selectProductId: selectInProducts
                ? selectProductId
                : products?.[0]?.id,
              products,
              needRefreshProduct: false,
            }));
          }
        }
      })
      .catch(() => {})
      .finally(() => {
        set(() => ({
          productLoading: false,
        }));
      });
  },
  resetRefresh() {
    set(() => ({
      needRefreshProduct: true,
      paymentStatus: "idle",
      products: [],
    }));
  },
}));
