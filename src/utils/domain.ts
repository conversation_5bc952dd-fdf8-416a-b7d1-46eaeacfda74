import { DOMAIN } from "@/constant";

export const getDomainTitle = (host: string | null): string => {
  if (!host) return "SweetAI";

  switch (host) {
    case DOMAIN.EHE:
      return "<PERSON><PERSON><PERSON>";
    case DOMAIN.NSFWCHAT:
      return "NSFWChat";
    case DOMAIN.PERCHANCEAI:
      return "PerchanceA<PERSON>";
    case DOMAIN.SWEETAI:
      return "SweetAI";
    default:
      return "SweetAI";
  }
};
