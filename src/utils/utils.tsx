"use client";
import { logEvent } from "firebase/analytics";
import { analytics } from "@/firebaseConfig";
import ReactMarkdown from "react-markdown";
import { SOURCE_ID_KEY } from "@/constant";
import JSZip from "jszip";
import { saveAs } from "file-saver";
import { showToast } from "./toast";

export const getReaderText = (str: string) => {
  let matchStr = "";
  let msgId = "";
  let type = "";
  let status = "";
  try {
    const result = str.match(/data:\s*({.*?})\s*\n/g);
    result?.forEach((_: string) => {
      const matchStrItem = _.match(/data:\s*({.*?})\s*\n/)?.[1];
      const data = matchStrItem ? JSON.parse(matchStrItem) : "";
      matchStr += data?.chunk || "";
      msgId = data?.message_id || "";
      type = data?.type || "";
      status = data?.status || "";
    });
  } catch {}
  return {
    matchStr,
    msgId,
    type,
    status,
  };
};

export const firebaseLogEvent = (
  event: string,
  options?: Record<string, any>
) => {
  const sourceId = localStorage.getItem(SOURCE_ID_KEY);
  if (analytics) {
    logEvent(
      analytics,
      event,
      options
        ? options
        : {
            page_path: event,
            source_id: sourceId,
          }
    );
  }
};

// 添加重试函数
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;

      if (i < maxRetries - 1) {
        await new Promise((resolve) => setTimeout(resolve, delay * (i + 1))); // 指数退避
      }
    }
  }

  throw lastError!;
}

export function MarkdownRenderer({ content }: { content: string }) {
  return <ReactMarkdown>{content}</ReactMarkdown>;
}

export const convertDate = (dateTimeStr: string) => {
  const date = new Date(dateTimeStr);
  const yearMonthDay =
    date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();
  return yearMonthDay;
};

export const saveUtmParams = () => {
  const urlParams = new URLSearchParams(window.location.search);

  const ref = urlParams.get("ref") || "";
  const utm_source =
    urlParams.get("utm_source") ||
    urlParams.get("sourceId") ||
    document.referrer ||
    "";
  const utm_medium = urlParams.get("utm_medium") || "";

  localStorage.setItem("ref", ref);
  localStorage.setItem("utm_source", utm_source);
  localStorage.setItem("utm_medium", utm_medium);
};

export const getUtmParams = () => {
  return {
    ref: localStorage.getItem("ref") || "",
    utm_source: localStorage.getItem("utm_source") || "",
    utm_medium: localStorage.getItem("utm_medium") || "",
  };
};

export const uuidToBase64 = (uuid: string) => {
  // 去掉连字符，得到纯十六进制字符串
  const hex = uuid.replace(/-/g, "");

  // 将十六进制字符串转为字节数组
  const bytes = new Uint8Array(16);
  for (let i = 0; i < 16; i++) {
    bytes[i] = parseInt(hex.slice(i * 2, i * 2 + 2), 16);
  }

  // 将字节数组转为二进制字符串
  const binary = String.fromCharCode(...bytes);

  // 使用 btoa 进行 Base64 编码
  return btoa(binary);
};
export const downloadFiles = (urls: string[], delay: number = 1000) => {
  const link = document.createElement("a");
  document.body.appendChild(link);
  link.style.display = "none";

  urls.forEach((url, index) => {
    setTimeout(() => {
      link.href = url;
      link.download = `file_${index + 1}`;
      link.click();
    }, index * delay);
  });

  document.body.removeChild(link);
};

export const downloadFilesAsZip = async (urls: string[]) => {
  showToast("Downloading...", "info");
  const zip = new JSZip();

  await Promise.all(
    urls.map(async (url, index) => {
      const response = await fetch(url);
      const blob = await response.blob();
      const fileExtension = url.split(".").pop();
      zip.file(`image_${index + 1}.${fileExtension}`, blob);
    })
  );

  zip.generateAsync({ type: "blob" }).then((content) => {
    saveAs(content, "images.zip");
    showToast("Downloaded", "success");
  });
};
