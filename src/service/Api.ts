import axios, { AxiosRequestConfig } from "axios";
import { ApiError } from "./ApiError";
import ApiServices from "./ApiServices";
import { LOGIN_CODE, STORAGE_TOKEN } from "@/constant";
import { changeLoginDialogState, clearUserLogout } from "@/stores/UserStores";
import { showToast } from "@/utils/toast";

export const baseURL =
  process.env.NEXT_PUBLIC_API_BASE_URL || "https://api.deloris-ai.com/api";

export const axiosInstance = axios.create({
  timeout: 60000,
});

export let API_TOKEN = "";
export let LOCALE = "";

export function updateLocale(locale: string) {
  LOCALE = locale;
}

export function updateToken(token: string) {
  localStorage.setItem(STORAGE_TOKEN, token);
  API_TOKEN = token;
}
export function clearToken() {
  localStorage.removeItem(STORAGE_TOKEN);
  API_TOKEN = "";
}

axiosInstance.interceptors.request.use((config) => {
  config.headers.set("client", "web");
  config.headers.set("Authorization", "Bearer " + API_TOKEN);
  config.headers.set("locale", LOCALE);
  return config;
});

export interface ResponseWrap<T> {
  code?: number;
  message?: string;
  data: T;
  status: number;
}

axiosInstance.interceptors.request.use(
  function (config) {
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

export function isValidResponse(obj: any): obj is ResponseWrap<any> {
  return (
    obj !== null &&
    typeof obj === "object" &&
    (typeof obj.code === "number" || obj.code === undefined) &&
    (typeof obj.message === "string" || obj.message === undefined)
  );
}

axiosInstance.interceptors.response.use(
  (res) => {
    const { data: response = {}, statusText } = res;
    const { code, message } = response as ResponseWrap<any>;
    if (code && LOGIN_CODE.includes(code)) {
      try {
        showToast("Login expired, please login again", "error");
        clearUserLogout();
        changeLoginDialogState(true);
      } catch {}
      return;
    }
    if (code !== undefined && code !== 0 && code !== 200) {
      throw new ApiError({
        code: String(code),
        message: message,
        httpStatus: statusText,
      });
    }
    return response;
  },
  (e) => {
    try {
      const obj = JSON.parse(e.request.response) as any;

      if (e.request && isValidResponse(obj)) {
        const commonResponse: ResponseWrap<any> = obj;
        if (commonResponse.code && LOGIN_CODE.includes(commonResponse.code)) {
          try {
            showToast("Login expired, please login again", "error");
            clearUserLogout();
            changeLoginDialogState(true);
          } catch {}
          return;
        }
        return commonResponse;
      } else {
        throw new Error(e);
      }
    } catch (err) {
      return { code: -1, message: e.message };
    }
  }
);

export const apiService = new ApiServices<AxiosRequestConfig>({
  baseURL,
  request: (params, options) => {
    return axiosInstance.request({
      ...params,
      ...options,
      headers: {
        ...options?.headers,
      },
    });
  },
});
