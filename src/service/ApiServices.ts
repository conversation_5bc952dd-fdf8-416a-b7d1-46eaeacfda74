import {
  APP_ID_DELORIS,
  SOULMATE_PAGE_SIZE,
  STORAGE_TOKEN,
  SUBSCRIPTION_TYPE,
} from "@/constant";
import {
  ArticleResp,
  AuthRequest,
  AuthResponse,
  ChatModel,
  CreateCharacterParamsV2,
  CreatePaymentRequest,
  CreatePaymentResponse,
  CreateV2Request,
  EditCustomCharacterParams,
  FaceModel,
  GenerateVideoParams,
  GenerateVideoResp,
  GetProductResponse,
  GetProductResponseForLoginUser,
  HomeListRequestLazyLoader,
  ImageStyle,
  InteractListResp,
  InterestInListResp,
  LoginResponse,
  MaterialData,
  MaterialRequest,
  ResponseWrap,
  UserEntity,
  WebsiteConfig,
} from "@/types";
import {
  BotTemplate,
  BotTemplateGroup,
  Chat,
  CreateCharacterParams,
  CreateMessageRequest,
  CreateMessageResponseData,
  CreateRequest,
  CreateResponse,
  EditCharacterParams,
  GetSoulmatesRequest,
  Message,
  ProjectEntity,
  TaskQueryRequest,
  VoiceIdList,
} from "@/types";
import { retryOperation } from "@/utils/utils";
import { axiosInstance, API_TOKEN } from "./Api";
import { UploadResponse } from "@/types/create";
import { ApiFetch } from "./ApiFetch";
import { CustomTag } from "@/types/createCustomChar";
import {
  ImageGeneratorConfig,
  InspirationItem,
  StyleItem,
} from "@/types/createImageType";
import {
  TaskDeleteRequest,
  TaskDeleteResponse,
  TasksQueryRequest,
  TasksQueryResponse,
} from "@/types/my-creation";

export default class ApiServices<T> {
  private request: any = () => {
    throw new Error("StoryApiService.request is undefined");
  };
  private baseURL: string | ((path: string) => string) = "";

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: "GET" | "DELETE" | "POST" | "PUT" | "PATCH";
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || "";
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === "string"
      ? this.baseURL + path
      : this.baseURL(path);
  }

  Auth(req?: AuthRequest, options?: T): Promise<AuthResponse> {
    return this.request(
      { url: this.genBaseURL(`/v1/users/auth`), method: "POST", data: req },
      options
    );
  }
  Login(): Promise<LoginResponse> {
    return this.request({
      url: this.genBaseURL(`/v1/users/me`),
      method: "GET",
    });
  }
  // 获取人设列表
  GetSoulmatesList(
    req: GetSoulmatesRequest & { locale: string },
    options?: T
  ): Promise<ResponseWrap<{
    bot_templates: BotTemplate[];
    total_count: number;
  }> | null> {
    const url = this.genBaseURL(
      `/v1/sys/bot-templates?app_id=${APP_ID_DELORIS}&is_system_template=${
        req.is_system_template
      }${
        req.template_tag_filter
          ? `&template_tag_filter=${req.template_tag_filter}`
          : ""
      }&page=${req.page || 1}&page_size=${req.page_size || SOULMATE_PAGE_SIZE}`
    );
    return ApiFetch({ url, method: "GET", headers: { locale: req.locale } });
  }
  // 获取首页列表
  getHomeList(req: { locale: string; interested_in?: string }): Promise<
    ResponseWrap<{
      bot_templates: BotTemplate[];
      total_count: number;
    }>
  > {
    const url = this.genBaseURL(
      `/v1/sys/personalization-bot-templates?page_size=${40}${
        req.interested_in ? `&interested_in=${req.interested_in}` : ""
      }`
    );
    return ApiFetch({ url, method: "GET", headers: { locale: req.locale } });
  }
  // 根据id创建chat
  createChat(data: { bot_template: string }): Promise<
    ResponseWrap<{
      id: string;
    }>
  > {
    return this.request({
      url: this.genBaseURL("/v1/chats/"),
      method: "POST",
      data,
    });
  }
  CreateTask(req?: CreateRequest, options?: T): Promise<CreateResponse> {
    const url = this.genBaseURL("/v1/projects/create");
    const method = "POST";
    const data = req;
    return this.request({ url, method, data }, options);
  }
  QueryTask(
    req?: TaskQueryRequest,
    options?: T
  ): Promise<ResponseWrap<ProjectEntity>> {
    const url = this.genBaseURL(`/v1/projects/${req?.project_id}`);
    const method = "GET";
    return this.request({ url, method }, options);
  }
  // 获取语音ID列表
  getVoiceIdList(): Promise<ResponseWrap<VoiceIdList>> {
    return this.request({
      url: this.genBaseURL("/v1/materials/?scene=create_image"),
      method: "GET",
    });
  }
  // 获取角色组
  getGroups(): Promise<ResponseWrap<BotTemplateGroup[]>> {
    return this.request({
      url: this.genBaseURL(
        `/v1/sys/bot-template-group?app_id=${APP_ID_DELORIS}`
      ),
      method: "GET",
    });
  }
  // 编辑角色
  editCharacter(data: Partial<EditCharacterParams>) {
    return this.request({
      url: this.genBaseURL("/v1/sys/edit-bot-templates"),
      method: "POST",
      data: {
        ...data,
        app_id: APP_ID_DELORIS,
      },
    });
  }
  // 获取人设描述和人设prompt
  queryCharacterDescriptionAndPrompt(req: { message_id: string }): Promise<
    ResponseWrap<{
      content: string;
    }>
  > {
    return this.request({
      url: this.genBaseURL(
        `/v1/article/get_seo_article?message_id=${req.message_id}`
      ),
      method: "GET",
    });
  }
  // 获取历史人设记录列表
  queryHistoryChatList(req?: {
    last_seen_create_time?: string;
    page_size?: number;
  }): Promise<
    ResponseWrap<{
      chats: Chat[];
      has_more: boolean;
    }>
  > {
    const url = this.genBaseURL(
      `/v1/chats/?app_id=${APP_ID_DELORIS}${
        req?.last_seen_create_time
          ? `&last_seen_create_time=${req.last_seen_create_time}`
          : ""
      }&page_size=${req?.page_size || SOULMATE_PAGE_SIZE}`
    );
    return this.request({
      url,
      method: "GET",
    });
  }
  // 获取chat详情
  queryChatDetail(
    chat_id: string,
    offset?: number
  ): Promise<
    ResponseWrap<{
      messages: { messages: Message[] };
      bot_template: BotTemplate;
      chat_id: string;
    }>
  > {
    return this.request({
      url: this.genBaseURL(
        `/v1/chats/get_chat?chat_id=${chat_id}&offset=${offset || 0}`
      ),
      method: "GET",
    });
  }
  // 聊天api
  createChatMessage(
    req?: CreateMessageRequest,
    options?: T
  ): Promise<CreateMessageResponseData> {
    return this.request(
      { url: this.genBaseURL(`/v1/chats/message`), method: "POST", data: req },
      options
    );
  }

  // 删除人设
  deleteCharacter(data: { chat_id?: string }) {
    return this.request({
      url: this.genBaseURL(`/v1/chats/del-chat?chat_id=${data.chat_id}`),
      method: "DELETE",
    });
  }
  // 删除会话记录
  deleteChatHistory(chat_id: string) {
    return this.request({
      url: this.genBaseURL(`/v1/chats/clear-chat-message?chat_id=${chat_id}`),
      method: "DELETE",
    });
  }
  // 创建角色
  createCharacter(data: CreateCharacterParams): Promise<
    ResponseWrap<{
      message_id: string;
      id: string;
    }>
  > {
    return this.request({
      url: this.genBaseURL("/v1/sys/bot-templates"),
      method: "POST",
      data: {
        ...data,
        app_id: APP_ID_DELORIS,
      },
    });
  }
  // 获取语音流转成的blobSrc
  async getBlobSrc({
    voice_id,
    message_id,
    bot_template_id,
  }: {
    voice_id: string;
    message_id: string;
    bot_template_id: string;
  }) {
    return retryOperation(async () => {
      const response = await fetch(this.genBaseURL(`/v1/chats/speech`), {
        method: "POST",
        body: JSON.stringify({
          voice_id,
          message_id,
          bot_template_id,
        }),
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem(STORAGE_TOKEN)}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const contentType = response.headers.get("content-type") || "audio/mpeg";
      const blob = await response.blob();
      const audioBlob = new Blob([blob], { type: contentType });
      if (audioBlob.size === 0) {
        throw new Error("Received empty audio blob");
      }

      const blobUrl = URL.createObjectURL(audioBlob);
      return blobUrl;
    });
  }

  QueryMaterial(
    req?: MaterialRequest,
    options?: T
  ): Promise<ResponseWrap<MaterialData>> {
    const url = this.genBaseURL("/v1/materials/");
    const method = "GET";
    return this.request({ url, method, params: req }, options);
  }
  GetFaces(): Promise<ResponseWrap<FaceModel[]>> {
    const url = this.genBaseURL("/v1/faces/fetch");
    const method = "GET";
    return this.request({ url, method });
  }
  UploadFace(req: { file: File }): Promise<UploadResponse> {
    const url = this.genBaseURL("/v1/faces/upload");
    const formData = new FormData();
    formData.append("file", req.file);
    return axiosInstance.post(url, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: "Bearer " + API_TOKEN,
      },
      method: "POST",
    });
  }
  QueryFaces(
    req: {
      face_model_id: string;
    },
    options?: T
  ): Promise<ResponseWrap<FaceModel>> {
    const url = this.genBaseURL("/v1/faces/query");
    const method = "GET";
    const params = req;
    return this.request({ url, method, params }, options);
  }
  CreatePayment(
    req: CreatePaymentRequest,
    options?: T
  ): Promise<CreatePaymentResponse> {
    const url = this.genBaseURL(`/v1/subscription/payment`);
    const method = "POST";
    const data = req;
    return this.request({ url, method, data }, options);
  }
  GetProducts(
    subscription_type: (typeof SUBSCRIPTION_TYPE)[keyof typeof SUBSCRIPTION_TYPE]
  ): Promise<GetProductResponse> {
    const url = this.genBaseURL(
      `/v1/subscription/ehentai_products?subscription_type=${subscription_type}`
    );
    const method = "GET";
    return this.request({ url, method });
  }
  // 获取所有的模板标签
  async getTemplateTags(
    req: { locale: string },
    app_id: string = APP_ID_DELORIS
  ): Promise<ResponseWrap<{ template_id_list: string[] }>> {
    const url = this.genBaseURL(`/v1/sys/template-tags?app_id=${app_id}`);
    const method = "GET";
    return ApiFetch({ url, method, headers: { locale: req.locale } });
  }
  // 获取落地页配置
  async getWebsiteConfig(title: string): Promise<ResponseWrap<WebsiteConfig>> {
    const url = this.genBaseURL(`/v1/article/get_seo_page?title=${title}`);
    const method = "GET";
    return ApiFetch({ url, method });
  }
  // (SEO)获取ai文章
  async getSEOPageList(): Promise<ResponseWrap<{ title_list: string[] }>> {
    const url = this.genBaseURL(`/v1/article/get_seo_page_list`);
    const method = "GET";
    return ApiFetch({ url, method });
  }
  // (SEO)获取问候语、人设信息等
  async getTemplateBasicInfo(
    template_id: string,
    locale?: string
  ): Promise<ResponseWrap<{ template: BotTemplate }>> {
    const url = this.genBaseURL(
      `/v1/sys/template-basic-info?template_id=${template_id}`
    );
    const method = "GET";
    return ApiFetch({ url, method, headers: { locale: locale || "en" } });
  }
  // 游客登录
  GetSkipLoginUserInfo(req?: {
    user_id: string;
  }): Promise<ResponseWrap<UserEntity>> {
    const url = this.genBaseURL(`/v1/users/guest_auth`);
    const method = "POST";
    return this.request({ url, method, data: req });
  }

  // 改变nsfw
  changeNSFW(check_nsfw: boolean): Promise<ResponseWrap<any>> {
    const url = this.genBaseURL(
      `/v1/users/change_nsfw?check_nsfw=${check_nsfw}`
    );
    const method = "GET";
    return this.request({ url, method });
  }
  // 上传图片
  uploadImage(req: {
    file: File;
  }): Promise<ResponseWrap<{ icon_url: string }>> {
    const url = this.genBaseURL("/v1/sys/upload-custom-bot-icon");
    const formData = new FormData();
    formData.append("file", req.file);
    return this.request({ url, method: "POST", data: formData });
  }
  // 创建自定义人设 - v2
  createCustomCharacter(data: CreateCharacterParamsV2): Promise<
    ResponseWrap<{
      id: string;
    }>
  > {
    return this.request({
      url: this.genBaseURL("/v1/sys/create-custom-bot-templates"),
      method: "POST",
      data: {
        ...data,
        app_id: APP_ID_DELORIS,
      },
    });
  }
  // 获取自定义人设的标签
  async getCustomTags(locale: string): Promise<ResponseWrap<CustomTag[]>> {
    const url = this.genBaseURL("/v1/sys/custom-template-tags");
    const method = "GET";
    return ApiFetch({ url, method, headers: { locale } });
  }
  // 取消订阅
  cancelSubscription(): Promise<ResponseWrap<any>> {
    const url = this.genBaseURL("/v1/subscription/cancel_subscription");
    const method = "POST";
    return this.request({ url, method });
  }
  // 获取自定义模版
  getCustomTemplate(
    page: number = 1,
    pageSize: number = SOULMATE_PAGE_SIZE
  ): Promise<
    ResponseWrap<{
      bot_templates: BotTemplate[];
      total_count: number;
    }>
  > {
    const url = this.genBaseURL(
      `/v1/sys/my-bot-templates?page=${page}&page_size=${pageSize}`
    );
    const method = "GET";
    return ApiFetch({ url, method });
  }
  // 编辑自定义模板
  editCustomTemplate(
    data: EditCustomCharacterParams
  ): Promise<ResponseWrap<{ id: string }>> {
    const url = this.genBaseURL(`/v1/sys/edit-custom-bot-templates`);
    const method = "POST";
    return this.request({
      url,
      method,
      data,
    });
  }
  // 删除自定义模板
  deleteCustomTemplate(
    template_id: string
  ): Promise<ResponseWrap<{ id: string }>> {
    const url = this.genBaseURL(
      `/v1/sys/delete-custom-bot-template?template_id=${template_id}`
    );
    const method = "DELETE";
    return this.request({ url, method });
  }

  // 获取SuggestReply
  getSuggestReply(req: {
    chat_id: string;
  }): Promise<ResponseWrap<{ reply: string; error_code: number }>> {
    const url = this.genBaseURL(
      `/v1/chats/suggest_reply?chat_id=${req.chat_id}`
    );
    const method = "GET";
    return this.request({ url, method });
  }
  // 获取suggestImageTemplate
  getSuggestImageTemplate() {
    const url = this.genBaseURL(`/v1/chats/suggest_image_template`);
    const method = "GET";
    return this.request({ url, method });
  }
  // 取消/收藏模版
  cancelOrCollectTemplate(data: { template_id: string; is_favorite: boolean }) {
    const url = this.genBaseURL(`/v1/sys/favorite-template`);
    const method = "POST";
    return this.request({ url, method, data });
  }
  // 获取是否被收藏的模版
  getIsBotTemplateFavorited(
    bot_template_id: string
  ): Promise<ResponseWrap<{ is_favorited: boolean }>> {
    const url = this.genBaseURL(
      `/v1/sys/is_bot_template_favorited?bot_template_id=${bot_template_id}`
    );
    const method = "GET";
    return this.request({ url, method });
  }
  // 获取收藏的模版
  getBookmarkTemplate(
    page: number = 1,
    pageSize: number = SOULMATE_PAGE_SIZE
  ): Promise<
    ResponseWrap<{ bot_templates: BotTemplate[]; total_count: number }>
  > {
    const url = this.genBaseURL(
      `/v1/sys/my-favorite-templates?page=${page}&page_size=${pageSize}`
    );
    const method = "GET";
    return this.request({ url, method });
  }
  // 登录用户调用的接口
  getEhentaiProductsForLoginUser(
    subscription_type: (typeof SUBSCRIPTION_TYPE)[keyof typeof SUBSCRIPTION_TYPE]
  ): Promise<GetProductResponseForLoginUser> {
    const url = this.genBaseURL(
      `/v1/subscription/ehentai_products_for_login_user?subscription_type=${subscription_type}`
    );
    const method = "GET";
    return this.request({ url, method });
  }

  // 获取差价接口
  getPreviewProration(
    product_id: string
  ): Promise<ResponseWrap<{ price_difference: number }>> {
    const url = this.genBaseURL(
      `/v1/subscription/preview_proration?product_id=${product_id}`
    );
    const method = "GET";
    return this.request({ url, method });
  }
  // 获取模型
  getChatModels(chat_id: string): Promise<ResponseWrap<ChatModel>> {
    const url = this.genBaseURL(`/v1/chats/get_chat_models?chat_id=${chat_id}`);
    const method = "GET";
    return this.request({ url, method });
  }
  // 切换模型
  changeChatModel(data: { chat_id: string; model_index: number }) {
    const url = this.genBaseURL(
      `/v1/chats/change_chat_model?chat_id=${data.chat_id}&model_index=${data.model_index}`
    );
    const method = "GET";
    return this.request({ url, method });
  }
  // 重新生成需要删除最后一条消息
  deleteLastMessages({
    chat_id,
    message_id,
  }: {
    chat_id: string;
    message_id: string;
  }) {
    const url = this.genBaseURL(`/v1/chats/${chat_id}/message/${message_id}`);
    const method = "DELETE";
    return this.request({ url, method });
  }
  // 获取聊天生图风格列表
  getImageStyles(chat_id: string): Promise<ResponseWrap<ImageStyle>> {
    const url = this.genBaseURL(
      `/v1/chats/get_image_styles?chat_id=${chat_id}`
    );
    const method = "GET";
    return this.request({ url, method });
  }
  // 设置聊天生图风格
  changeInChatImageGenStyle(data: { chat_id: string; style_index: number }) {
    const url = this.genBaseURL(
      `/v1/chats/change_in_chat_image_gen_style?chat_id=${data.chat_id}&style_index=${data.style_index}`
    );
    const method = "GET";
    return this.request({ url, method });
  }
  // 更新用户偏好
  updateUserPreferences(data: {
    preferences_config: { nsfw_switch?: boolean; interested_in?: string };
  }) {
    const url = this.genBaseURL("/v1/users/update_user_preferences");
    const method = "POST";
    return this.request({ url, method, data });
  }
  // 获取兴趣列表
  getInterestInList(): Promise<ResponseWrap<InterestInListResp>> {
    const url = this.genBaseURL("/v1/users/get_interest_in_list");
    const method = "GET";
    return this.request({ url, method });
  }
  // 获取文章
  getArticle(id: string): Promise<ResponseWrap<ArticleResp>> {
    const url = this.genBaseURL(`/v1/article/?article_id=${id}`);
    const method = "GET";
    return this.request({ url, method });
  }
  // 获取首页列表，下拉加载更多
  async getHomeListForLazyLoader(req: HomeListRequestLazyLoader): Promise<
    ResponseWrap<{
      bot_templates: BotTemplate[];
      total_count: number;
    }>
  > {
    const url = this.genBaseURL(
      `/v1/sys/pagination_personalization_bot_templates_v2`
    );
    const method = "POST";
    return this.request({ url, method, data: req });
  }
  // 获取文章列表
  async getArticleByTag(url_tag: string): Promise<ResponseWrap<ArticleResp>> {
    const url = this.genBaseURL(`/v1/article/url_tag?url_tag=${url_tag}`);
    const method = "GET";
    return this.request({ url, method });
  }
  // 生图模块-获取Styles列表
  getStyleList(): Promise<ResponseWrap<StyleItem[]>> {
    const url = "/api/create-image/styles";
    const method = "GET";
    return this.request({ url, method });
  }
  // 生图模块-获取instantInspirationConfig列表
  getInstantInspirationConfig({
    baseURL,
    keyword,
  }: {
    baseURL: string;
    keyword: string;
  }): Promise<ResponseWrap<InspirationItem[]>> {
    const url = `${baseURL}/api/create-image/instant-inspiration-list?keyword=${keyword}`;
    const method = "GET";
    return this.request({ url, method });
  }

  // 生图模块-获取image-generator-list列表
  getImageGeneratorList({
    baseURL,
  }: {
    baseURL: string;
  }): Promise<ResponseWrap<ImageGeneratorConfig>> {
    const url = `${baseURL}/api/create-image/image-generator-list`;
    const method = "GET";
    return this.request({ url, method });
  }
  CreateTaskV2(
    req?: Partial<CreateV2Request>,
    options?: T
  ): Promise<CreateResponse> {
    const url = this.genBaseURL("/v1/projects/create_v2");
    const method = "POST";
    const data = req;
    return this.request({ url, method, data }, options);
  }
  QueryTasks(
    req?: TasksQueryRequest,
    options?: T
  ): Promise<TasksQueryResponse> {
    const url = this.genBaseURL(`/v1/projects/fetch`);
    const method = "GET";
    const params = req;
    return this.request({ url, method, params }, options);
  }

  // 删除画廊中的图片
  DeleteGalleryImage(
    req?: TaskDeleteRequest,
    options?: T
  ): Promise<TaskDeleteResponse> {
    const url = this.genBaseURL(`/v1/projects/delete_segments`);
    const method = "DELETE";
    return this.request({ url, method, data: req }, options);
  }
  // 删除账号
  deleteAccount(): Promise<ResponseWrap<any>> {
    const url = this.genBaseURL(`/v1/users/delete_account_v2`);
    const method = "GET";
    return this.request({ url, method });
  }
  // 获取interact_list
  getInteractList(): Promise<ResponseWrap<InteractListResp>> {
    const url = this.genBaseURL(`/v1/chats/interact_list`);
    const method = "GET";
    return this.request({ url, method });
  }
  // 请求生成视频
  imageToVideoInChat(
    data: Partial<GenerateVideoParams>
  ): Promise<ResponseWrap<GenerateVideoResp>> {
    const url = this.genBaseURL(`/v1/chats/image_to_video_in_chat`);
    const method = "POST";
    return this.request({ url, method, data });
  }

  // 轮询生成视频
  pollGenerateVideoInChat(template_video_id: string): Promise<
    ResponseWrap<{
      status: string;
      video_url: string;
    }>
  > {
    const url = this.genBaseURL(
      `/v1/chats/get_video_in_chat?template_video_id=${template_video_id}`
    );
    const method = "GET";
    return this.request({ url, method });
  }
}
