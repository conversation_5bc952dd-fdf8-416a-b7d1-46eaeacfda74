import { imageGeneratorConfig } from "@/config/imageGeneratorConfig";
import { ResponseWrap } from "@/types";
import { ImageGeneratorConfig } from "@/types/createImageType";

export async function GET() {
  try {
    const response: ResponseWrap<ImageGeneratorConfig> = {
      data: imageGeneratorConfig,
      message: "success",
      code: 200,
      status: 200,
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    const errorResponse: ResponseWrap<null> = {
      data: null,
      message: "An error occurred",
      code: 500,
      status: 500,
    };

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }
}
