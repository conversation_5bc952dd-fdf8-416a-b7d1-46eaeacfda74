import { styles } from "@/config/styleList";
import { ResponseWrap } from "@/types";
import { StyleItem } from "@/types/createImageType";

export async function GET() {
  try {
    const response: ResponseWrap<StyleItem[]> = {
      data: styles,
      message: "success",
      code: 200,
      status: 200,
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    const errorResponse: ResponseWrap<null> = {
      data: null,
      message: "An error occurred",
      code: 500,
      status: 500,
    };

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }
}
