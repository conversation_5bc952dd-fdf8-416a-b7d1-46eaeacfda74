import { NextRequest } from "next/server";
import { ResponseWrap } from "@/types";
import { instantInspirationConfig } from "@/config/instantInspirationConfig";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const keyword = searchParams.get("keyword") || "";
    const response = {
      data: instantInspirationConfig[keyword],
      message: "success",
      code: 200,
      status: 200,
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    const errorResponse: ResponseWrap<null> = {
      data: null,
      message: "An error occurred",
      code: 500,
      status: 500,
    };

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }
}
