import { ROUTES } from "@/constant";
import { locales } from "@/i18n/routing";
import { baseURL } from "@/service/Api";
import { BotTemplate } from "@/types";
import { MetadataRoute } from "next";
import { headers } from "next/headers";

export interface Article {
    id: string;
    title: string;
    article_type: number;
    create_time: string;
    url_tag: string;
}

const FIXED_LAST_MODIFIED = new Date("2025-06-07T20:58:32.891Z");

// 需要排除的路由
const EXCLUDED_ROUTES = ["/chatDetail", "/ai-chat"];

// 获取bot模板
const handleGetBotTemplates = async (urlList: string[]) => {
    const res = await fetch(`${baseURL}/v1/sys/bot-templates?app_id=deloris`, {
        method: "GET",
    });
    const data = await res.json();
    const botTemplates = data.data.bot_templates;

    // 处理bot模板对应的id路径
    botTemplates.slice(0,30).map((item: BotTemplate) => {
        const url = `/message/${item.id}`;
        urlList.push(url);
    });
};

// 获取tag
const handleGetTags = async (urlList: string[]) => {
    const res = await fetch(`${baseURL}/v1/sys/template-tags?app_id=deloris`, {
        method: "GET",
    });
    const data = await res.json();
    const { template_id_list } = data.data;
    Object.entries(template_id_list).forEach(([key, value]) => {
        const total_count = value as number;
        const page_count = Math.ceil(total_count / 20);
        Array.from({ length: page_count }, (_, index) => {
            urlList.push(`/tags/${key}?page=${index + 1}`);
        });
    });
};

// 获取文章
const handleGetArticles = async (urlList: string[]) => {
    const res = await fetch(`${baseURL}/v1/article/list?article_type=6`, {
        method: "GET",
    });
    const data = await res.json();
    const articles = data.data.article_list;
    articles.map((item: Article) => {
        const url = `/article/${item.url_tag}`;
        console.log(`tag ${item}`)
        urlList.push(url);
    });
};

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
    const urlList: string[] = Object.values(ROUTES).filter(
        (route) =>
            !EXCLUDED_ROUTES.some((excludedRoute) => route.includes(excludedRoute))
    );
    const headersList = await headers();
    const host = headersList.get("host") || "";
    await handleGetBotTemplates(urlList);
    await handleGetTags(urlList);
    await handleGetArticles(urlList);

    const sitemap: MetadataRoute.Sitemap = urlList.map((url) => ({
        url: `https://${host}${url}`.replace(/&/g, "&amp;"),
        lastModified: FIXED_LAST_MODIFIED,
        alternates: {
            languages: Object.fromEntries(
                locales.map((locale) => [
                    locale,
                    `https://${host}/${locale}${url}`.replace(/&/g, "&amp;"),
                ])
            ),
        },
    }));
    return sitemap;
}
