@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  background-color: #282c34;
  color: #fff;
}

@layer utilities {
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: #1b1c2c;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #2d2f3f;
}

:root {
  --primary-color: rgb(202, 60, 118);
  --primary-color-pure: 202, 60, 118;
  --dark-bg: #282c34;
}

@layer utilities {
  .input-style {
    @apply w-full bg-white/5 p-2 px-3 focus:ring-2 focus:ring-primary-color rounded-xl outline-none resize-none;
  }
  .text-muted-foreground {
    color: #7f8ea3;
  }
}

em {
  color: #7f8ea3;
}

@keyframes shake {
  0% {
    transform: translateX(0);
  }
  12.5% {
    transform: translateX(-5px);
  }
  25% {
    transform: translateX(5px);
  }
  37.5% {
    transform: translateX(-5px);
  }
  50% {
    transform: translateX(5px);
  }
  62.5% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
  87.5% {
    transform: translateX(-5px);
  }
  100% {
    transform: translateX(0);
  }
}

@layer utilities {
  .animate-shake {
    animation: shake 1s ease-in-out;
  }
  .canvasBackground {
    background-image: radial-gradient(circle, #444 1px, transparent 1px);
    background-size: 20px 20px;
  }
}

@media (max-width: 640px) {
  .layout-container {
    min-height: 100vh;
    min-height: 100dvh;
    min-height: -webkit-fill-available;
  }

  html {
    height: -webkit-fill-available;
  }

  body {
    min-height: 100vh;
    min-height: 100dvh;
    min-height: -webkit-fill-available;
  }
}
@media (min-width: 640px) {
  .layout-container {
    @apply h-screen;
  }
}
