import { getDomainTitle } from "@/utils/domain";
import { headers } from "next/headers";
import { Link } from "@/i18n/routing";

const BlockedContentPolicy = async () => {
  const headerList = await headers();
  const host = headerList.get("host");
  const title = getDomainTitle(host);

  const introduction = {
    main: `At ${title}, we are committed to provide a secure, respectful, and lawful online environment for all users. Even though users can not upload any content on our Website, App(s) or Services, but can "only" chat and initiate the creation of AI generated content, which is accessible only to the specific user in his/her private space and is not accessible publicly, there are categories of content strictly forbidden on our Website, App(s) or Services and outlined in the present Blocked Content Policy.`,
    secondary: `By using the ${title} Website, App(s), or Services, you explicitly acknowledge and agree to adhere to these content restrictions. We reserve the right to investigate and take appropriate action against any user found in violation of these terms.`,
  };

  const sections = [
    {
      title: "1. Prohibited Content",
      content: [
        "This policy applies to all content, including but not limited to text, images, videos, and any other AI-generated content created on our Website, App(s) or Services.",
        "Illegal Content: Content that contravenes local, national, or international laws and regulations is strictly prohibited. This includes but is not limited to, content associated with illegal drugs, weapons, violence, bestiality, sexual violence, incest, child pornography (CP), or activities that advocate or endorse illegal actions.",
        "Hate Speech and Discrimination: We do not tolerate content that promotes hate speech, discrimination, or harassment based on various factors, including race, ethnicity, nationality, religion, gender, sexual orientation, disability, or any other protected characteristic.",
        "Violence and Harm: Content that encourages, glorifies, or promotes violence, self-harm, or harm to others is strictly prohibited. This includes content that endorses suicide, terrorism, or any form of harm.",
        "Child Exploitation: We unequivocally disallow content that exploits or poses a danger to minors, including but not limited to child pornography, sexual exploitation, or any form of harm or harassment towards minors.",
        "Content Resembling Minors: It is strictly prohibited trying to generate AI content resembling minors. We are committed to ensuring that AI-generated content on our platform does not bear any resemblance to minors or exploit their likeness. Any attempt to generate AI content resembling minors will be promptly reviewed and removed according to our Content Moderation procedures (see paragraph 3).",
        "Infringement on Privacy and Copyright: Any content that infringes upon the privacy, copyrights, trademarks, or intellectual property rights of individuals or entities is not permitted. This includes sharing personal information without consent, pirated content, or any unauthorized use of copyrighted material.",
        "Impersonation and Celebrity Content: Deceptive or harmful impersonation of real individuals, public figures, or celebrities is prohibited. This encompasses any misleading attempts to impersonate or misrepresent others.",
      ],
    },
    {
      title: "2. Content Responsibility",
      content: [
        `You, as a user of our Website, App(s) or Services are solely responsible for the Output generated by the AI Companions through text messages, voice messages, images, and videos. The AI Companions learn and respond based on the conversations you lead and the parameters you select. You understand and agree that ${title} does not control or endorse the content generated by the AI Companions. Therefore, you acknowledge that you are fully responsible for the Output generated by the AI and for your own actions while using the Website, App(s) or Services.`,
        `You must ensure that your interactions with the AI Companions comply with applicable laws, regulations, our Terms of Use, Community Guidelines other policies, in particular this Blocked Content Policy, and you shall not engage in any illegal, unethical, or harmful activities through the Website, App(s) or Services.`,
      ],
    },
    {
      title: "3. Content Moderation",
      content: [
        `While conversations between users and AI Companions are generally confidential, we have implemented a content moderation filter, based on our proprietary LLM technology to ensure compliance with our Terms of Use, Community Guidelines, this Blocked Content Policy, other policies. In the event that the moderation filter detects any content that violates our Terms of Use, Community Guidelines, this Blocked Content Policy, or other policies, we reserve the right to manually review the flagged content and take appropriate action, which may include terminating the user's account. This measure is implemented to maintain a respectful and secure environment for all users. We strive to strike a balance between privacy and community standards, and we appreciate your understanding and cooperation in adhering to our guidelines.`,
      ],
    },
    {
      title: "4. Content Removal and Account Suspension",
      content: [
        "Violations of this Blocked Content Policy may result in content removal, account suspension, or legal action as appropriate.",
      ],
    },
    {
      title: "5. Contact Information",
      content: [
        "If you have noticed any violation of these Terms of Use, Community Guidelines, this Blocked Content Policy, other policies from your perspective, the content of any nature whatsoever, or you have any questions or require further clarification regarding our Blocked Content Policy, please contact us at: ",
      ],
      contact: {
        email: `support@${host}`,
        additional: ` or use "Contact" section within the App(s).`,
      },
    },
    {
      title: "6. Termination",
      content: [
        "We have the right to suspend or terminate the use of the Website, App(s) or Services by anyone engaged in suspected infringement described above.",
      ],
    },
  ];

  return (
    <main className="p-10">
      <h1 className="text-3xl font-bold mb-6">Blocked Content Policy</h1>
      <section className="prose prose-lg max-w-none">
        <div className="mb-8 space-y-4">
          <p className="text-gray-700">{introduction.main}</p>
          <p className="text-gray-700">{introduction.secondary}</p>
        </div>

        <div className="space-y-8">
          {sections.map((section, index) => (
            <article key={index} className="rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold mb-3">{section.title}</h2>
              <div className="space-y-4">
                {section.content.map((paragraph, pIndex) => (
                  <p key={pIndex} className="text-gray-700">
                    {paragraph}
                    {section.contact &&
                      pIndex === section.content.length - 1 && (
                        <>
                          <Link
                            href="mailto:<EMAIL>?subject=Feedback&body=Body%20Content"
                            className="text-blue-600 hover:text-blue-800 underline"
                            aria-label={`Email support at ${title}`}
                          >
                            {section.contact.email}
                          </Link>
                          {section.contact.additional}
                        </>
                      )}
                  </p>
                ))}
              </div>
            </article>
          ))}
        </div>
      </section>
    </main>
  );
};

export default BlockedContentPolicy;
