import { getDomainTitle } from "@/utils/domain";
import { headers } from "next/headers";
import { Link } from "@/i18n/routing";

const CommunityGuidelines = async () => {
  const headerList = await headers();
  const host = headerList.get("host");
  const title = getDomainTitle(host);

  const introduction = {
    main: `Our community guidelines are pivotal in ensuring a positive experience for our members. Kindly adhere to these guidelines, all pertinent laws, the ${title} Terms of Use, the ${title} Privacy Policy, and all visible regulations when utilizing the ${title} Service. Should we be informed or recognize a possible guideline infringement, we hold the discretion to assess and impose measures, which might include restricting or discontinuing a user's entry to the community or app, or as otherwise detailed in these guidelines. For an in-depth understanding, refer to our Terms of Use.`,
    note: `We reserve the right to update these guidelines; hence, periodic revisits are encouraged. ${title} is accessible solely to users aged 18 and above.`,
  };

  const guidelines = [
    {
      title: "Illegal Activities",
      content: `Refrain from employing the ${title} Service for any unlawful pursuits, including but not limited to commercial sexual actions, trafficking, explicit content, or the endorsement of hazardous or illegal endeavors.`,
    },
    {
      title: "Malicious Use",
      content: `It's forbidden to send viruses, malware, or any malevolent or damaging software. Distributing content that jeopardizes or impedes the ${title} Service's functionality is strictly prohibited.`,
    },
    {
      title: "Hate Speech",
      content:
        "Avoid publishing or disseminating materials that incite animosity or aggression against groups based on aspects like their ethnicity, nationality, religious beliefs, disabilities, gender, age, veteran status, sexual preference, or gender identification.",
    },
    {
      title: "Misinformation",
      content:
        "Steer clear of sharing misleading information that could heighten the threat of immediate violence or physical danger or disrupt the seamless flow of political processes.",
    },
    {
      title: "Personal and Confidential Information",
      content:
        "One must not share another individual's private and confidential details, such as images, credit card details, account security codes, or undisclosed contact data.",
    },
    {
      title: "Account Hijacking",
      content:
        "Unauthorized access to another user's account is strictly forbidden. Employing our service for phishing endeavors is also not allowed.",
    },
    {
      title: "Child Exploitation",
      content:
        "Do not design characters who are underage. Refrain from posting pictures of young individuals, including non-authentic portrayals resembling humans, like artwork, AI-derived visuals, fictional entities, or figurines. Moreover, avoid uploading or disseminating material that takes advantage of, entices, or harms minors. This encompasses all images of child sexual abuse and any content portraying minors in a sensual context. We will eradicate such content and take suitable measures, which encompass account suspension and notifying the National Center for Missing & Exploited Children (NCMEC) and the appropriate legal authorities.",
    },
    {
      title: "Spam",
      content:
        "Avoid spamming, which includes generating or sending characters that deliver undesired marketing or business content, or unrequested or bulk requests.",
    },
    {
      title: "Sexually Explicit Content",
      content:
        "Refrain from posting: Images of real, unclothed adults, showcasing: Visible private parts. Visible rear and/or complete naked close-ups of the backside. Exposed female breasts except when related to breastfeeding, childbirth and post-childbirth scenarios, medical or wellness situations (such as post-mastectomy, raising awareness for breast cancer, or gender affirmation surgery) or during acts of dissent. Depictions of sexual acts, encompassing: Overt sexual actions and arousal. Direct sexual encounters or oral intimacy, described as mouth or private parts engaging or touching another's private areas or backside, with at least one individual's private parts being exposed.",
    },
    {
      title: "Adult Sexual Activity",
      content:
        "Adult sexual activity in digital art, except when shared in an educational or scientific context, or when it aligns with the criteria below and is presented exclusively to individuals aged 18 and above. Real-world art that showcases implicit or overt sexual acts. Adult sexual activity in digital art, under these conditions: The sexual activity isn't overt and doesn't fall into the previously defined fetish content. The material was shared in a comedic or satirical light. Only the body outlines or silhouettes are discernible.",
    },
    {
      title: "Non-Consensual Sexually Explicit Material",
      content:
        "Refrain from sharing content that manipulates individuals via sextortion, vengeance pornography, sharing or threatening to share involuntary intimate visuals, covertly captured images of typically sexualized body parts, or sharing or threatening to disclose confidential intimate conversations.",
    },
    {
      title: "Harassment and Bullying",
      content: `Avoid bullying or harassing others. Those utilizing the ${title} Service for harassment or bullying might see the inappropriate content deleted or face permanent exclusion from the Service. Continuously bombarding other users with undesired messages, inclusive of unasked-for intimate content, constitutes harassment.`,
    },
    {
      title: "Violence and Threats",
      content:
        "Do not circulate portrayals of intense or unnecessary violence. We might delete content and could liaise with law enforcement if we identify a real threat of physical harm or a direct peril to societal safety. Threatening others or planning real-world violent acts is prohibited.",
    },
    {
      title: "Self-Harm",
      content:
        "We might eliminate any content that promotes or endorses self-harm, self-injury, eating disorders, or substance abuse.",
    },
    {
      title: "Impersonation or Deceptive Behavior",
      content:
        "Do not impersonate others via our Service. Avoid providing false data, uploading images of others without their consent, registering an account for someone else, creating multiple accounts, or requesting monetary contributions from other members.",
    },
    {
      title: "Intellectual Property",
      content:
        "Before disseminating content on the Service, ensure you have the requisite permissions. We urge you to honor copyrights, trademarks, and other legal entitlements. Profiles that are predominantly composed of others' materials or content duplicated from online sources might be deactivated.",
    },
    {
      title: "Restricted Goods",
      content:
        "Avoid promoting or endeavoring to finalize deals involving controlled commodities, such as firearms, alcoholic beverages, tobacco, or mature products, through the Service.",
    },
    {
      title: "Contests, Promotions, and Solicitations",
      content:
        "Do not organize contests, raffles, or similar promotional events using the Service. Do not solicit other users for commercial aims, this includes via advertisements, discount codes, or sales links.",
    },
  ];

  const reportingSection = {
    title: "Reporting Potential Issues",
    content: `We employ a blend of human reviewers and automated tools to supervise and assess ${title} accounts, characters, and interactions for content that might breach our Community Guidelines, our Terms of Use, or be injurious.`,
    additionalInfo: `Our members play a pivotal role in reporting content or conduct that might breach our Community Guidelines. If you encounter situations that unsettle or endanger you, we earnestly recommend discontinuing interactions with the character.`,
    contactInfo: `Should you come across content, a character, or a user that you suspect breaches the stated guidelines, kindly report it to us at support@${host}.`,
  };

  return (
    <main className="p-10">
      <h1 className="text-3xl font-bold mb-6">Community Guidelines</h1>
      <p className="text-lg font-semibold mb-6">Last updated January 7, 2025</p>

      <section className="prose prose-lg max-w-none">
        <div className="mb-8 space-y-4">
          <p className="text-gray-700">{introduction.main}</p>
          <p className="text-gray-700">{introduction.note}</p>
        </div>

        <div className="space-y-8">
          {guidelines.map((guideline, index) => (
            <article key={index} className="rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold mb-3">{guideline.title}</h2>
              <p className="text-gray-700">{guideline.content}</p>
            </article>
          ))}
        </div>

        <section className="mt-12 ">
          <h2 className="text-xl font-semibold mb-4">
            {reportingSection.title}
          </h2>
          <div className="space-y-4">
            <p className="text-gray-700">{reportingSection.content}</p>
            <p className="text-gray-700">{reportingSection.additionalInfo}</p>
            <p className="text-gray-700">
              Should you come across content, a character, or a user that you
              suspect breaches the stated guidelines, kindly report it to us at{" "}
              <Link
                href="mailto:<EMAIL>?subject=Feedback&body=Body%20Content"
                className="text-blue-600 hover:text-blue-800 underline"
                aria-label={`Email support at ${title}`}
              >
                support@{host}
              </Link>
            </p>
          </div>
        </section>
      </section>
    </main>
  );
};

export default CommunityGuidelines;
