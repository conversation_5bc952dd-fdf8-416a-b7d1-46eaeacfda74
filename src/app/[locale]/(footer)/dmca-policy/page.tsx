import { getDomainTitle } from "@/utils/domain";
import { headers } from "next/headers";
import { Link } from "@/i18n/routing";

const DMCAPolicy = async () => {
  const headerList = await headers();
  const host = headerList.get("host");
  const title = getDomainTitle(host);

  const introduction = {
    content: `${title} values and upholds the intellectual property rights of others, and we are dedicated to adhering to the Digital Millennium Copyright Act (DMCA) and other relevant copyright laws. Our DMCA Policy delineates the steps we take to address notifications of copyright infringement and guides reaching out to us if you suspect that your copyrighted material has been utilized on our platform without proper authorization.`,
  };

  const sections = [
    {
      title: "1. Reporting Copyright Infringement",
      content: [
        "If you believe in good faith that materials transmitted or created through our Website, App(s) or Services infringe your copyright, you (or your agent) may send us a notice requesting that we remove the material or block access to it. Please provide the following information in writing:",
        "An electronic or physical signature of the owner (or person authorized to act on behalf of the owner) of the copyrighted work;",
        "A description of the copyrighted work that you claim has been infringed upon and sufficient information for us to locate such copyrighted work;",
        "Your address, telephone number, and e-mail address;",
        "A statement by you that you have a good-faith belief that the disputed use is not authorized by the copyright owner, its agent, or the law;",
        "A statement by you, made under penalty of perjury, that the above information in your notice is accurate and that you are the copyright owner or authorized to act on the copyright owner's behalf.",
      ],
    },
    {
      title: "2. DMCA Notices Response",
      content: [
        "After receiving a complete infringement notice, we will take the following actions:",
        "Review and confirm that received documents meet DMCA requirements;",
        "Take proper preliminary actions against said alleged infringement within 1-3 days after receipt of said information, including without limitation link blockage;",
        "Notify the alleged infringer and demand him or her to explain and provide counter evidence.",
      ],
    },
    {
      title: "3. Counter Notification",
      content: [
        "If you believe in good faith that someone has wrongly filed a notice of copyright infringement against you, you may send us a counter-notice. If you do, we will notify the alleged claimant and hold the process for 10-14 days and then re-enable your content unless the copyright owner initiates a legal action against you before then.",
      ],
    },
    {
      title: "4. Contact Information",
      content: [
        "Notices and counter-notices should be sent to us via email at: ",
      ],
      contact: {
        email: `support@${host}`,
        additional:
          ". We are committed to addressing concerns in a timely manner and ensuring a positive experience for all our users.",
      },
    },
    {
      title: "5. Termination",
      content: [
        "We have the right to suspend or terminate the use of the APP by anyone engaged in suspected infringement described above.",
      ],
    },
  ];

  return (
    <main className="p-10">
      <h1 className="text-3xl font-bold mb-6">DMCA Policy</h1>
      <section className="prose prose-lg max-w-none">
        <div className="mb-8">
          <p className="text-gray-700">{introduction.content}</p>
        </div>

        <div className="space-y-8">
          {sections.map((section, index) => (
            <article key={index} className="rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold mb-3">{section.title}</h2>
              <div className="space-y-4">
                {section.content.map((paragraph, pIndex) => (
                  <p key={pIndex} className="text-gray-700">
                    {paragraph}
                    {section.contact &&
                      pIndex === section.content.length - 1 && (
                        <>
                          <Link
                            href="mailto:<EMAIL>?subject=Feedback&body=Body%20Content"
                            className="text-blue-600 hover:text-blue-800 underline"
                            aria-label={`Email support at ${title}`}
                          >
                            {section.contact.email}
                          </Link>
                          {section.contact.additional}
                        </>
                      )}
                  </p>
                ))}
              </div>
            </article>
          ))}
        </div>
      </section>
    </main>
  );
};

export default DMCAPolicy;
