import { getDomainTitle } from "@/utils/domain";
import { headers } from "next/headers";
import { Link } from "@/i18n/routing";

const ComplianceStatement = async () => {
  const headerList = await headers();
  const host = headerList.get("host");
  const title = getDomainTitle(host);

  const introduction = {
    main: `All visual depictions displayed on this website, ${title}, are exempt from the provision of 18 U.S.C. §2257, 2257A and/or 28 C.F.R. 75 because:`,
    exemptions: [
      "They do not portray actual human beings engaged in actual sexually explicit conduct.",
      "They are exclusively artificial creations generated by Artificial Intelligence (AI).",
      "No human models, actors, actresses, or other persons appear in any of the generated images on this website.",
    ],
    commitment: `${title} is committed to only displaying AI-generated content that complies with all applicable laws and regulations. The AI-generated images on our Website, App(s) and Services are created and provided with a strict adherence to ethical guidelines and do not involve the use or depiction of real humans.`,
  };

  const contact = {
    content:
      "For further inquiries or clarification regarding our compliance with 2257 regulations, please contact legal at ",
    email: `support@${host}`,
    additional: ".",
  };

  return (
    <main className="p-10">
      <h1 className="text-3xl font-bold mb-6">
        2257 Record-Keeping Requirements Compliance Statement
      </h1>
      <section className="prose prose-lg max-w-none">
        <div className="mb-8 space-y-4">
          <p className="text-gray-700">{introduction.main}</p>
          <div className="space-y-2">
            {introduction.exemptions.map((exemption, index) => (
              <p key={index} className="text-gray-700">
                {exemption}
              </p>
            ))}
          </div>
          <p className="text-gray-700">{introduction.commitment}</p>
        </div>

        <div className="space-y-8">
          <article className="rounded-lg shadow-sm">
            <div className="space-y-4">
              <p className="text-gray-700">
                {contact.content}
                <Link
                  href="mailto:<EMAIL>?subject=Feedback&body=Body%20Content"
                  className="text-blue-600 hover:text-blue-800 underline"
                  aria-label={`Email support at ${title}`}
                >
                  {contact.email}
                </Link>
                {contact.additional}
              </p>
            </div>
          </article>
        </div>
      </section>
    </main>
  );
};

export default ComplianceStatement;
