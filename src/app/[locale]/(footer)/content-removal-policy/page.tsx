import { getDomainTitle } from "@/utils/domain";
import { headers } from "next/headers";
import { Link } from "@/i18n/routing";

const ContentRemovalPolicy = async () => {
  const headerList = await headers();
  const host = headerList.get("host");
  const title = getDomainTitle(host);

  const introduction = {
    main: `At ${title} we strive to maintaining a safe and respectful experience of all our users. Our Content Removal Policy outlines our approach to addressing concerns related to content that may inadvertently resemble real individuals. All content accessible on the Website, App(s), Services is generated exclusively through artificial intelligence technology, allowing users to create and interact with AI-generated characters. It is important to note that any similarity to an actual person is unintentional and purely coincidental.`,
  };

  const sections = [
    {
      title: "Unintentional Resemblance to Actual Persons",
      content: [
        `Despite the AI-generated nature of the content on the Website, App(s), Services, we acknowledge that there might be instances where the generated content unintentionally resembles actual persons. Recognizing the concerns that may arise in such situations, we are dedicated to promptly addressing them.`,
      ],
    },
    {
      title: "Content Removal Process",
      content: [
        `If a user believes that any content on the Website, App(s), Services bears resemblance to them or another actual person, they can request its removal by contacting our support team at support@${host} or directly report their concern using the "Contact" section in the App(s). We will thoroughly review the request and take appropriate action within a reasonable timeframe.`,
      ],
    },
    {
      title: "User Verification",
      content: [
        "To ensure the accuracy and legitimacy of content removal requests, we may request the user to provide adequate evidence of their identity or relationship to the person depicted in the content. This verification process is implemented to responsibly handle requests and safeguard the rights and interests of all users.",
      ],
    },
    {
      title: "Content Removal",
      content: [
        "Upon verification and confirmation of a valid content removal request, the specified content will be removed in a timely manner from the Website, App(s), Services. Our goal is to complete this process promptly while ensuring compliance with applicable laws and regulations.",
      ],
    },
    {
      title: "Privacy",
      content: [
        "Our highest priority is to respect user privacy throughout the entire content removal process. All requests are treated strictly confidential, and we do not disclose any personal information or details of the requests to any third parties without the user's explicit consent unless required by law.",
      ],
    },
    {
      title: "Contact Information",
      content: [
        `If you have any questions or require further clarification regarding our Content Removal Policy, please contact us at: `,
      ],
      contact: {
        email: `support@${host}`,
        additional: ` or using the "Contact" section in the App(s). We are committed to addressing concerns on time and ensuring a positive experience for all our users.`,
      },
    },
  ];

  return (
    <main className="p-10">
      <h1 className="text-3xl font-bold mb-6">Content Removal Policy</h1>

      <section className="prose prose-lg max-w-none">
        <div className="mb-8">
          <p className="text-gray-700">{introduction.main}</p>
        </div>

        <div className="space-y-8">
          {sections.map((section, index) => (
            <article key={index} className="rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold mb-3">{section.title}</h2>
              <div className="space-y-4">
                {section.content.map((paragraph, pIndex) => (
                  <p key={pIndex} className="text-gray-700">
                    {paragraph}
                    {section.contact &&
                      pIndex === section.content.length - 1 && (
                        <>
                          <Link
                            href="mailto:<EMAIL>?subject=Feedback&body=Body%20Content"
                            className="text-blue-600 hover:text-blue-800 underline"
                            aria-label={`Email support at ${title}`}
                          >
                            {section.contact.email}
                          </Link>
                          {section.contact.additional}
                        </>
                      )}
                  </p>
                ))}
              </div>
            </article>
          ))}
        </div>
      </section>
    </main>
  );
};

export default ContentRemovalPolicy;
