import { getDomainTitle } from "@/utils/domain";
import { headers } from "next/headers";
import { Link } from "@/i18n/routing";

const ComplaintPolicy = async () => {
  const headerList = await headers();
  const host = headerList.get("host");
  const title = getDomainTitle(host);

  const introduction = {
    main: `At ${title}, we prioritize a safe and compliant environment for all users. We strongly encourage users to report any content they believe might be illegal or violate our Terms of Use (TOU). Upon receiving a complaint, ${title} commits to conducting a thorough review of the reported content within the timeframe specified in this Complaint Policy.`,
    outcomes: [
      "Content Removal. If the reported content is found to violate our TOU or the applicable law, it will be promptly removed or modified as necessary.",
      "Content Retention: If the reported content is deemed compliant with our TOU and the applicable law, it will remain accessible on the Website, App(s), or Services.",
      "Other Actions: Depending on the severity or nature of the violation, additional actions such as warnings, user account suspension, or termination may be taken.",
    ],
    final: `${title} reserves the right to modify this Complaint Policy as needed and without prior notice to ensure alignment with applicable laws and enhance the user experience.`,
  };

  const sections = [
    {
      title: "1. Customer Support",
      content: [
        "We have a dedicated support team to assist our users with any concerns or complaints.",
        `Our support team is committed to providing prompt and effective assistance. Users can contact our support team if they come across any issues or have inquiries regarding the ${title} Website, App(s) or Services.`,
        "All inquiries are managed by our support team with professionalism, confidentiality, and impartiality. We are committed to addressing our users' concerns promptly and to the best of our abilities.",
      ],
    },
    {
      title: "2. Submitting a Complaint",
      content: [
        "Users who wish to file a complaint can do so by contacting our customer support team through ",
      ],
      contact: {
        email: `support@${host}`,
        additional: " or directly report in the APP in the Contact section.",
      },
    },
    {
      title: "3. Information to Include in the Complaint",
      content: [
        "When submitting a complaint, users are encouraged to provide the following details to help us investigate and address the issue promptly:",
        "User's full name and e-mail;",
        "Description of the complaint, including relevant details such as the date and time of the incident;",
        "Any supporting documentation or screenshots, if applicable.",
      ],
    },
    {
      title: "4. Acknowledgment of Complaint",
      content: [
        "Upon receiving a complaint, our customer support team will acknowledge the receipt within 24 hours via e-mail. All reported complaints will be thoroughly reviewed by our dedicated team. We strive to address and resolve complaints within 7 (seven) business days from the date of receipt. During this time, we may contact you for additional information to better understand the nature of the complaint.",
      ],
    },
    {
      title: "5. Investigation and Resolution",
      content: [
        "We will conduct a thorough investigation into each complaint to understand the nature of the issue. Our goal is to provide a resolution within a reasonable timeframe. Depending on the complexity of the complaint, some cases may require additional time to conduct a comprehensive investigation. Users will be kept informed of the progress and expected resolution timeline.",
      ],
    },
    {
      title: "6. Feedback and Follow-Up",
      content: [
        "Once the complaint has been addressed, users will receive feedback regarding the outcome of the investigation and any actions taken. We may also seek user feedback on the resolution process to continuously improve our services.",
      ],
    },
    {
      title: "7. Escalation",
      content: [
        "If a user is dissatisfied with the resolution provided, he/she may request further escalation. In this case, users can notify our support team within a reasonable timeframe, providing clear reasons for their dissatisfaction with the initial resolution.",
        "The case will be reviewed by individuals or teams not initially involved in the complaint resolution process. They will reassess the complaint and reconsider the previous decision. The user will be notified of the outcome of the escalation within a reasonable timeframe.",
      ],
    },
  ];

  return (
    <main className="p-10">
      <h1 className="text-3xl font-bold mb-6">Complaint Policy</h1>
      <section className="prose prose-lg max-w-none">
        <div className="mb-8 space-y-4">
          <p className="text-gray-700">{introduction.main}</p>
          <div className="space-y-2">
            {introduction.outcomes.map((outcome, index) => (
              <p key={index} className="text-gray-700">
                {outcome}
              </p>
            ))}
          </div>
          <p className="text-gray-700">{introduction.final}</p>
        </div>

        <div className="space-y-8">
          {sections.map((section, index) => (
            <article key={index} className="rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold mb-3">{section.title}</h2>
              <div className="space-y-4">
                {section.content.map((paragraph, pIndex) => (
                  <p key={pIndex} className="text-gray-700">
                    {paragraph}
                    {section.contact &&
                      pIndex === section.content.length - 1 && (
                        <>
                          <Link
                            href="mailto:<EMAIL>?subject=Feedback&body=Body%20Content"
                            className="text-blue-600 hover:text-blue-800 underline"
                            aria-label={`Email support at ${title}`}
                          >
                            {section.contact.email}
                          </Link>
                          {section.contact.additional}
                        </>
                      )}
                  </p>
                ))}
              </div>
            </article>
          ))}
        </div>
      </section>
    </main>
  );
};

export default ComplaintPolicy;
