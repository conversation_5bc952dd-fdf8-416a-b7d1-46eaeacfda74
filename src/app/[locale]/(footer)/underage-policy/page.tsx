import { getDomainTitle } from "@/utils/domain";
import { headers } from "next/headers";

const UnderagePolicy = async () => {
  const headerList = await headers();
  const host = headerList.get("host");
  const title = getDomainTitle(host);

  const introduction = {
    main: `At ${title} we are committed to ensuring that our Website, App(s), Services is used responsibly and safely by users who meet the legal age requirements.`,
    description: `The Website, App(s), Services include AI-generated adult content for registered users only. Therefore, users must be at least 18 years old or of legal age in their country of residence to access and engage with such content. By using the Website, App(s), Services, users affirm that they meet the minimum age requirement and are legally permitted to access adult content.`,
    note: `We have implemented several measures to oversee and prevent the registration of users not of legal age.`,
    commitment: `We are committed to ensuring that AI-generated content on our Website, App(s), Services does not bear any resemblance to minors or exploit their likeness. For this purpose, we have implemented our proprietary content moderation filter to ensure full compliance with our Terms of Use (TOU), this Underage Policy and other policies.`,
  };

  const sections = [
    {
      title: "Registering",
      content: [
        "When registering on the Website or App(s), an initial landing page or age gate, strategically designed to restrict entry to individuals who are not of legal age is presented to the user. Prior to gaining access to the main content, users are obligated to confirm their age.",
        "Users are prompted to affirm that they are above 18, and this information is used to verify their eligibility to access the Website, App(s) or Services.",
        `A transparent disclaimer emphasizes that users must be 18 years or older to access our Website, App(s) or Services. Users are also notified that providing inaccurate information about their age constitutes a violation of our Terms of Service (TOU).`,
        `${title} cannot be held responsible for any inaccuracies or misrepresentations regarding user age. It is the user's responsibility to ensure compliance with their local laws and regulations regarding the access and consumption of adult content.`,
      ],
    },
    {
      title: "Content responsibility",
      content: [
        `As a user of the Website, App(s), or Services, you bear sole responsibility for the content generated by the AI Companions, encompassing text messages, voice messages, images, and videos. The AI Companions evolve and respond based on your conversations and selected parameters. It is crucial to recognize that ${title} neither controls nor endorses the content produced by the AI Companions.`,
        "You must ensure that your interactions with the AI Companions comply with applicable laws, regulations, our Terms and Policies, in particular the Blocked Content Policy, and you shall not engage in any illegal, unethical, or harmful activities through the APP.",
      ],
    },
    {
      title: "Content Moderation",
      content: [
        `We at ${title} value safety, respect and integrity of all our users. While conversations between users and AI Companions are generally confidential, we have implemented a content moderation filter, based on our proprietary LLM technology to ensure compliance with our Terms of Use (TOU), this Underage Policy, our Blocked Content Policy, other policies. If the moderation filter detects any content that violates our terms, we reserve the right to manually review the flagged content and take appropriate action, which may include terminating the user's account. This measure is implemented to maintain a respectful and secure environment for all users. We strive to strike a balance between privacy and community standards, and we appreciate your understanding and cooperation in adhering to our guidelines.`,
      ],
    },
    {
      title: "Content Removal",
      content: [
        "Any user content that we believe, in our sole discretion, violates these provisions will be promptly removed.",
      ],
    },
    {
      title: "Contact Information",
      content: [
        `If you have noticed any violation of this Underage Policy from your perspective, content of any nature whatsoever or you have any questions or require further clarification regarding our Underage Policy, please contact us at: support@${host} or contact us using "Contact" section with the App(s).`,
      ],
    },
    {
      title: "Termination",
      content: [
        "We reserve the right to suspend or terminate the usage of our Website, App(s) or Services for individuals involved in suspected infringements as described above in this Underage Policy.",
      ],
    },
  ];

  return (
    <main className="p-10">
      <h1 className="text-3xl font-bold mb-6">Underage Policy</h1>

      <section className="prose prose-lg max-w-none">
        <div className="mb-8 space-y-4">
          <p className="text-gray-700">{introduction.main}</p>
          <p className="text-gray-700">{introduction.description}</p>
          <p className="text-gray-700">{introduction.note}</p>
          <p className="text-gray-700">{introduction.commitment}</p>
        </div>

        <div className="space-y-8">
          {sections.map((section, index) => (
            <article key={index} className="rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold mb-3">{section.title}</h2>
              <div className="space-y-4">
                {section.content.map((paragraph, pIndex) => (
                  <p key={pIndex} className="text-gray-700">
                    {paragraph}
                  </p>
                ))}
              </div>
            </article>
          ))}
        </div>
      </section>
    </main>
  );
};

export default UnderagePolicy;
