"use client";

import { useEffect, useRef, useState } from "react";
import { useSearchParams } from "next/navigation";
import { debounce } from "lodash";
import { Box, Typography, IconButton, InputBase, Tooltip } from "@mui/material";
import { useRouter } from "@/i18n/routing";
import {
  Send as SendIcon,
  Image as ImageIcon,
  PlayArrow as PlayArrowIcon,
  Close as CloseIcon,
  Pause as PauseIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
} from "@mui/icons-material";

import { useSoulChatStore } from "@/stores/SoulChatStore";
import ThreeDotsLoading from "@/components/SoulChat/ThreeDotsLoading";
import ChatHistory from "@/components/SoulChat/ChatHistory";
import { COST_TYPE, MESSAGE_TYPE, MESSAGE_TYPE_API, ROUTES } from "@/constant";
import CircularProgress from "@mui/material/CircularProgress";
import { TaskType } from "@/constant";
import { changeLoginDialogState, useUserStores } from "@/stores/UserStores";
import SubscriptionCard from "@/components/SoulChat/SubscriptionCard";
import { showToast } from "@/utils/toast";
import { firebaseLogEvent, MarkdownRenderer } from "@/utils/utils";
import {
  EVENT_ACTION_PAGE_AI_CHAT,
  EVENT_ACTION_SOULMATES_SEND_CHAT_MESSAGE,
  EVENT_ACTION_SOULMATES_SEND_PIC_MESSAGE,
  EVENT_ACTION_SUGGEST_REPLY_SEND,
  EVENT_ACTION_SUGGEST_REPLY_EDIT,
  EVENT_ACTION_SUGGEST_REPLY_RESET,
} from "@/utils/event";
import CardLoading from "@/components/CardLoading";
import { useLocale, useTranslations } from "next-intl";
import { UpgradeCoinsDialog } from "@/components/subscribe/UpgradeCoinsDialog";
import Image from "next/image";
import RightSideBar from "@/components/SoulChat/RightSideBar";
import LoadingResponse from "@/components/SoulChat/LoadingResponse";
import SuggestedReplyBox from "@/components/SoulChat/SuggestedReplyBox";
import QuickOptCol from "@/components/SoulChat/QuickOptCol";
import PhotoMenu from "@/components/SoulChat/AskPhotoMenu";
import ChatHeader from "./_components/ChatHeader";
import ChatNSFW from "./_components/ChatNSFW";
import MindReadingCard from "./_components/MindReadingCard";
import { VideoLoadingCard } from "@/components/SoulChat";
import GenerationFailure from "./_components/GenerationFailure";
import VideoAvatar from "@/components/VideoAvatar";

export default function SoulChat() {
  const t = useTranslations("SubscribePage");
  const t2 = useTranslations("OtherTranslations");
  const searchParams = useSearchParams();
  const chat_id = searchParams.get("id");
  const [msg, setMsg] = useState<string>("");
  const [photoOf, setPhotoOf] = useState<boolean>(false);
  const router = useRouter();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [showRightPanel, setShowRightPanel] = useState(true);
  const [openCoinsModal, setOpenCoinsModal] = useState(false);
  const [openMemberModal, setOpenMemberModal] = useState(false);
  const {
    historyChatList,
    getHistoryChatList,
    getChatDetail,
    chatDetail,
    sendMessage,
    currentChat,
    getGreetings,
    canSendMessage,
    setHistoryChatList,
    playingMessageId,
    loadingMessageId,
    playSingleMessageVoice,
    isPlaying,
    initAudio,
    currentAudio,
    clearStore,
    face_model_id,
    pic_generate_loading,
    isLoadingMore,
    handleLoadMore,
    isCanScroll,
    querySuggestReply,
    changeSuggestReplyInfo,
    suggestReplyInfo,
    fetchSuggestImageTemplate,
    fetchInteractList,
    handleRegenerate,
  } = useSoulChatStore();
  const { userEntity, fetchUserInfo } = useUserStores();
  const locale = useLocale();
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isCanRegenerate, setIsCanRegenerate] = useState(false);

  useEffect(() => {
    if (!chat_id) {
      router.push(ROUTES.HOME, { locale });
      return;
    }
    firebaseLogEvent(EVENT_ACTION_PAGE_AI_CHAT);
    fetchUserInfo();
    fetchInteractList();
    fetchSuggestImageTemplate();
    if (!currentAudio) {
      initAudio();
    }
    return () => {
      clearStore();
    };
  }, []);

  useEffect(() => {
    changeSuggestReplyInfo({
      content: "",
      loading: false,
      isRequesting: false,
    });
    const fetchData = async () => {
      if (chat_id) {
        getHistoryChatList();
        await getChatDetail(chat_id);
      }
    };
    fetchData();
  }, [chat_id]);

  useEffect(() => {
    if (chatDetail.some((chat) => chat.is_need_signup)) {
      changeLoginDialogState(true);
    }

    if (!chatDetail.length && currentChat?.id) {
      getGreetings(chat_id || "");
    }
  }, [chatDetail, currentChat, chat_id]);

  useEffect(() => {
    const lastMessage = chatDetail.slice(-1)[0];
    setIsCanRegenerate(!!(lastMessage && lastMessage.type !== "image"));
  }, [chatDetail]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };
  useEffect(() => {
    if (isCanScroll) scrollToBottom();
  }, [chatDetail, isCanScroll]);

  const handleScroll = debounce((target: HTMLDivElement) => {
    if (target.scrollTop <= 0 && chat_id) {
      handleLoadMore(chat_id);
    }
  }, 200);

  const judgeMemberLevel = (level: number) => {
    const { member_level = [] } = userEntity?.entitlement || {};
    return member_level[0] === level;
  };
  const judgeCreditPoint = (coin: number) => {
    const { credit_point = 0 } = userEntity?.entitlement || {};
    return credit_point >= coin;
  };

  const handleSendMessage = ({
    photoSend = false,
    id,
    prompt,
  }: {
    photoSend?: boolean;
    id?: number;
    prompt?: string;
  }) => {
    if (!photoSend && !msg.trim()) return;
    if (!canSendMessage) {
      showToast("Generating reply, please wait...", "info");
      return;
    }
    handleChangeOrder();
    if (photoOf || photoSend) {
      if (judgeMemberLevel(0) || judgeMemberLevel(1)) {
        setOpenMemberModal(true);
        return;
      }
      // if (judgeMemberLevel(1)) {
      //   showToast(
      //     t("imageGenerationInChatIsOnlyAvailableForDeluxeMembership"),
      //     "error"
      //   );
      //   return;
      // }
      if (!judgeCreditPoint(5)) {
        setOpenCoinsModal(true);
        return;
      }
      if (!photoSend && msg.length < 20) {
        showToast("Please enter at least 20 characters", "error");
        return;
      }
      const sendText = photoSend ? prompt || "" : "Photo of " + msg;
      firebaseLogEvent(EVENT_ACTION_SOULMATES_SEND_PIC_MESSAGE);
      const picParams = {
        project_novel_text: sendText,
        face_model_id: currentChat?.face_model_id || face_model_id,
        bot_template_id: currentChat?.id,
        type: COST_TYPE.IMAGE_IN_CHAT,
        chat_id: chat_id || "",
        face_url: currentChat?.icon,
        suggest_template_id: id ?? -1,
        // project_config: {
        //   sd_model_name: "edge-of-realism",
        //   sd_image_ratio: "2:3",
        //   batch_number: 1,
        //   lora_model_name: "Real Girl",
        //   voice_name: "",
        //   voice_speed: 1,
        //   voice_volume: 11,
        //   background_music_url: "",
        // },
      };
      sendMessage(
        {
          content: sendText,
          chat: chat_id || "",
        },
        true,
        picParams
      );
    } else {
      firebaseLogEvent(EVENT_ACTION_SOULMATES_SEND_CHAT_MESSAGE);
      sendMessage({
        content: msg,
        chat: chat_id || "",
      });
    }
    setMsg("");
  };

  const handleSendMindReading = (desc: string) => {
    if (!canSendMessage) {
      showToast("Generating reply, please wait...", "info");
      return;
    }
    handleChangeOrder();
    sendMessage({
      content: desc,
      chat: chat_id || "",
      message_type: MESSAGE_TYPE_API.shortcuts_mind_reading, // 读心术的type
      is_hide_user_message: true,
    });
  };

  const handleSendDancing = (desc: string, title: string) => {
    if (!canSendMessage) {
      showToast("Generating reply, please wait...", "info");
      return;
    }
    handleChangeOrder();
    sendMessage({
      content: desc,
      chat: chat_id || "",
      message_type: MESSAGE_TYPE_API.shortcuts_video, // 跳舞的type
      video_type: title === "Dancing" ? "Dance" : "PoleDance",
    });
  };

  // 开始聊天后改变左边列表的顺序，正在聊天的排在最前面
  const handleChangeOrder = () => {
    const currentItem = historyChatList.find((item) => item.id === chat_id);
    if (!currentItem) return;

    const newHistoryChatList = historyChatList.filter(
      (item) => item.id !== chat_id
    );
    newHistoryChatList.unshift(currentItem);
    setHistoryChatList(newHistoryChatList);
  };

  const handlePhotoClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSelect = (prompt: string, id?: number) => {
    if (id === undefined) {
      if (prompt.includes("Send me a photo of...")) {
        if (userEntity?.guest_flag) {
          changeLoginDialogState(true);
          return;
        }
        setMsg("");
        setPhotoOf(true);
      } else {
        setPhotoOf(false);
        setMsg(prompt.replace("...", " "));
      }
    } else {
      if (userEntity?.guest_flag) {
        changeLoginDialogState(true);
        return;
      }
      if (judgeMemberLevel(0) || judgeMemberLevel(1)) {
        setOpenMemberModal(true);
        return;
      }
      handleSendMessage({ photoSend: true, id, prompt });
    }

    handleClose();
  };

  return (
    <Box
      sx={{
        display: "flex",
        height: {
          // lg: "calc(100vh)",
          xs: "calc(var(--vh, 1vh) * 100)",
        },
        color: "white",
      }}
      className="m-auto w-full xl:rounded-3xl"
    >
      {/* Left Sidebar - Chat History */}
      <ChatHistory className="xl:flex hidden" />

      {/* Main Chat Area */}
      <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
        {/* Chat Header */}
        <ChatHeader
          currentChat={currentChat}
          chat_id={chat_id}
          showRightPanel={showRightPanel}
          setShowRightPanel={setShowRightPanel}
        />

        {/* Messages Area */}
        <Box
          sx={{
            flex: 1,
            overflowY: "auto",
            display: "flex",
            flexDirection: "column",
            gap: 2,
          }}
          className="p-4 sm:p-6 lg:mx-0 -mx-4"
          onScroll={(e) => {
            const target = e.target as HTMLDivElement;
            handleScroll(target);
          }}
        >
          {isLoadingMore && currentChat?.chat_id === chat_id && (
            <Box sx={{ display: "flex", justifyContent: "center", mb: 2 }}>
              <CircularProgress
                size={20}
                sx={{ color: "rgba(255,255,255,0.7)" }}
              />
            </Box>
          )}
          <div className="flex justify-center items-center flex-col">
            {currentChat?.icon &&
              (currentChat.icon.includes(".mp4") ? (
                <VideoAvatar
                  src={currentChat.icon}
                  className="h-[80px] w-[80px] rounded-full object-cover"
                  width={80}
                  height={80}
                />
              ) : (
                <Image
                  width={80}
                  height={80}
                  src={currentChat.icon}
                  alt="avatar"
                  className="w-[80px] h-[80px] rounded-full object-cover"
                />
              ))}
            <div>{currentChat?.title}</div>
            {currentChat?.description && (
              <div>
                <div
                  className={`max-w-96 text-center text-xs text-muted-foreground ${
                    !showFullDescription ? "line-clamp-3" : ""
                  }`}
                >
                  {currentChat?.description}
                </div>
                <div
                  className="text-center mt-1 cursor-pointer text-primary-color text-xs"
                  onClick={() => setShowFullDescription(!showFullDescription)}
                >
                  {showFullDescription ? (
                    <div className="flex flex-col items-center justify-center">
                      <button
                        className="text-primary-color my-3 text-sm"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          router.push(
                            `${ROUTES.CHAT_DETAIL}/${currentChat?.id}`
                          );
                        }}
                      >
                        show profile
                      </button>
                      <KeyboardArrowUpIcon />
                    </div>
                  ) : (
                    <KeyboardArrowDownIcon />
                  )}
                </div>
              </div>
            )}
          </div>
          {chatDetail.map((chat, index) => {
            let content = chat.content;
            try {
              if (chat.type === MESSAGE_TYPE.video) {
                content = JSON.parse(content || "{}");
              }
            } catch {}

            return (
              <Box
                key={`${chat.id}-${index}`}
                sx={{
                  alignSelf: chat.is_from_bot ? "flex-start" : "flex-end",
                  maxWidth:
                    chat.type === MESSAGE_TYPE.mind_reading ? "90%" : "70%",
                }}
              >
                <Box
                  sx={{
                    borderRadius: 2,
                    position: "relative",
                    ...(chat.is_from_bot &&
                    !chat.loading &&
                    !chat.is_need_subscribe &&
                    chat.type !== MESSAGE_TYPE.video_error
                      ? {
                          bgcolor: "rgba(255,255,255,0.9)",
                          color: "#000",
                        }
                      : {
                          bgcolor: "rgba(255,255,255,0.1)",
                          color: "#fff",
                        }),
                    ...(chat.type === MESSAGE_TYPE.mind_reading ||
                    chat.type === MESSAGE_TYPE.video
                      ? {
                          bgcolor: "transparent",
                        }
                      : {}),
                  }}
                  className="sm:p-3 p-2"
                >
                  {chat.is_need_subscribe ? (
                    <SubscriptionCard content={chat.content} />
                  ) : chat.loading &&
                    chat.type === MESSAGE_TYPE.mind_reading ? (
                    <MindReadingCard chat={chat} currentChat={currentChat} />
                  ) : chat.loading && chat.type === "image" ? (
                    <CardLoading width={200} height={300} />
                  ) : chat.loading && chat.type === MESSAGE_TYPE.video ? (
                    <VideoLoadingCard />
                  ) : chat.loading && chat.type !== "image" ? (
                    <ThreeDotsLoading />
                  ) : (
                    <div className="flex items-center">
                      {chat.type === "image" ? (
                        <>
                          <Image
                            src={chat.content || ""}
                            alt="AI generated chat pic"
                            width={192}
                            height={280}
                            className="relative"
                          />
                          {userEntity?.guest_flag && (
                            <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                              <div
                                className="cursor-pointer !no-underline px-4 py-2.5 rounded-lg font-medium !text-white bg-gradient-to-r bg-primary-color hover:opacity-90 transition-opacity"
                                onClick={() => {
                                  changeLoginDialogState(true);
                                }}
                              >
                                Sign Up For Free 😘
                              </div>
                            </div>
                          )}
                        </>
                      ) : chat.type === MESSAGE_TYPE.mind_reading ? (
                        <MindReadingCard
                          chat={chat}
                          currentChat={currentChat}
                        />
                      ) : chat.type === MESSAGE_TYPE.video &&
                        content?.status?.[1] &&
                        content?.video_url ? (
                        <div className="w-[240px] h-[360px] my-2">
                          <video
                            src={content.video_url}
                            poster={currentChat?.icon}
                            controls
                            className="w-full h-full"
                          ></video>
                        </div>
                      ) : chat.type === MESSAGE_TYPE.video_error ||
                        (chat.type === MESSAGE_TYPE.video &&
                          content?.status?.[3]) ? (
                        <GenerationFailure />
                      ) : chat.type === MESSAGE_TYPE.video &&
                        content?.status?.[0] ? (
                        <div></div>
                      ) : (
                        <Box sx={{ textAlign: "left" }}>
                          <MarkdownRenderer content={chat.content || ""} />
                        </Box>
                      )}
                    </div>
                  )}
                </Box>
                {!chat.is_need_subscribe &&
                  !chat.loading &&
                  chat.is_from_bot &&
                  ![
                    MESSAGE_TYPE.image,
                    MESSAGE_TYPE.mind_reading,
                    MESSAGE_TYPE.video,
                    MESSAGE_TYPE.video_error,
                  ].includes(chat.type || "") && (
                    <Tooltip title={t2("audioPlayPointsNotice")} arrow>
                      <IconButton
                        className="play-button"
                        size="small"
                        sx={{
                          color: "var(--primary-color)",
                          bgcolor: "rgba(255,255,255)",
                          borderRadius: 20,
                          mt: 1,
                          px: 1,
                          "&:hover": {
                            bgcolor: "rgba(255,255,255,0.8)",
                          },
                        }}
                        onClick={() => {
                          if (judgeMemberLevel(0) || judgeMemberLevel(1)) {
                            setOpenMemberModal(true);
                            return;
                          }
                          // if (judgeMemberLevel(1)) {
                          //   showToast(
                          //     t("audioPlayIsOnlyAvailableForDeluxeMembership"),
                          //     "error"
                          //   );
                          //   return;
                          // }
                          if (!judgeCreditPoint(5)) {
                            setOpenCoinsModal(true);
                            return;
                          }
                          playSingleMessageVoice(
                            chat.id || "",
                            chat.message_voice_url || ""
                          );
                        }}
                      >
                        {loadingMessageId === chat.id ? (
                          <CircularProgress size={14} color="inherit" />
                        ) : playingMessageId === chat.id && isPlaying ? (
                          <>
                            <PauseIcon sx={{ fontSize: 18 }} />
                            <span className="text-xs">Pause</span>
                          </>
                        ) : (
                          <>
                            <PlayArrowIcon sx={{ fontSize: 18 }} />
                            <span className="text-xs">Play</span>
                          </>
                        )}
                      </IconButton>
                    </Tooltip>
                  )}
              </Box>
            );
          })}
          <div ref={messagesEndRef} />
        </Box>

        {/* Input Area */}
        <Box>
          {suggestReplyInfo.loading &&
            (suggestReplyInfo.content.length > 0 ? (
              <SuggestedReplyBox
                content={suggestReplyInfo.content}
                isRequesting={suggestReplyInfo.isRequesting || false}
                onReset={async () => {
                  firebaseLogEvent(EVENT_ACTION_SUGGEST_REPLY_RESET);
                  const res = await querySuggestReply(chat_id || "");
                  if (!res) {
                    changeSuggestReplyInfo({
                      loading: false,
                    });
                    setOpenCoinsModal(true);
                  }
                }}
                onEdit={() => {
                  firebaseLogEvent(EVENT_ACTION_SUGGEST_REPLY_EDIT);
                  setMsg(suggestReplyInfo.content);
                }}
                onContentClick={() => {
                  firebaseLogEvent(EVENT_ACTION_SUGGEST_REPLY_SEND);
                  sendMessage({
                    content: suggestReplyInfo.content,
                    chat: chat_id || "",
                  });
                }}
                canSendMessage={canSendMessage}
              />
            ) : (
              <LoadingResponse />
            ))}
          <QuickOptCol
            chat_id={chat_id || ""}
            onRegenerate={() => {
              handleRegenerate(chat_id || "");
            }}
            canSendMessage={canSendMessage}
            setOpenCoinsModal={setOpenCoinsModal}
            isCanRegenerate={isCanRegenerate}
          />
          <Box
            sx={{
              display: "flex",
              gap: 2,
            }}
            className="sm:h-16 h-14 p-2"
          >
            <Box
              sx={{
                color: "white",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "rgba(255, 255, 255, 0.1)",
                borderRadius: 2,
                cursor: "pointer",
                px: 1,
              }}
              onClick={handlePhotoClick}
            >
              <ImageIcon sx={{ fontSize: 24 }} />
              <span className="text-sm ml-1 sm:block hidden">Ask</span>
              <KeyboardArrowDownIcon />
            </Box>
            <ChatNSFW />
            <Box
              sx={{
                display: "flex",
                flex: 1,
                bgcolor: "rgba(255,255,255,0.1)",
                borderRadius: 999,
              }}
              className="sm:p-2"
            >
              <InputBase
                sx={{
                  flex: 1,
                  color: "white",
                  "& .MuiInputBase-input": {
                    p: 0,
                  },
                }}
                className="pl-4"
                onChange={(e) => {
                  if (e.target.value.startsWith("Photo of ")) {
                    setMsg(e.target.value.slice(9));
                    setPhotoOf(true);
                  } else {
                    setMsg(e.target.value);
                  }
                }}
                value={msg}
                placeholder="Type a message"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleSendMessage({});
                  }
                }}
                startAdornment={
                  photoOf && (
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: "4px",
                        backgroundColor: "rgba(var(--primary-color-pure), 0.2)",
                        borderRadius: "50px",
                        mr: 1,
                      }}
                      className="sm:py-2 sm:px-3 py-1 px-1 sm:min-w-[120px] min-w-[82px]"
                    >
                      <Typography
                        sx={{
                          color: "var(--primary-color)",
                          fontSize: "14px",
                          flex: 1,
                        }}
                      >
                        Photo of
                      </Typography>
                      <IconButton
                        size="small"
                        sx={{
                          padding: 0,
                          color: "var(--primary-color)",
                          "&:hover": { backgroundColor: "transparent" },
                        }}
                        onClick={() => setPhotoOf(false)}
                      >
                        <CloseIcon sx={{ fontSize: 16 }} />
                      </IconButton>
                    </Box>
                  )
                }
                endAdornment={
                  <IconButton
                    sx={{ color: "white" }}
                    onClick={() => {
                      handleSendMessage({});
                    }}
                  >
                    <SendIcon
                      className={`${
                        msg && canSendMessage && "text-primary-color"
                      }`}
                    />
                  </IconButton>
                }
              />
            </Box>
          </Box>
        </Box>
      </Box>
      {/* Right Sidebar - Profile */}
      <RightSideBar
        showRightPanel={showRightPanel}
        currentChat={currentChat}
        pic_generate_loading={pic_generate_loading}
      />

      {openCoinsModal && (
        <UpgradeCoinsDialog
          open={openCoinsModal}
          onClose={() => setOpenCoinsModal(false)}
          onUpgrade={() => {
            setOpenCoinsModal(false);
            router.push(ROUTES.COINS, { locale });
          }}
          imgSrc={currentChat?.icon || ""}
        />
      )}
      {openMemberModal && (
        <UpgradeCoinsDialog
          open={openMemberModal}
          onClose={() => setOpenMemberModal(false)}
          onUpgrade={() => {
            setOpenMemberModal(false);
            router.push(ROUTES.SUBSCRIBE, { locale });
          }}
          imgSrc={currentChat?.icon || ""}
          desc1={t("UpgradeToDeluxeMembership")}
          desc2={t("toGenerateImagesInChat")}
          desc3={t("hurryUpUpgradeNowToGenerateImagesInChat")}
        />
      )}
      <PhotoMenu
        anchorEl={anchorEl}
        onClose={handleClose}
        onSelect={handleSelect}
        handleSendMindReading={handleSendMindReading}
        handleSendDancing={handleSendDancing}
      />
    </Box>
  );
}
