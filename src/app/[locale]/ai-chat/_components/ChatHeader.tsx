"use client";

import { useEffect, useState } from "react";
import { Box, Typography, IconButton, Avatar, Drawer } from "@mui/material";
import { useRouter } from "@/i18n/routing";
import {
  MenuOpen as MenuOpenIcon,
  VolumeUp as VolumeUpIcon,
  VolumeOff as VolumeOffIcon,
  ArrowBackIos as ArrowBackIosIcon,
} from "@mui/icons-material";

import { useSoulChatStore } from "@/stores/SoulChatStore";
import MoreOperate from "@/components/SoulChat/MoreOperate";
import useResponsive from "@/hooks/useResponsive";
import ChatHistory from "@/components/SoulChat/ChatHistory";
import AutoPlayGuide from "@/components/SoulChat/AutoPlayGuide";
import { AUTO_PLAY_KEY, ROUTES } from "@/constant";
import { useLocale } from "next-intl";
import { useUserStores } from "@/stores/UserStores";
import VideoAvatar from "@/components/VideoAvatar";

const ChatHeader = ({
  currentChat,
  chat_id,
  showRightPanel,
  setShowRightPanel,
}: {
  currentChat: any;
  chat_id: string | null;
  showRightPanel: boolean;
  setShowRightPanel: (show: boolean) => void;
}) => {
  const locale = useLocale();
  const router = useRouter();
  const { isSmallScreen, isXLargeScreen } = useResponsive();
  const { deleteChatHistory } = useSoulChatStore();
  const { userEntity } = useUserStores();

  const [open, setOpen] = useState(false);
  const [showDrawer, setShowDrawer] = useState(false);
  const [autoPlay, setAutoPlay] = useState(false);

  useEffect(() => {
    const autoPlay = localStorage.getItem(AUTO_PLAY_KEY) === "true";
    setAutoPlay(autoPlay);
  }, []);

  const judgeMemberLevel = (level: number) => {
    const { member_level = [] } = userEntity?.entitlement || {};
    return member_level[0] === level;
  };
  return (
    <>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          borderBottom: "1px solid rgba(255,255,255,0.1)",
        }}
        className="h-14 px-2 sm:px-4 sm:h-[75px]"
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <div className="xl:hidden block cursor-pointer">
            <ArrowBackIosIcon
              onClick={() => {
                router.push(ROUTES.HOME, { locale });
              }}
            />
          </div>
          {currentChat?.icon?.includes?.(".mp4") ? (
            <VideoAvatar
              src={currentChat?.icon}
              className="h-[40px] w-[40px] rounded-full object-cover"
              width={40}
              height={40}
            />
          ) : (
            <Avatar src={currentChat?.icon} key={currentChat?.icon} />
          )}

          <Typography variant="h6">
            {isSmallScreen
              ? currentChat?.title?.slice(0, 10)
              : currentChat?.title}
          </Typography>
        </Box>
        <Box>
          <IconButton
            sx={{
              mr: 1,
            }}
            disabled={!judgeMemberLevel(2)}
            onClick={() => {
              setOpen(true);
            }}
            title={autoPlay ? "Disable auto-play" : "Enable auto-play"}
          >
            {autoPlay ? (
              <VolumeUpIcon sx={{ color: "var(--primary-color)" }} />
            ) : (
              <VolumeOffIcon />
            )}
          </IconButton>
          <MoreOperate
            chat_id={chat_id || ""}
            icon={currentChat?.icon || ""}
            onClearChats={() => {
              deleteChatHistory(chat_id || "");
            }}
          />
          <IconButton
            sx={{ color: "white" }}
            onClick={() => {
              if (isXLargeScreen) {
                setShowDrawer(true);
              } else {
                setShowRightPanel(!showRightPanel);
              }
            }}
          >
            <MenuOpenIcon
              sx={{
                transform:
                  showRightPanel || showDrawer ? "rotate(180deg)" : "none",
                transition: "transform 0.3s",
              }}
            />
          </IconButton>
        </Box>
      </Box>
      <AutoPlayGuide open={open} setOpen={setOpen} setAutoPlay={setAutoPlay} />
      {/* Mobile Drawer */}
      <Drawer
        anchor="right"
        open={showDrawer}
        onClose={() => setShowDrawer(false)}
        sx={{
          "& .MuiDrawer-paper": {
            maxWidth: "280px",
            bgcolor: "var(--dark-bg)",
            border: "none",
            color: "white",
          },
        }}
      >
        <ChatHistory className="flex" onClose={() => setShowDrawer(false)} />
      </Drawer>
    </>
  );
};

export default ChatHeader;
