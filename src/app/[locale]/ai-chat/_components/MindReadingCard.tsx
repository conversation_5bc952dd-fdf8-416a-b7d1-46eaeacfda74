import Image from "next/image";
import React, { useState } from "react";
import { CircularProgress, IconButton } from "@mui/material";
import { Pause as PauseIcon, PlayArrow as PlayIcon } from "@mui/icons-material";

import { BotTemplate, Message } from "@/types";
import { MarkdownRenderer, firebaseLogEvent } from "@/utils/utils";
import { EVENT_ACTION_MIND_READING_CARD_CLICK } from "@/utils/event";
import { SOURCE_ID_KEY } from "@/constant";
import { useSoulChatStore } from "@/stores/SoulChatStore";
const MindReadingCard = ({
  chat,
  currentChat,
}: {
  chat: Partial<Message>;
  currentChat: Partial<
    Omit<BotTemplate, "photo_array"> & { photo_array: string[] }
  > | null;
}) => {
  const [isShow, setIsShow] = useState(false);
  const {
    playingMessageId,
    playSingleMessageVoice,
    isPlaying,
    loadingMessageId,
  } = useSoulChatStore();
  return (
    <>
      <div className="sm:h-[175px] h-[150px] relative rounded-xl overflow-hidden">
        <Image
          src="/letter.png"
          width={200}
          height={200}
          alt="letter"
          className="w-full h-full object-cover"
        />
        <div className="w-[40px] sm:w-[50px] absolute left-1/2 top-1/2 -translate-y-1/2 transform -translate-x-1/2 right-0 z-20">
          <Image
            src="/love.png"
            width={78}
            height={66}
            alt="letter"
            className="w-full object-cover"
          />
        </div>
        <div className="w-[25%] sm:h-[85px] h-[65px] absolute left-5 -rotate-12 top-5 rounded-lg overflow-hidden">
          <Image
            width={150}
            height={150}
            src={currentChat?.icon || ""}
            alt="letter"
            className="w-full h-full object-cover"
          />
        </div>
        <div
          style={{ fontFamily: "Indie Flower, cursive" }}
          className="text-black w-[60%] line-clamp-3 absolute top-5 right-3 sm:right-5 text-xs sm:text-sm leading-tight"
        >
          {chat.loading ? (
            "Reading, it takes about 3 seconds...."
          ) : (
            <MarkdownRenderer content={chat.content || ""} />
          )}
        </div>
        <div
          className={`text-white text-sm cursor-pointer bg-[#573EA2] p-2 rounded-3xl absolute bottom-2 left-1/2 transform -translate-x-1/2 font-bold`}
          onClick={() => {
            if (chat.loading) return;
            const sourceId = localStorage.getItem(SOURCE_ID_KEY);
            firebaseLogEvent(EVENT_ACTION_MIND_READING_CARD_CLICK, {
              page_path: EVENT_ACTION_MIND_READING_CARD_CLICK,
              source_id: sourceId,
              character_id: currentChat?.id,
              character_name: currentChat?.title,
              message_id: chat.id,
            });
            setIsShow(true);
          }}
        >
          {chat.loading ? (
            <div className="flex gap-2 cursor-not-allowed">
              <CircularProgress size={20} color="secondary" />
              <div>Reading Mind</div>
            </div>
          ) : (
            "Click to View"
          )}
        </div>
      </div>
      {isShow && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={() => setIsShow(false)}
        >
          <div
            className="bg-[#FFF9E6] rounded-lg shadow-xl max-w-lg w-full max-h-[95vh] overflow-y-auto p-6"
            style={{ fontFamily: "Indie Flower, cursive" }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center gap-3 mb-6 pb-4 border-b border-gray-300 relative">
              <div className="w-12 h-12 rounded-full overflow-hidden">
                <Image
                  width={48}
                  height={48}
                  src={currentChat?.icon || ""}
                  alt="avatar"
                  className="w-full h-full object-cover"
                />
              </div>
              <h2 className="text-xl font-bold text-blue-800 italic flex-1">
                {currentChat?.title}&apos;s Mind
              </h2>
              <IconButton
                onClick={() => {
                  if (loadingMessageId === chat.id) return;

                  playSingleMessageVoice(
                    chat.id || "",
                    chat.message_voice_url || ""
                  );
                }}
                sx={{
                  position: "absolute",
                  top: -8,
                  right: -8,
                  backgroundColor: "rgba(87, 62, 162, 0.9)",
                  color: "white",
                  width: 40,
                  height: 40,
                  "&:hover": {
                    backgroundColor: "rgba(87, 62, 162, 1)",
                  },
                }}
              >
                {loadingMessageId === chat.id ? (
                  <CircularProgress size={20} />
                ) : playingMessageId === chat.id && isPlaying ? (
                  <PauseIcon />
                ) : (
                  <PlayIcon />
                )}
              </IconButton>
            </div>

            <div className="space-y-4 mb-8">
              <div className="text-gray-800 text-sm leading-relaxed">
                <MarkdownRenderer content={chat.content || ""} />
              </div>
            </div>

            <div className="flex justify-center">
              <button
                onClick={() => {
                  setIsShow(false);
                }}
                className="bg-pink-500 hover:bg-pink-600 text-white px-8 py-2 rounded-full font-medium transition-colors"
              >
                Receive it
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default MindReadingCard;
