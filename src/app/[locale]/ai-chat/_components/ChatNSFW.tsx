"use client";

import NSFWSwitch from "@/components/Navbar/NSFWSwitch";
import { STORAGE_CHAT_NSFW } from "@/constant";
import { apiService } from "@/service/Api";
import { useUserStores } from "@/stores/UserStores";
import { useEffect, useState } from "react";

export default function ChatNSFW() {
  const { userEntity } = useUserStores();
  const [checkNSFW, setCheckNSFW] = useState<boolean>(
    userEntity?.check_nsfw || false
  );

  useEffect(() => {
    setCheckNSFW(localStorage.getItem(STORAGE_CHAT_NSFW) === "true");
  }, []);

  const handleChangeNSFW = async () => {
    const nsfw_status = !checkNSFW;
    setCheckNSFW(nsfw_status);
    if (!userEntity?.guest_flag) {
      apiService.changeNSFW(nsfw_status).then((resp) => {
        if (resp.code === 200) {
          localStorage.setItem(STORAGE_CHAT_NSFW, JSON.stringify(nsfw_status));
        }
      });
    }
  };

  return (
    <NSFWSwitch checkNSFW={checkNSFW} handleChangeNSFW={handleChangeNSFW} />
  );
}
