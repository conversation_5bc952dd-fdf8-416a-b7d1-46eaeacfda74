import { apiService } from "@/service/Api";
import { LeftPanel } from "./_components/LeftPanel";
import { RightPanel } from "./_components/RightPanel";
import { headers } from "next/headers";

export default async function CreateImage({
  searchParams,
}: {
  searchParams: Promise<{ keyword: string }>;
}) {
  const searchParam = await searchParams;
  const keyword = searchParam.keyword || "default";
  const headersList = await headers();
  const host = headersList.get("host");
  const protocol = process.env.NODE_ENV === "development" ? "http" : "https";
  const baseURL = `${protocol}://${host}`;
  const { data: ImageGeneratorConfig } = await apiService.getImageGeneratorList(
    {
      baseURL,
    }
  );

  return (
    <div className="lg:flex lg:flex-row-reverse">
      <RightPanel config={ImageGeneratorConfig} keyword={keyword} />
      <LeftPanel />
    </div>
  );
}
