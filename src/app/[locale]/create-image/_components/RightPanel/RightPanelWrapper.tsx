"use client";

import React from "react";
import { CircleX } from "lucide-react";
import { useTranslations } from "next-intl";

import { useCreateImageStore } from "@/stores/createImageStore";
import { Canvas } from "./Canvas";
import { ROUTES } from "@/constant";
import { UpgradeCoinsDialog } from "@/components/subscribe/UpgradeCoinsDialog";
import { useRouter } from "@/i18n/routing";

export const RightPanelWrapper = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const router = useRouter();
  const t = useTranslations("SubscribePage");
  const {
    isShowExample,
    setIsShowExample,
    setOpenCommonMemberModal,
    openCommonMemberModal,
  } = useCreateImageStore();
  return (
    <div className="bg-[#18181b] p-4 -mx-4 lg:h-screen lg:overflow-y-auto lg:flex-1">
      {isShowExample ? children : <Canvas />}
      {isShowExample && (
        <div
          className="absolute top-2 right-3"
          onClick={() => setIsShowExample(false)}
        >
          <CircleX className="cursor-pointer text-gray-d hover:text-white" />
        </div>
      )}
      {openCommonMemberModal && (
        <UpgradeCoinsDialog
          open={openCommonMemberModal}
          onClose={() => setOpenCommonMemberModal(false)}
          onUpgrade={() => {
            setOpenCommonMemberModal(false);
            router.push(ROUTES.SUBSCRIBE);
          }}
          imgSrc="/login_bg.webp"
          desc1={t("upgradeNow")}
          desc2={t("toGenerateImages")}
          desc3={t("hurryUpUpgradeNowToGenerateImages")}
        />
      )}
    </div>
  );
};
