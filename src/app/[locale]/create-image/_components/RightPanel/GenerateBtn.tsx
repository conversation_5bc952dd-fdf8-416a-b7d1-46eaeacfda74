"use client";

import React from "react";
import { Button } from "@mui/material";
import { useCreateImageStore } from "@/stores/createImageStore";

export const GenerateBtn = ({ cls }: { cls?: string }) => {
  const { setCreateImageState } = useCreateImageStore();
  return (
    <Button
      variant="contained"
      className={`!bg-primary-color !rounded-full !normal-case ${cls}`}
      onClick={() => {
        setCreateImageState({ isShake: true });
      }}
    >
      Generate Now
    </Button>
  );
};
