"use client";

import React from "react";
import { Pencil } from "lucide-react";

import { useCreateImageStore } from "@/stores/createImageStore";
import { InspirationItem } from "@/types/createImageType";

export const TryThisPrompt = ({ info }: { info: InspirationItem }) => {
  const { setCreateImageState } = useCreateImageStore();
  return (
    <button
      className="bg-[#3f3f46] hover:bg-[#52525a] py-2 px-4 rounded-lg flex items-center justify-center gap-2"
      onClick={() => {
        setCreateImageState({ prompt: info.text, isShake: true });
      }}
    >
      <Pencil size={16} />
      Try this prompt
    </button>
  );
};
