"use client";

import React from "react";
import Image from "next/image";

import { ROUTES } from "@/constant";
import { useRouter } from "@/i18n/routing";
import { ImageGeneratorConfig } from "@/types/createImageType";

export const Explore = ({ config }: { config: ImageGeneratorConfig }) => {
  const router = useRouter();
  const keys = Object.keys(config);

  return (
    <div className="max-w-screen-xl mx-auto">
      <h2 className="font-bold text-lg mt-10">Explore more</h2>
      <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 xl:grid-cols-4 xl:gap-6 mt-4">
        {keys.map(
          (key) =>
            config[key].label && (
              <div
                key={key}
                className="bg-[#303035] rounded-xl cursor-pointer p-3 hover:scale-105 transition-transform duration-300 ease-in-out"
                onClick={(e) => {
                  e.preventDefault();
                  router.push(
                    `/${ROUTES.CREATE_IMAGE}?keyword=${encodeURIComponent(key)}`
                  );
                }}
              >
                {config[key].imgUrlThumbnails && (
                  <div className="gap-1 p-1">
                    <Image
                      src={config[key].imgUrlThumbnails}
                      alt={"background image"}
                      className="w-full object-cover rounded"
                      width={402}
                      height={195}
                    />
                  </div>
                )}

                <div>
                  <h3 className="text-center text-sm font-bold pt-2">
                    {config[key].label}
                  </h3>
                </div>
              </div>
            )
        )}
      </div>
    </div>
  );
};
