"use client";

import React from "react";
import { useCreateImageStore } from "@/stores/createImageStore";
import Image from "next/image";

export const ShowImageList = () => {
  const { photo_list } = useCreateImageStore();
  return (
    <div className="grid grid-cols-2 gap-4">
      {photo_list.map((pic) => (
        <Image
          src={pic}
          alt="generated image"
          width={1024}
          height={1024}
          className="w-full h-full object-cover"
          key={pic}
        />
      ))}
    </div>
  );
};
