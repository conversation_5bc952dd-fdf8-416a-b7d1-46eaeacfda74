import React from "react";
import { GenerateBtn } from "./GenerateBtn";

const EasySteps = () => {
  return (
    <section className="my-8 rounded-xl bg-[#27272a] p-8 lg:py-12">
      <div className="mx-auto flex max-w-screen-xl flex-col gap-8 2xl:px-16">
        <h2 className="text-center text-3xl font-semibold">
          Get Started in 4 Easy Steps
        </h2>
        <div className="flex shrink-0 items-center gap-8">
          <div className="flex grow flex-col gap-8">
            <ol className="flex grid-cols-2 flex-col gap-4 2xl:grid">
              <li className="flex w-full items-start gap-4 order-1 2xl:order-1">
                <div className="flex size-6 shrink-0 items-center justify-center rounded-md bg-[#3f3f46] p-1 text-[#9e9ea6]">
                  1
                </div>
                <div className="text-lg">
                  Open GPT AI Image Creator and sign in into your account
                </div>
              </li>
              <li className="flex w-full items-start gap-4 order-2 2xl:order-3">
                <div className="flex size-6 shrink-0 items-center justify-center rounded-md bg-[#3f3f46] p-1 text-[#9e9ea6]">
                  2
                </div>
                <div className="text-lg">
                  Describe the visuals you want generate in the Prompt Box
                </div>
              </li>
              <li className="flex w-full items-start gap-4 order-3 2xl:order-2">
                <div className="flex size-6 shrink-0 items-center justify-center rounded-md bg-[#3f3f46] p-1 text-[#9e9ea6]">
                  3
                </div>
                <div className="text-lg">
                  Select your preferred art style from Styles
                </div>
              </li>
              <li className="flex w-full items-start gap-4 order-4 2xl:order-4">
                <div className="flex size-6 shrink-0 items-center justify-center rounded-md bg-[#3f3f46] p-1 text-[#9e9ea6]">
                  4
                </div>
                <div className="text-lg">
                  Hit &quot;Generate Image&quot; button
                </div>
              </li>
            </ol>
            <div className="text-center">
              <GenerateBtn />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EasySteps;
