import React from "react";
import { ImageGeneratorConfig } from "@/types/createImageType";
import { <PERSON> } from "./Banner";
import { Explore } from "./Explore";
import EasySteps from "./EasySteps";
import { InstantInspiration } from "./InstantInspiration";
import { ShowImageList } from "./ShowImageList";
import { RightPanelWrapper } from "./RightPanelWrapper";

export const RightPanel = ({
  config,
  keyword,
}: {
  config: ImageGeneratorConfig;
  keyword: string;
}) => {
  return (
    <RightPanelWrapper>
      {/* example */}
      <>
        <ShowImageList />
        <Banner config={config} keyword={keyword} />
        <Explore config={config} />
        <EasySteps />
        <InstantInspiration keyword={keyword} />
      </>
    </RightPanelWrapper>
  );
};
