"use client";

import React, { useState } from "react";
import { Di<PERSON>, Sparkles, Download } from "lucide-react";
import Image from "next/image";
import { Skeleton, Tooltip } from "@mui/material";
import Lightbox from "react-image-lightbox";
import "react-image-lightbox/style.css";

import { useCreateImageStore } from "@/stores/createImageStore";
import CardLoading from "@/components/CardLoading";
import { downloadFiles, downloadFilesAsZip } from "@/utils/utils";

export const Canvas = () => {
  const {
    hasStartGenerate,
    createImageState,
    generateImageLoading,
    photo_list,
  } = useCreateImageStore();
  const [loading, setLoading] = useState(true);
  const [retryCount, setRetryCount] = useState<number[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [photoIndex, setPhotoIndex] = useState(0);

  const handleImageError = (index: number) => {
    setRetryCount((prev) => {
      const newRetryCount = [...prev];
      newRetryCount[index] = (newRetryCount[index] || 0) + 1;
      return newRetryCount;
    });
  };

  return (
    <div className="flex-1 p-6 canvasBackground sm:min-h-screen sm:max-h-screen relative min-h-[30vh] max-h-[30vh] overflow-y-auto">
      {!hasStartGenerate ? (
        <div className="flex flex-col text-white justify-center items-center h-full w-full">
          <div className="mb-4">
            <Sparkles className="w-12 h-12" />
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold mb-2">Welcome to Your Canvas!</p>
            <p className="text-sm">
              Describe your character in the Prompt box or try the &quot;
              <Dices className="w-4 h-4 inline-block m-1" />
              &quot; button for inspiration.
            </p>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 sm:gap-6 gap-2 mt-6">
          {/* loading */}
          {generateImageLoading &&
            Array.from({ length: createImageState.imageCount }).map(
              (_, index) => (
                <div key={index}>
                  <CardLoading width={200} height={300} />
                </div>
              )
            )}

          {/* image list */}
          {photo_list.length > 0 && (
            <div className="absolute top-0 right-0">
              <Tooltip title="Download All">
                <Download
                  className="w-8 h-8 cursor-pointer text-white"
                  onClick={() => downloadFilesAsZip(photo_list)}
                />
              </Tooltip>
            </div>
          )}
          {photo_list.length > 0 &&
            photo_list.map((item, index) => {
              return (
                <div key={item} className="relative cursor-pointer group">
                  {loading && (
                    <Skeleton
                      variant="rounded"
                      width={200}
                      height={300}
                      sx={{ bgcolor: "grey.600" }}
                    />
                  )}
                  <Image
                    src={`${item}?retry=${retryCount[index] || 0}`}
                    alt="image"
                    className="w-full"
                    width={340}
                    height={512}
                    onLoad={() => setLoading(false)}
                    onError={() => handleImageError(index)}
                    loading="lazy"
                    onClick={() => {
                      setPhotoIndex(index);
                      setIsOpen(true);
                    }}
                  />
                  <div className="absolute top-2 right-2 bg-black bg-opacity-50 rounded-full p-1">
                    <Download
                      className="w-6 h-6 cursor-pointer text-white"
                      onClick={(e) => {
                        e.stopPropagation();
                        downloadFiles([item]);
                      }}
                    />
                  </div>
                </div>
              );
            })}
        </div>
      )}
      {isOpen && (
        <Lightbox
          mainSrc={photo_list[photoIndex]}
          nextSrc={photo_list[(photoIndex + 1) % photo_list.length]}
          prevSrc={
            photo_list[(photoIndex + photo_list.length - 1) % photo_list.length]
          }
          onCloseRequest={() => setIsOpen(false)}
          onMovePrevRequest={() =>
            setPhotoIndex(
              (photoIndex + photo_list.length - 1) % photo_list.length
            )
          }
          onMoveNextRequest={() =>
            setPhotoIndex((photoIndex + 1) % photo_list.length)
          }
        />
      )}
    </div>
  );
};
