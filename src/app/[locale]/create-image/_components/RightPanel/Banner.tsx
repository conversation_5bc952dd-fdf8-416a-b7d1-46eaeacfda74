import React from "react";
import Image from "next/image";

import { ImageGeneratorConfig } from "@/types/createImageType";
import { GenerateBtn } from "./GenerateBtn";

export const Banner = ({
  config,
  keyword,
}: {
  config: ImageGeneratorConfig;
  keyword: string;
}) => {
  const { heading, description, imgUrlPc, imgUrlMobile } =
    config?.[keyword] || {};
  return (
    <div className="bg-gradient-to-b from-[#27272a] to-[#1f1f23] rounded-lg">
      <div className="flex items-center max-w-screen-xl mx-auto xl:flex-row sm:flex-col flex-col-reverse">
        {/* Left Column */}
        {imgUrlMobile && (
          <div className="py-8 px-4">
            <Image
              src={imgUrlMobile}
              width={640}
              height={555}
              alt="bg"
              className="xl:hidden rounded-xl block max-h-[400px] object-contain"
              priority
            />
          </div>
        )}
        <div className="text-center p-4 xl:p-8 xl:text-left xl:py-10 xl:max-w-[50%] mt-2">
          <h1 className="lg:text-4xl text-3xl font-bold mb-6 lg:leading-[3.25rem]">
            {heading}
          </h1>
          <p className="lg:text-lg text-base text-[#e2e7f0aa] mb-8">
            {description}
          </p>
          <GenerateBtn cls="!text-base !py-3 !px-12 !rounded-full !bg-primary-color !normal-case" />
        </div>

        {/* Right Column */}
        {imgUrlPc && (
          <div className="xl:py-8">
            <Image
              src={imgUrlPc}
              width={640}
              height={555}
              alt="bg"
              className="hidden rounded-xl xl:block max-h-[400px] object-contain"
            />
          </div>
        )}
      </div>
    </div>
  );
};
