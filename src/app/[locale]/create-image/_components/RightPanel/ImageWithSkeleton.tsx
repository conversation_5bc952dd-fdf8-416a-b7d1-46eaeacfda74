"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import { Skeleton } from "@mui/material";
import { TryThisPrompt } from "./TryThisPrompt";

const ImageWithSkeleton = ({
  src,
  alt,
  width,
  height,
  text,
  item,
}: {
  src: string;
  alt: string;
  width: number;
  height: number;
  text: string;
  item: any;
}) => {
  const [imageSrc, setImageSrc] = useState(src);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;
  const retryDelay = 2000;

  // 处理图片加载失败并重试
  const handleImageError = () => {
    if (retryCount < maxRetries) {
      setTimeout(() => {
        setRetryCount((prev) => prev + 1);
        setImageSrc(`${src}?retry=${retryCount + 1}`);
      }, retryDelay);
    }
  };

  useEffect(() => {
    setImageSrc(src);
    setRetryCount(0);
  }, [src]);

  return (
    <div className="w-full relative flex flex-col justify-between items-center cursor-pointer group rounded-lg">
      <div className="overflow-hidden w-full">
        <Image
          key={imageSrc}
          src={imageSrc}
          alt={alt}
          width={width}
          height={height}
          className={`w-full shadow-lg transition-transform duration-300 transform group-hover:scale-105`}
          onError={handleImageError}
        />
      </div>
      <div className="p-3 absolute bottom-0 left-0 right-0 flex flex-col justify-end bg-gradient-to-t from-black to-transparent text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300 h-4/5">
        <p className="text-sm line-clamp-6 mb-4">{text}</p>
        <TryThisPrompt info={item} />
      </div>
    </div>
  );
};

export default ImageWithSkeleton;
