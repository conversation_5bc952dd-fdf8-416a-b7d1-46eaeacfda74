import { apiService } from "@/service/Api";
import React from "react";
import { headers } from "next/headers";
import ImageWithSkeleton from "./ImageWithSkeleton";

export const InstantInspiration = async ({ keyword }: { keyword: string }) => {
  const headersList = await headers();
  const host = headersList.get("host");
  const protocol = process.env.NODE_ENV === "development" ? "http" : "https";
  const baseURL = `${protocol}://${host}`;
  const { data: instantInspirationList } =
    await apiService.getInstantInspirationConfig({
      baseURL,
      keyword,
    });
  return (
    <div className="max-w-screen-xl mx-auto p-4 lg:p-12">
      <h2 className="text-3xl font-bold text-center my-10">
        Try These Prompts for Instant Inspiration
      </h2>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
        {instantInspirationList?.slice(0, 8).map((item) => (
          <ImageWithSkeleton
            src={item.imgUrl}
            alt={item.text}
            width={500}
            height={300}
            text={item.text}
            item={item}
            key={item.imgUrl}
          />
        ))}
      </div>
    </div>
  );
};
