"use client";

import React, { useState } from "react";
import { CircleDollarSign, Di<PERSON> } from "lucide-react";
import { useTranslations } from "next-intl";
import { Button } from "@mui/material";
import { useCreateImageStore } from "@/stores/createImageStore";
import { promptTextList } from "@/config/promptList";
import { useRouter } from "@/i18n/routing";
import { useUserStores } from "@/stores/UserStores";
import { useCreateStores } from "@/stores/CreateStores";

function GenerateButton() {
  const {
    createImageState,
    setCreateImageState,
    generateImage,
    generateImageLoading,
  } = useCreateImageStore();
  const { uploadFaceCreateEntity } = useCreateStores();
  const { imageCount } = createImageState;
  const { userEntity } = useUserStores();
  const router = useRouter();
  const t = useTranslations("SubscribePage");

  const [lastNumber, setLastNumber] = useState<number | null>(null);
  const generateUniqueRandom = () => {
    let newNumber: number;
    do {
      newNumber = Math.floor(Math.random() * 10);
    } while (newNumber === lastNumber);

    setLastNumber(newNumber);
    return newNumber;
  };

  return (
    <div className="flex gap-4 items-center">
      <div
        className="p-2 rounded-full bg-slate-300/20 cursor-pointer hover:bg-slate-300/30 transition-colors"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          const randomNumber = generateUniqueRandom();
          setCreateImageState({
            prompt: promptTextList[randomNumber],
            isShake: true,
          });
        }}
      >
        <Dices />
      </div>
      <Button
        variant="contained"
        fullWidth
        className="flex hover:opacity-90 py-3 !normal-case text-[15px] !rounded-full"
        style={{
          backgroundColor: "var(--primary-color)",
        }}
        disabled={generateImageLoading}
        onClick={() => {
          if (createImageState.prompt) {
            const faceModelId =
              createImageState.selectedStyle?.internalName === "realistic_pulid"
                ? uploadFaceCreateEntity?.faceModel?.id
                : null;
            generateImage({
              faceModelId,
            });
          } else {
            setCreateImageState({ isShake: true });
          }
        }}
      >
        {generateImageLoading ? (
          <span>Generating...</span>
        ) : (
          <>
            <span>Generate Image</span>
            <span className="mx-2">|</span>
            <span className="ml-2 flex gap-1 items-center">
              {imageCount * 6} <CircleDollarSign size={14} />{" "}
            </span>
          </>
        )}
      </Button>
    </div>
  );
}

export default GenerateButton;
