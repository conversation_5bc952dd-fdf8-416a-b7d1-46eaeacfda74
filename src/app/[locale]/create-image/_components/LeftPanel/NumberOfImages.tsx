"use client";

import React, { useState } from "react";
import { useCreateImageStore } from "@/stores/createImageStore";
import { UpgradeCoinsDialog } from "@/components/subscribe/UpgradeCoinsDialog";
import { ROUTES } from "@/constant";
import { useUserStores } from "@/stores/UserStores";
import { useTranslations } from "next-intl";
import { useRouter } from "@/i18n/routing";

export const NumberOfImages = () => {
  const t = useTranslations("SubscribePage");
  const {
    createImageState,
    setCreateImageState,
    generateImageLoading,
    setOpenCommonMemberModal,
  } = useCreateImageStore();
  const { userEntity } = useUserStores();
  const router = useRouter();

  const [openMemberModal, setOpenMemberModal] = useState(false);
  return (
    <>
      <div className="mb-5">
        <h3 className="text-gray-300 text-[15px] font-medium mb-3">
          Number of Images
        </h3>
        <div className="grid grid-cols-4 gap-2">
          {[1, 2, 4, 8].map((num) => (
            <button
              key={num}
              disabled={generateImageLoading}
              className={`relative py-2 rounded text-[14px] ${
                createImageState.imageCount === num
                  ? "text-white"
                  : "bg-[#27272a] text-gray-300 hover:bg-[#3a3a3a]"
              }`}
              style={{
                backgroundColor:
                  createImageState.imageCount === num
                    ? "var(--primary-color)"
                    : undefined,
              }}
              onClick={() => {
                if (
                  num === 2 &&
                  (userEntity?.entitlement?.member_level?.[0] ?? 0) === 0
                ) {
                  setOpenCommonMemberModal(true);
                  return;
                }
                if (
                  num > 2 &&
                  [0, 1].includes(
                    userEntity?.entitlement?.member_level?.[0] ?? 0
                  )
                ) {
                  setOpenMemberModal(true);
                  return;
                }
                setCreateImageState({ imageCount: num });
              }}
            >
              {num}
              {num > 1 && (
                <div className="absolute -top-2 -right-1 bg-red-500 text-white text-xs px-1 rounded-md min-w-[25px] text-center">
                  Pro
                </div>
              )}
            </button>
          ))}
        </div>
      </div>
      {openMemberModal && (
        <UpgradeCoinsDialog
          open={openMemberModal}
          onClose={() => setOpenMemberModal(false)}
          onUpgrade={() => {
            setOpenMemberModal(false);
            router.push(ROUTES.SUBSCRIBE);
          }}
          imgSrc="/login_bg.webp"
          desc1={t("UpgradeToDeluxeMembership")}
          desc2={t("toGenerateMoreImages")}
          desc3={t("hurryUpUpgradeNowToGenerateMoreImages")}
        />
      )}
    </>
  );
};
