"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import { Skeleton } from "@mui/material";

import { useCreateImageStore } from "@/stores/createImageStore";
import { apiService } from "@/service/Api";
import { StyleItem } from "@/types/createImageType";
import LooksLike from "./LooksLike";

export const Styles = () => {
  const { createImageState, setCreateImageState } = useCreateImageStore();
  const [styleList, setStyleList] = useState([] as StyleItem[]);

  useEffect(() => {
    apiService.getStyleList().then((res) => {
      if (res.data) {
        setStyleList(res.data);
        setCreateImageState({ selectedStyle: res.data[0] });
      }
    });
  }, []);

  return (
    <div className="my-5">
      <h3 className="text-gray-300 text-[15px] font-medium mb-3">Styles</h3>
      <div className="grid grid-cols-3 gap-1">
        {styleList?.length > 0
          ? styleList.map((style) => (
              <div
                key={style.displayName}
                className={`relative cursor-pointer rounded-lg`}
                onClick={() => setCreateImageState({ selectedStyle: style })}
              >
                <div
                  className={`relative cursor-pointer rounded-lg overflow-hidden p-1 ${
                    createImageState.selectedStyle?.displayName ===
                    style.displayName
                      ? "ring-2 ring-[var(--primary-color)]"
                      : ""
                  }`}
                >
                  <Image
                    src={style.exampleImage}
                    alt={style.displayName}
                    width={160}
                    height={160}
                    className="w-full aspect-square object-cover rounded-lg"
                  />
                </div>
                <div className="w-full pt-1 text-center text-[13px] text-[#c1c1c5]">
                  {style.displayName}
                </div>
              </div>
            ))
          : Array.from({ length: 3 }).map((_, index) => (
              <div
                key={index}
                className="relative cursor-pointer rounded-lg overflow-hidden p-1"
              >
                <div className="relative cursor-pointer rounded-lg overflow-hidden p-1">
                  <Skeleton
                    variant="rectangular"
                    className="w-full aspect-square rounded-lg lg:!h-[90px] !h-[70px]"
                    sx={{
                      bgcolor: "rgba(255, 255, 255, 0.1)",
                      transform: "none",
                    }}
                  />
                </div>
                <div className="w-full pt-1 text-center">
                  <Skeleton
                    variant="text"
                    width={75}
                    height={24}
                    sx={{
                      bgcolor: "rgba(255, 255, 255, 0.1)",
                      transform: "none",
                      margin: "0 auto",
                    }}
                  />
                </div>
              </div>
            ))}
      </div>
      {createImageState.selectedStyle?.internalName === "realistic_pulid" && (
        <LooksLike />
      )}
    </div>
  );
};
