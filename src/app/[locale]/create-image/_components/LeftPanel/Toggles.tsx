"use client";

import React from "react";
import { CircleHelp } from "lucide-react";
import { Switch, Tooltip } from "@mui/material";

import { useCreateImageStore } from "@/stores/createImageStore";

export const Toggles = () => {
  const { createImageState, setCreateImageState } = useCreateImageStore();
  return (
    <div className="grid grid-cols-1 lg:gap-3 lg:mb-5">
      <div className="flex items-center justify-between rounded-lg">
        <div className="flex items-center gap-1.5">
          <span className="text-gray-300 text-[14px]">Auto-Enhance</span>
          <Tooltip
            title={
              <div>
                <div className="font-bold text-base mb-1">
                  Automatic Prompt Refinement
                </div>
                <div className="text-sm opacity-90">
                  This, when enabled, will improve your prompt with the help of
                  AI in the background
                </div>
              </div>
            }
            arrow
            placement="top"
          >
            <div>
              <CircleHelp size={14} className="text-[#c1c1c5] cursor-help" />
            </div>
          </Tooltip>
        </div>
        <Switch
          checked={createImageState.autoEnhance}
          onChange={(e) =>
            setCreateImageState({ autoEnhance: e.target.checked })
          }
          size="medium"
          sx={{
            "& .MuiSwitch-switchBase.Mui-checked": {
              color: "var(--primary-color)",
              "&:hover": {
                backgroundColor: "rgba(var(--primary-color-rgb), 0.04)",
              },
            },
            "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
              backgroundColor: "var(--primary-color)",
            },
          }}
        />
      </div>
      <div className="flex items-center justify-between rounded-lg">
        <div className="flex items-center gap-1.5">
          <span className="text-gray-300 text-[14px]">Safe Mode</span>
          <Tooltip
            title={
              <div>
                <div className="font-bold text-base mb-1">
                  Keeping Content Appropriate and Safe
                </div>
                <div className="text-sm opacity-90">
                  Safe Mode, when enabled, will ensure that you won&apos;t get
                  any unsafe/inappropriate content in your image
                </div>
              </div>
            }
            arrow
            placement="top"
          >
            <div>
              <CircleHelp size={14} className="text-[#c1c1c5] cursor-help" />
            </div>
          </Tooltip>
        </div>
        <Switch
          checked={createImageState.safeMode}
          onChange={(e) => setCreateImageState({ safeMode: e.target.checked })}
          size="medium"
          sx={{
            "& .MuiSwitch-switchBase.Mui-checked": {
              color: "var(--primary-color)",
              "&:hover": {
                backgroundColor: "rgba(var(--primary-color-rgb), 0.04)",
              },
            },
            "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
              backgroundColor: "var(--primary-color)",
            },
          }}
        />
      </div>
    </div>
  );
};
