import React, { useEffect, useRef, useState } from "react";
import { useTranslations } from "next-intl";

import FaceUpload from "@/components/SoulChat/FaceUpload";
import AddButton from "@/components/SoulChat/AddButton";
import { useCreateStores } from "@/stores/CreateStores";
import { useUserStores } from "@/stores/UserStores";
import { UpgradeCoinsDialog } from "@/components/subscribe/UpgradeCoinsDialog";
import { ROUTES } from "@/constant";
import { useRouter } from "@/i18n/routing";
import Image from "next/image";

const LooksLike = () => {
  const faceUploadRef = useRef<HTMLInputElement>(null);
  const {
    faces,
    setUploadFaceCreateEntity,
    uploadFaceCreateEntity,
    fetchFaces,
  } = useCreateStores();
  const { userEntity } = useUserStores();
  const t = useTranslations("SubscribePage");
  const router = useRouter();
  const [openMemberModal, setOpenMemberModal] = useState(false);

  useEffect(() => {
    fetchFaces();
  }, []);

  return (
    <div>
      <div className="flex flex-row  justify-start items-center bg-transparent  bg-dark-bg ml-[10px] mt-[8px]">
        <div className="text-white font-bold text-[20px] select-none mb-2">
          Looks like
        </div>
      </div>
      <div className="flex flex-row gap-1 overflow-x-auto whitespace-nowrap pb-2 scrollbar-hide">
        <div className="flex-shrink-0">
          <AddButton
            isPro={true}
            onClick={() => {
              if (userEntity?.entitlement?.member_level?.[0] !== 2) {
                setOpenMemberModal(true);
                return;
              }
              faceUploadRef.current?.click();
            }}
          />
          <FaceUpload ref={faceUploadRef} />
        </div>
        {faces &&
          faces.map((face, index) => {
            const selected = index == uploadFaceCreateEntity?.faceModel?.index;
            return (
              <div
                key={face.id}
                className="flex-shrink-0 w-[69px] h-[69px] ml-[10px] scroll-snap-align-start"
                onClick={() => setUploadFaceCreateEntity(face)}
              >
                <Image
                  width={69}
                  height={69}
                  className={`border-[2px] border-${
                    selected ? "primary-color" : "gray"
                  } rounded-full w-[69px] h-[69px] object-cover`}
                  src={face.resize_url}
                  alt="face"
                />
              </div>
            );
          })}
      </div>
      {openMemberModal && (
        <UpgradeCoinsDialog
          open={openMemberModal}
          onClose={() => setOpenMemberModal(false)}
          onUpgrade={() => {
            setOpenMemberModal(false);
            router.push(ROUTES.SUBSCRIBE);
          }}
          imgSrc=""
          desc1={t("UpgradeToDeluxeMembership")}
          desc2={t("toGenerateImagesInChat")}
          desc3={t("hurryUpUpgradeNowToGenerateImagesInChat")}
        />
      )}
    </div>
  );
};

export default LooksLike;
