"use client";

import React, { useEffect, useRef } from "react";
import { ChevronDown } from "lucide-react";
import { Collapse, TextField } from "@mui/material";

import { useCreateImageStore } from "@/stores/createImageStore";

export const Prompt = () => {
  const { createImageState, setCreateImageState } = useCreateImageStore();
  const { isShake, prompt, isNegativePromptOpen, negativePrompt } =
    createImageState;
  const promptInputRef = useRef<HTMLDivElement>(null);
  const textFieldStyles = {
    "& .MuiOutlinedInput-root": {
      color: "white",
      fontSize: "14px",
      "& fieldset": {
        borderColor: "#3a3a3a",
      },
      "&:hover fieldset": {
        borderColor: "#4a4a4a",
      },
      "&.Mui-focused fieldset": {
        borderColor: "var(--primary-color)",
      },
    },
  };
  useEffect(() => {
    if (isShake) {
      promptInputRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
      setTimeout(() => {
        setCreateImageState({ isShake: false });
      }, 1000);
    }
  }, [isShake]);
  return (
    <>
      <div ref={promptInputRef}>
        {/* Prompt Header */}
        <div className="flex justify-between items-center mb-3">
          <span className="text-gray-300 text-[15px] font-medium">Prompt</span>
          <button
            className="text-[#c1c1c5] text-[13px] hover:bg-[#393940] rounded-full px-2 py-1"
            onClick={() => {
              setCreateImageState({ prompt: "" });
            }}
          >
            Clear Prompt
          </button>
        </div>

        {/* Prompt Input */}
        <TextField
          multiline
          rows={3}
          fullWidth
          placeholder="Write what you would like to see in your image. Tip: Start your prompt with a subject (e.g. A fantasy sorcerer standing in a mystical library...)"
          value={prompt}
          onChange={(e) => setCreateImageState({ prompt: e.target.value })}
          className={`rounded-lg mb-5 ${isShake ? "animate-shake" : ""}`}
          variant="outlined"
          sx={{
            "& .MuiOutlinedInput-root": {
              color: "white",
              fontSize: "14px",
              border: "2px solid transparent",
              ...(isShake && {
                border: "2px solid var(--primary-color)",
              }),
              "& fieldset": {
                borderColor: "#3a3a3a",
              },
              "&:hover fieldset": {
                borderColor: "#4a4a4a",
              },
              "&.Mui-focused fieldset": {
                borderColor: "var(--primary-color)",
              },
            },
          }}
        />
      </div>
      {/* Negative Prompt */}
      <div className="mt-2 lg:my-5">
        <div
          className="flex items-center text-gray-300 cursor-pointer mb-2 justify-between"
          onClick={() =>
            setCreateImageState({
              isNegativePromptOpen: !isNegativePromptOpen,
            })
          }
        >
          <div className="flex">
            <span className="text-[15px] font-medium">Negative Prompt</span>
            <div
              className={`transform transition-transform duration-300 ${
                isNegativePromptOpen ? "rotate-180" : ""
              }`}
            >
              <ChevronDown />
            </div>
          </div>
          <button
            className="text-[#c1c1c5] text-[13px] hover:bg-[#393940] rounded-full px-2 py-1"
            onClick={(e) => {
              e.stopPropagation();
              setCreateImageState({ negativePrompt: "" });
            }}
          >
            Clear Prompt
          </button>
        </div>
        <Collapse in={isNegativePromptOpen}>
          <TextField
            multiline
            rows={3}
            fullWidth
            placeholder="Write what you don't want to see in your image"
            value={negativePrompt}
            onChange={(e) =>
              setCreateImageState({ negativePrompt: e.target.value })
            }
            className="rounded-lg"
            variant="outlined"
            sx={textFieldStyles}
          />
        </Collapse>
      </div>
    </>
  );
};
