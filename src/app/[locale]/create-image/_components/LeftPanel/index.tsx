import React from "react";

import { Prompt } from "./Prompt";
import { Toggles } from "./Toggles";
import { Styles } from "./Styles";
import { NumberOfImages } from "./NumberOfImages";
import { CreativitySlider } from "./CreativitySlider";
import GenerateButton from "./GenerateButton";

export const LeftPanel = () => {
  return (
    <div className="lg:w-[380px] lg:h-screen lg:pt-0 w-full flex flex-col relative pt-4">
      <div className="lg:flex-1 lg:overflow-auto lg:p-6 lg:pr-10 lg:pb-24">
        <Prompt />
        <Toggles />
        <Styles />
        <NumberOfImages />
        <CreativitySlider />
      </div>

      <div className="lg:absolute lg:bottom-0 lg:-left-4 lg:right-4 lg:z-10 lg:p-6 p-3 bg-[#292c34] border-t border-[#3F3F46BF]">
        <GenerateButton />
      </div>
    </div>
  );
};
