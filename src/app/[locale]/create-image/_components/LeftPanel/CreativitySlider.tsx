"use client";

import { useCreateImageStore } from "@/stores/createImageStore";
import { <PERSON>lide<PERSON>, Tooltip } from "@mui/material";
import { CircleHelp } from "lucide-react";
import React from "react";

const TEXT_COLOR = "#c1c1c5";

export const CreativitySlider = () => {
  const { createImageState, setCreateImageState } = useCreateImageStore();

  return (
    <div className="lg:mb-6">
      <div className="flex items-center gap-1.5 mb-3">
        <h3 className={`text-[${TEXT_COLOR}] text-[15px] font-medium`}>
          Creativity
        </h3>
        <Tooltip
          title={
            <div>
              <div className="font-bold text-base mb-1">
                Tailored Outputs with Adjustable Creativity
              </div>
              <div className="text-sm opacity-90">
                Higher creativity produces more diverse images, but they may not
                exactly match this character. We recommend to start with default
                value, and later adjust if needed.
              </div>
            </div>
          }
          arrow
          placement="top"
        >
          <div>
            <CircleHelp
              size={14}
              className={`text-[${TEXT_COLOR}] cursor-help`}
            />
          </div>
        </Tooltip>
      </div>
      <div className="flex items-center gap-4">
        <div className={`text-[${TEXT_COLOR}]`}>0</div>
        <div className="flex-1">
          <Slider
            value={createImageState.creativity}
            onChange={(_, value) =>
              setCreateImageState({ creativity: value as number })
            }
            step={0.1}
            min={0}
            max={1}
            marks
            sx={{
              color: "var(--primary-color)",
              "& .MuiSlider-thumb": {
                backgroundColor: "var(--primary-color)",
              },
              "& .MuiSlider-track": {
                backgroundColor: "var(--primary-color)",
              },
            }}
          />
        </div>
        <div className={`text-[${TEXT_COLOR}]`}>1</div>
        <div
          className={`text-[${TEXT_COLOR}] min-w-[32px] border border-[#38383f] bg-[#27272a] p-0.5 px-2 rounded`}
        >
          {createImageState.creativity.toFixed(1)}
        </div>
      </div>
    </div>
  );
};
