"use client";
import { useUserStores } from "@/stores/UserStores";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCopy } from "@fortawesome/free-solid-svg-icons";
import React, { useEffect, useState } from "react";
import { faGoogle } from "@fortawesome/free-brands-svg-icons";
import { Tabs, Tab, Box } from "@mui/material";
import { styled } from "@mui/material/styles";
import BackButton from "@/components/ChatDetail/BackButton";
import { Bookmark } from "./_components";
import { TabType } from "./type";
import { copy, TABS } from "./constant";
import Chatbot from "./_components/Chatbot";
import Setting from "./_components/Setting";
import { Preferences } from "./_components/Preferences";

const StyledTab = styled(Tab)({
  color: "#64748B",
  backgroundColor: "transparent",
  margin: "0 8px",
  padding: "8px 16px",
  minHeight: "40px",
  textTransform: "none",
  fontSize: "1rem",
  borderRadius: "12px",
  "&:hover": {
    backgroundColor: "rgba(var(--primary-color-pure), 0.1)",
    color: "var(--primary-color)",
  },
  "&.Mui-selected": {
    color: "#FFF",
    backgroundColor: "var(--primary-color)",
  },
});

export default function AccountInfo() {
  const [selectedTab, setSelectedTab] = useState<TabType>("Chatbot");

  const { userEntity, fetchUserInfo, resetRefresh } = useUserStores();

  useEffect(() => {
    fetchUserInfo();
    return () => {
      resetRefresh();
    };
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: TabType) => {
    setSelectedTab(newValue);
  };

  return (
    <div className="flex flex-col sm:p-6 min-h-screen">
      {userEntity?.openid && (
        <>
          {/* Header Section */}
          <div className="sm:hidden flex items-center my-2">
            <BackButton />
          </div>

          <h1 className="text-2xl sm:text-4xl font-bold text-primary-color mb-4 sm:mb-6">
            Profile
          </h1>

          {/* User Profile Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6 sm:mb-8">
            <div className="flex items-center mb-4 sm:mb-0">
              <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-primary-color flex items-center justify-center">
                <FontAwesomeIcon
                  icon={faGoogle}
                  className="text-xl sm:text-2xl"
                  color="white"
                />
              </div>
              <div className="ml-4 flex items-center">
                <div className="text-white text-base sm:text-xl break-all">
                  User#{userEntity?.openid || ""}
                </div>
                <FontAwesomeIcon
                  onClick={() => copy(userEntity?.openid || "")}
                  className="ml-4 mr-[10px] cursor-pointer text-primary-color"
                  size={"sm"}
                  color={"white"}
                  icon={faCopy}
                />
              </div>
            </div>
          </div>

          {/* Tabs */}
          <Box
            sx={{
              maxWidth: "100%",
              mb: { xs: 3, sm: 4 },
            }}
          >
            <Tabs
              value={selectedTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons={false}
              sx={{
                "& .MuiTabs-indicator": {
                  display: "none",
                },
                minHeight: "40px",
                backgroundColor: "transparent",
              }}
            >
              {TABS.map((tab) => (
                <StyledTab
                  key={tab.value}
                  value={tab.value}
                  label={
                    <div className="flex items-center gap-2">
                      <tab.icon />
                      <span>{tab.label}</span>
                    </div>
                  }
                />
              ))}
            </Tabs>
          </Box>
          <>
            <Chatbot activeTab={selectedTab} />
            <Setting activeTab={selectedTab} />
            <Bookmark activeTab={selectedTab} />
            <Preferences activeTab={selectedTab} />
          </>
        </>
      )}
    </div>
  );
}
