import {
  SmartToy as SmartToyIcon,
  Bookmark as BookmarkIcon,
  Settings as SettingsIcon,
  SettingsSuggest as PreferencesIcon,
} from "@mui/icons-material";
import { Box } from "@mui/material";
import { TabPanelProps } from "./type";
import { showToast } from "@/utils/toast";

export const TABS = [
  { value: "Chatbot", label: "Chatbot", icon: SmartToyIcon },
  { value: "Setting", label: "Setting", icon: SettingsIcon },
  { value: "Bookmark", label: "Bookmark", icon: BookmarkIcon },
  { value: "Preferences", label: "Preferences", icon: PreferencesIcon },
];

export function TabPanel({ children, value, activeTab }: TabPanelProps) {
  return (
    <div role="tabpanel" hidden={value !== activeTab}>
      {value === activeTab && <Box>{children}</Box>}
    </div>
  );
}

export const copy = (text: string) => {
  navigator.clipboard.writeText(text);
  showToast("Copy Success", "success");
};
