import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from "@mui/material";

const DeleteAccountDialog = ({
  showDeleteAccountDialog,
  setShowDeleteAccountDialog,
  handleDeleteAccount,
  isLoading,
}: {
  showDeleteAccountDialog: boolean;
  setShowDeleteAccountDialog: (value: boolean) => void;
  handleDeleteAccount: () => void;
  isLoading: boolean;
}) => {
  return (
    <Dialog
      open={showDeleteAccountDialog}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: "#1A1B1E",
          color: "white",
          borderRadius: "16px",
          backgroundImage:
            "linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.4)",
          position: "relative",
        },
      }}
    >
      <DialogTitle
        sx={{
          color: "white",
          textAlign: "center",
          fontSize: { xs: 20, sm: 24 },
          fontWeight: 600,
          pt: 4,
          pb: 2,
        }}
      >
        Delete Account
      </DialogTitle>
      <DialogContent sx={{ px: { xs: 2, sm: 4 }, pb: 1 }}>
        <div className="text-white/80 space-y-4">
          <p className="mb-4 text-base leading-relaxed">
            Are you sure you want to delete your account? This action cannot be
            undone and all your data will be permanently removed.
          </p>
        </div>
      </DialogContent>
      <DialogActions sx={{ pb: 2, gap: 1, justifyContent: "center" }}>
        <Button
          onClick={() => setShowDeleteAccountDialog(false)}
          variant="outlined"
          sx={{
            color: "white",
            borderColor: "white",
            py: 1,
            px: 4,
            borderRadius: "8px",
            textTransform: "none",
            fontSize: "16px",
            "&:hover": {
              borderColor: "white",
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleDeleteAccount}
          variant="contained"
          disabled={isLoading}
          sx={{
            backgroundColor: "#EF4444",
            color: "white",
            py: 1,
            px: 4,
            borderRadius: "8px",
            textTransform: "none",
            fontSize: "16px",
            "&:hover": {
              backgroundColor: "#DC2626",
            },
          }}
        >
          {isLoading ? "Processing..." : "Delete Account"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DeleteAccountDialog;
