"use client";

import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { Dialog, IconButton } from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";
import ProfileCustomSkeleton from "@/components/Skeleton/ProfileCustomSkeleton";
import { PUBLIC_STATUS, ROUTES, SOULMATE_PAGE_SIZE } from "@/constant";
import { showToast } from "@/utils/toast";
import { useUserStores } from "@/stores/UserStores";
import { apiService } from "@/service/Api";
import Pagination from "@/components/Pagination";
import { useRouter } from "@/i18n/routing";

import { TabPanel } from "../constant";
import { TabType } from "../type";
import CustomCharacterCreateForm from "@/components/CreatePage/CustomCharacterCreateForm";

const Chatbot = ({ activeTab }: { activeTab: TabType }) => {
  const chatBotRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const [isPageLoading, setIsPageLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<{
    show: boolean;
    templateId: string | null;
  }>({
    show: false,
    templateId: null,
  });
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [defaultData, setDefaultData] = useState({} as any);
  const {
    customTemplate: { list, totalCount },
    getCustomTemplate,
  } = useUserStores();

  useEffect(() => {
    const fetchData = async () => {
      setIsPageLoading(true);
      await getCustomTemplate(currentPage);
      setIsPageLoading(false);
    };

    fetchData();
  }, [currentPage, getCustomTemplate]);
  const scrollToTop = () => {
    chatBotRef.current?.scrollIntoView({ behavior: "instant" });
  };
  const handleJumpToChat = ({ id }: { id: string }) => {
    apiService.createChat({ bot_template: id }).then((res) => {
      if (res.code === 200 && res.data.id) {
        router.push(`${ROUTES.AI_CHAT}?id=${res.data.id}`);
      }
    });
  };
  return (
    <>
      <TabPanel value="Chatbot" activeTab={activeTab}>
        <div ref={chatBotRef}></div>
        {list.length === 0 && (
          <div className="flex flex-col items-start gap-4 mb-6">
            <button
              onClick={() => router.push(ROUTES.CREATE)}
              className="px-6 py-3 rounded-full bg-gradient-to-r from-primary-color to-secondary-color text-white hover:opacity-90 transition-opacity"
            >
              Create your AI
            </button>
          </div>
        )}
        {isPageLoading ? (
          <ProfileCustomSkeleton />
        ) : (
          <>
            <div className="grid gap-4 grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {list?.map((template) => {
                const { title, description, icon, character_tags } = template;

                return (
                  <div
                    key={template.id}
                    className="relative bg-[#1E293B]/40 backdrop-blur-sm rounded-2xl overflow-hidden cursor-pointer"
                    onClick={() => {
                      if (template.publish_status === PUBLIC_STATUS.APPROVED) {
                        handleJumpToChat({ id: template.id });
                      } else {
                        showToast(
                          `This chatbot is ${
                            template.publish_status === PUBLIC_STATUS.PENDING
                              ? "under review"
                              : "rejected"
                          }`,
                          "error"
                        );
                      }
                    }}
                  >
                    {/* Status badges */}
                    {template.publish_status === PUBLIC_STATUS.PENDING && (
                      <div className="absolute top-3 right-1 px-3 bg-emerald-500/30 backdrop-blur-sm border border-emerald-500/50 rounded-lg z-10">
                        <span className="text-sm font-medium text-emerald-400">
                          Under Review
                        </span>
                      </div>
                    )}
                    {template.publish_status === PUBLIC_STATUS.REJECTED && (
                      <div className="absolute top-3 right-1 px-3 bg-rose-500/30 backdrop-blur-sm border border-rose-500/50 rounded-lg z-10">
                        <span className="text-sm font-medium text-rose-400">
                          Rejected
                        </span>
                      </div>
                    )}
                    <div className="flex flex-col">
                      <Image
                        src={icon}
                        alt="AI Avatar"
                        width={1024}
                        height={1536}
                        loading="eager"
                        className="w-full h-40 sm:h-60 rounded-t-xl object-cover"
                      />
                      <div className="p-4">
                        <h3 className="text-white text-base sm:text-lg line-clamp-1">
                          {title}
                        </h3>
                        <div className="text-sm text-muted-foreground line-clamp-2 my-2">
                          {description}
                        </div>
                        <div className="flex flex-wrap gap-2 mb-3">
                          {character_tags?.tags_desc?.map((tag) => (
                            <span
                              key={tag}
                              className="text-xs text-gray-400 px-3 py-1 rounded-lg border-[#334155] border"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>

                        <div className="flex gap-2">
                          {/* Edit button */}
                          <div
                            onClick={(e) => {
                              e.stopPropagation();
                              setOpenEditDialog(true);
                              const defaultInfo = {
                                ...template,
                                bot_name: template.title,
                                character_tags: {
                                  personality:
                                    template.character_tags?.personality?.join(
                                      ","
                                    ),
                                  tags_desc:
                                    template.character_tags?.tags_desc?.join(
                                      ","
                                    ),
                                },
                              };
                              setDefaultData(defaultInfo);
                            }}
                            className="cursor-pointer p-3 rounded-xl bg-primary-color/20 hover:bg-primary-color/30 transition-colors border border-primary-color/30"
                          >
                            <svg
                              width="20"
                              height="20"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="var(--primary-color)"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                            </svg>
                          </div>
                          {/* Delete button */}
                          {!template.is_public && (
                            <div
                              onClick={(e) => {
                                e.stopPropagation();
                                setShowDeleteConfirm({
                                  show: true,
                                  templateId: template.id,
                                });
                              }}
                              className="cursor-pointer p-3 rounded-xl bg-[#EF4444]/20 hover:bg-[#EF4444]/30 transition-colors border border-[#EF4444]/30"
                            >
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="#EF4444"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              >
                                <path d="M3 6h18" />
                                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
                              </svg>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            {/* Pagination */}
            {totalCount >= SOULMATE_PAGE_SIZE && (
              <div>
                <Pagination
                  currentPage={currentPage}
                  onHandlePageChange={(page) => {
                    setCurrentPage(page);
                    scrollToTop();
                  }}
                  totalCount={totalCount}
                  pageSize={SOULMATE_PAGE_SIZE}
                />
              </div>
            )}
          </>
        )}
      </TabPanel>
      {/* Confirmation Modal */}
      {showDeleteConfirm.show && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-[#1E1E1E] p-6 rounded-xl max-w-md w-full mx-4">
            <h2 className="text-xl font-semibold mb-2">Delete Chatbot</h2>
            <p className="text-gray-400 mb-6">
              Are you sure you want to delete this chatbot?
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() =>
                  setShowDeleteConfirm({ show: false, templateId: null })
                }
                className="px-4 py-2 rounded-lg border border-gray-600 hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={async () => {
                  if (!showDeleteConfirm.templateId) return;
                  const res = await apiService.deleteCustomTemplate(
                    showDeleteConfirm.templateId
                  );
                  if (res.code === 200) {
                    showToast("Delete Success", "success");
                    getCustomTemplate(currentPage);
                  } else {
                    showToast("Delete Failed", "error");
                  }
                  setShowDeleteConfirm({ show: false, templateId: null });
                }}
                className="px-4 py-2 rounded-lg bg-[#EF4444] hover:bg-[#EF4444]/90 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
      {openEditDialog && (
        <Dialog
          open={openEditDialog}
          onClose={() => setOpenEditDialog(false)}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              backgroundColor: "#1A1B1E",
              color: "white",
              borderRadius: "16px",
              backgroundImage:
                "linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.4)",
              position: "relative",
            },
          }}
        >
          <IconButton
            onClick={() => setOpenEditDialog(false)}
            sx={{ position: "absolute", right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
          <CustomCharacterCreateForm
            openGuideDialogDefault={false}
            defaultData={defaultData}
            isEdit={true}
            callback={() => {
              setOpenEditDialog(false);
              getCustomTemplate(currentPage);
            }}
          />
        </Dialog>
      )}
    </>
  );
};

export default Chatbot;
