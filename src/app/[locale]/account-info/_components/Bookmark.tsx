"use client";

import { Box } from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import { TabPanelProps, TabType } from "../type";
import { ROUTES, SOULMATE_PAGE_SIZE } from "@/constant";
import Pagination from "@/components/Pagination";
import { useUserStores } from "@/stores/UserStores";
import CardBox from "@/components/SoulChat/SoulmateCard/CardBox";
import { useTranslations } from "next-intl";
import { useRouter } from "@/i18n/routing";

export function TabPanel({ children, value, activeTab }: TabPanelProps) {
  return (
    <div role="tabpanel" hidden={value !== activeTab}>
      {value === activeTab && <Box>{children}</Box>}
    </div>
  );
}

const Bookmark = ({ activeTab }: { activeTab: TabType }) => {
  const {
    fetchBookmarkTemplate,
    bookmarkTemplate: { list, totalCount },
  } = useUserStores();
  const [currentPage, setCurrentPage] = useState(1);
  const bookmarkRef = useRef<HTMLDivElement>(null);
  const t = useTranslations("accountPage");
  const router = useRouter();
  const scrollToTop = () => {
    bookmarkRef.current?.scrollIntoView({ behavior: "instant" });
  };

  useEffect(() => {
    fetchBookmarkTemplate(currentPage, SOULMATE_PAGE_SIZE);
  }, [currentPage]);

  return (
    <TabPanel value="Bookmark" activeTab={activeTab}>
      <div ref={bookmarkRef}></div>
      {list.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-20">
          <p className="text-gray-500 text-lg font-medium">{t("noBookmark")}</p>
          <p className="text-gray-400 text-sm mt-2">
            {t("noBookmarkDescription")}
          </p>
          <button
            className="mt-4 bg-primary-color text-white px-4 py-2 rounded-md"
            onClick={() => {
              router.push(ROUTES.HOME);
            }}
          >
            {t("exploreTemplates")}
          </button>
        </div>
      ) : (
        <div className="grid gap-4 grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {list.map((template) => {
            return <CardBox key={template.id} soulmate={template} />;
          })}
        </div>
      )}
      {/* Pagination */}
      {totalCount >= SOULMATE_PAGE_SIZE && (
        <div>
          <Pagination
            currentPage={currentPage}
            onHandlePageChange={(page) => {
              setCurrentPage(page);
              scrollToTop();
            }}
            totalCount={totalCount}
            pageSize={SOULMATE_PAGE_SIZE}
          />
        </div>
      )}
    </TabPanel>
  );
};

export default Bookmark;
