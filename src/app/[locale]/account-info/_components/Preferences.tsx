import React, { useEffect } from "react";
import InterestImageList from "@/components/InterestImageList";
import { useUserStores } from "@/stores/UserStores";
import { TabPanel } from "../constant";
import { TabType } from "../type";

export const Preferences = ({ activeTab }: { activeTab: TabType }) => {
  const { interestInList, fetchInterestInList } = useUserStores();
  useEffect(() => {
    if (!interestInList.length) {
      fetchInterestInList();
    }
  }, []);
  return (
    <TabPanel value="Preferences" activeTab={activeTab}>
      <div className="border border-[#484854] rounded-lg p-4">
        <div className="pl-4">
          <div className="text-2xl font-bold mb-5">Your Preferences</div>
          <div className="text-lg font-semibold">Gender preferences</div>
          <div className="text-sm text-gray-700 dark:text-zinc-400">
            Select what gender bots you wish to see in your feed
          </div>
        </div>
        <InterestImageList
          interestInList={interestInList}
          onSelect={(style) => {
            document.cookie = `interested_in=${style.name}; path=/`;
          }}
        />
      </div>
    </TabPanel>
  );
};
