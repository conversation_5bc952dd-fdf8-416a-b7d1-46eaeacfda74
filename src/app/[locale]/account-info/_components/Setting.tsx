import { ROUTES } from "@/constant";
import { convertDate } from "@/utils/utils";
import { faCopy, faSignOut, faTrash } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React, { useState } from "react";
import { TabPanel } from "./Bookmark";
import { TabType } from "../type";
import { useUserStores } from "@/stores/UserStores";
import { showToast } from "@/utils/toast";
import { copy } from "../constant";
import CancelSubscriptionDialog from "@/components/AccountInfo/CancelSubscriptionDialog";
import { apiService } from "@/service/Api";
import { useRouter } from "@/i18n/routing";
import { StripeSubscriptionStatus } from "@/types";
import DeleteAccountDialog from "./DeleteAccountDialog";
import { clearAllSoulChatStore } from "@/stores/SoulChatStore";

const Setting = ({ activeTab }: { activeTab: TabType }) => {
  const { userEntity, logout, fetchUserInfo } = useUserStores();
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [showDeleteAccountDialog, setShowDeleteAccountDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const handleCancelSubscription = async () => {
    setIsLoading(true);
    try {
      const res = await apiService.cancelSubscription();
      if (res.code === 200) {
        showToast("Subscription cancelled successfully", "success");
        fetchUserInfo();
      } else {
        showToast("Failed to cancel subscription", "error");
      }
    } catch (error) {
      showToast("An error occurred while cancelling subscription", "error");
    } finally {
      setIsLoading(false);
      setShowCancelDialog(false);
    }
  };

  const handleDeleteAccount = async () => {
    setIsDeleting(true);
    try {
      const data = await apiService.deleteAccount();
      if (data.code === 200) {
        showToast("Account deleted successfully", "success");
        logout();
        clearAllSoulChatStore();
        localStorage.clear();
        router.push(ROUTES.HOME);
      } else {
        showToast(data.message || "Failed to delete account", "error");
      }
    } catch (error) {
      showToast("An error occurred while deleting your account", "error");
    } finally {
      setIsDeleting(false);
      setShowDeleteAccountDialog(false);
    }
  };
  return (
    <>
      <TabPanel value="Setting" activeTab={activeTab}>
        <div className="mx-auto">
          {/* Membership Card */}
          <div className="bg-gradient-to-r from-[#1E293B] to-[#334155] rounded-2xl p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl text-white font-semibold">Membership</h2>
              <div className="flex items-center gap-2">
                <span className="text-white/60">Balance:</span>
                <span className="text-white text-xl font-bold">
                  {userEntity?.entitlement?.credit_point || "0"} Coins
                </span>
              </div>
            </div>

            {userEntity?.entitlement?.expires && (
              <div className="flex items-center justify-between text-white/80 text-sm">
                <span>Renewal Date</span>
                <span>{convertDate(userEntity.entitlement.expires)}</span>
              </div>
            )}
          </div>

          {/* Settings Options */}
          <div className="bg-[#1E293B]/40 backdrop-blur-sm rounded-2xl overflow-hidden">
            {/* Account Section */}
            <div className="p-4">
              <h3 className="text-white/60 text-sm font-medium mb-2">
                ACCOUNT
              </h3>
              <div className="space-y-1">
                {/* User ID Row */}
                <div className="flex items-center justify-between p-3 rounded-xl hover:bg-white/5">
                  <span className="text-white">User ID</span>
                  <div className="flex items-center gap-2">
                    <span className="text-white/60">#{userEntity?.openid}</span>
                    <button
                      onClick={() => copy(userEntity?.openid || "")}
                      className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                      aria-label="Copy ID"
                    >
                      <FontAwesomeIcon
                        icon={faCopy}
                        className="text-white/60 w-4 h-4"
                      />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="h-px bg-white/10 mx-4" />

            {/* Subscription Section */}
            {userEntity?.entitlement?.expires && (
              <div className="p-4">
                <h3 className="text-white/60 text-sm font-medium mb-2">
                  SUBSCRIPTION
                </h3>
                <div className="space-y-1">
                  {userEntity?.entitlement?.expires && (
                    <>
                      {userEntity?.stripe_subscription_status ===
                      StripeSubscriptionStatus.CancelSubscription ? (
                        <div className="flex items-center justify-between text-white/80 text-sm px-3">
                          <span className="text-primary-color font-bold">
                            Expire Date
                          </span>
                          <span className="text-primary-color font-bold">
                            {convertDate(userEntity.entitlement.expires)}
                          </span>
                        </div>
                      ) : (
                        <button
                          onClick={() => setShowCancelDialog(true)}
                          className="w-full flex items-center justify-between p-3 rounded-xl hover:bg-white/5 text-red-500 transition-colors"
                        >
                          <span>Cancel Subscription</span>
                          <span>{isLoading ? "Processing..." : "Cancel"}</span>
                        </button>
                      )}
                    </>
                  )}
                </div>
              </div>
            )}

            <div className="h-px bg-white/10 mx-4" />

            {/* Account Actions */}
            <div className="p-4">
              <h3 className="text-white/60 text-sm font-medium mb-2">
                ACTIONS
              </h3>
              <div className="space-y-1">
                <button
                  onClick={() => {
                    logout();
                    router.push(ROUTES.HOME);
                  }}
                  className="w-full flex items-center justify-between p-3 rounded-xl hover:bg-white/5 text-primary-color transition-colors"
                >
                  <span>Sign Out</span>
                  <FontAwesomeIcon icon={faSignOut} className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setShowDeleteAccountDialog(true)}
                  className="w-full flex items-center justify-between p-3 rounded-xl hover:bg-white/5 text-red-500 transition-colors"
                >
                  <span>Delete Account</span>
                  <FontAwesomeIcon icon={faTrash} className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </TabPanel>
      <CancelSubscriptionDialog
        open={showCancelDialog}
        onClose={() => setShowCancelDialog(false)}
        onConfirm={handleCancelSubscription}
        isLoading={isLoading}
      />

      <DeleteAccountDialog
        showDeleteAccountDialog={showDeleteAccountDialog}
        setShowDeleteAccountDialog={setShowDeleteAccountDialog}
        handleDeleteAccount={handleDeleteAccount}
        isLoading={isDeleting}
      />
    </>
  );
};

export default Setting;
