"use client";

import React, { useEffect } from "react";
import { useSubscribeStores } from "@/stores/SubscribeStores";
import { changeLoginDialogState, useUserStores } from "@/stores/UserStores";
import { useRouter } from "@/i18n/routing";
import { FAQ, ProductList } from "@/components/subscribe";
import {
  EVENT_ACTION_CREATE_PAYMENT,
  EVENT_PAGE_SUBSCRIBE,
} from "@/utils/event";
import { firebaseLogEvent } from "@/utils/utils";
import PriceBanner from "@/components/subscribe/PriceBanner";
import { STORAGE_USER_INFO, SUBSCRIPTION_TYPE } from "@/constant";
import { useLocale, useTranslations } from "next-intl";

export default function Subscribe() {
  const t = useTranslations("SubscribePage");
  const locale = useLocale();
  const {
    paymentStatus,
    createPayment,
    createResp,
    fetchProducts,
    fetchProductsForLoginUser,
    resetRefresh,
    needRefreshProduct,
  } = useSubscribeStores();
  const { fetchUserInfo, userEntity } = useUserStores();
  const { changeProductLoading } = useSubscribeStores();
  const router = useRouter();

  useEffect(() => {
    if (paymentStatus == "success") {
      if (
        createResp &&
        createResp?.data &&
        createResp.data.stripe_params &&
        createResp.data.stripe_params.checkout_url
      ) {
        const checkoutUrl = createResp?.data.stripe_params.checkout_url;
        const newWindow = window.open(checkoutUrl);

        if (newWindow === null) {
          window.location.href = checkoutUrl;
        }
      }
    }
  }, [paymentStatus]);

  useEffect(() => {
    const fetchData = async () => {
      changeProductLoading(true);
      try {
        const storedUser = localStorage.getItem(STORAGE_USER_INFO);
        const currentUser =
          userEntity ?? (storedUser ? JSON.parse(storedUser) : null);
        if (currentUser?.entitlement?.member_level?.[0] === 2) {
          router.push("/");
          return;
        }
        if (currentUser?.guest_flag === false) {
          await fetchProductsForLoginUser(SUBSCRIPTION_TYPE.AUTO_RENEWABLE);
        } else {
          await fetchProducts(SUBSCRIPTION_TYPE.AUTO_RENEWABLE);
        }
      } catch {}
    };

    fetchData();
  }, [userEntity?.entitlement?.member_level?.[0]]);

  useEffect(() => {
    firebaseLogEvent(EVENT_PAGE_SUBSCRIBE);
    return () => {
      resetRefresh();
    };
  }, []);

  const openPay = (isUpgrade?: boolean) => {
    if (userEntity && !userEntity.guest_flag) {
      firebaseLogEvent(EVENT_ACTION_CREATE_PAYMENT, {
        page_path: EVENT_ACTION_CREATE_PAYMENT,
      });
      createPayment(window.location.origin, locale, isUpgrade);
    } else {
      changeLoginDialogState(true);
    }
  };

  return (
    <div>
      <h2 className="text-white text-3xl font-bold text-left">
        {t("pricing")}
      </h2>
      <div>
        <div className="flex flex-col justify-center items-center">
          <PriceBanner />
          <ProductList
            openPay={openPay}
            paymentStatus={paymentStatus}
            subscription_type={SUBSCRIPTION_TYPE.AUTO_RENEWABLE}
          />
        </div>

        <div>
          <div className="flex justify-center flex-row text-white font-bold text-3xl sm:mt-[100px]">
            {t("faq")}
          </div>
          <FAQ />
        </div>
      </div>
    </div>
  );
}
