import { getTranslations } from "next-intl/server";
import AddSoulmatesButton from "@/components/SoulChat/AddSoulmatesButton";
import HistoryChatList from "@/components/SoulChat/HistoryChatList";
import PaginationCard from "@/components/PaginationCard";
import CategorySearch from "@/components/CategorySearch";
import { TemplateGenderEnum, TemplateStyleEnum } from "@/types";
import HomeQA from "@/components/HomeQA";
import { cookies } from "next/headers";

export default async function Home({
  searchParams,
}: {
  searchParams: Promise<{ page: string; interested_in: string; style: string }>;
}) {
  const t = await getTranslations("HomePage");
  const cookieStore = await cookies();
  const cookie_searchParams =
    cookieStore.get("cookie_searchParams")?.value || "";
  const urlSearchParams = new URLSearchParams(cookie_searchParams);
  const page_default = urlSearchParams.get("page") || 1;
  const interested_in_default = urlSearchParams.get("interested_in") || "";
  const style_default = urlSearchParams.get("style") || "";
  const {
    page = page_default,
    interested_in = interested_in_default,
    style = style_default,
  } = await searchParams;

  return (
    <div className="mt-2">
      {/* Header */}
      <div className="flex gap-4 flex-nowrap overflow-x-auto">
        <div className="w-24 flex flex-col items-center justify-center gap-2 pb-2 sm:pb-6">
          <AddSoulmatesButton />
          <span className="text-xs text-white">{t("addSoulmates")}</span>
        </div>
        <HistoryChatList />
      </div>
      {/* 分页 */}
      <CategorySearch
        interested_in={interested_in as TemplateGenderEnum}
        style={style as TemplateStyleEnum}
      />
      <PaginationCard
        page={Number(page)}
        pageSize={40}
        interested_in={interested_in as TemplateGenderEnum}
        style={style as TemplateStyleEnum}
      />
      <HomeQA />
    </div>
  );
}
