"use client";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCircleCheck,
  faCircleExclamation,
} from "@fortawesome/free-solid-svg-icons";
import React, { useEffect } from "react";

import { EVENT_ACTION_PAYMENT_SUCCESS } from "@/utils/event";
import { EVENT_ACTION_PAYMENT_FAILED } from "@/utils/event";
import { firebaseLogEvent } from "@/utils/utils";
import { Button } from "@mui/material";
import { useSearchParams } from "next/navigation";
import { ROUTES } from "@/constant";
import { useRouter } from "@/i18n/routing";
import { useUserStores } from "@/stores/UserStores";

export default function PaymentResult() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { fetchUserInfo } = useUserStores();
  const type = searchParams.get("result");

  useEffect(() => {
    if (type?.includes("success")) {
      fetchUserInfo();
      firebaseLogEvent(EVENT_ACTION_PAYMENT_SUCCESS);
    } else {
      firebaseLogEvent(EVENT_ACTION_PAYMENT_FAILED);
    }
  }, [type]);

  const gotoChat = () => {
    router.push(ROUTES.HOME);
  };

  const gotoPayment = () => {
    router.push(ROUTES.SUBSCRIBE);
  };
  return (
    <div className="flex flex-col justify-center items-center  w-full h-screen  bg-dark-bg">
      {type?.includes("success") ? (
        <div className="sm:text-[24px] text-lg text-center font-bold text-white flex justify-center items-center flex-col">
          <FontAwesomeIcon
            className="w-24 h-24"
            color={"green"}
            size={"4x"}
            icon={faCircleCheck}
          />
          <div className="mt-[2rem] mb-6">
            Congratulations! Your Payment is Successful
          </div>
          <Button
            onClick={gotoChat}
            className="!text-white !font-bold normal-case bg-gradient-to-r from-blue-500 to-pink-500 flex flex-row justify-center items-center sm:!text-[22px] text-lg bg-gray w-[80%] border-gray border-2 !rounded-[10px] sm:h-[60px] h-[50px]"
          >
            Goto Chat
          </Button>
        </div>
      ) : (
        <div className="sm:text-[24px] text-lg text-center font-bold text-white flex justify-center items-center flex-col">
          <FontAwesomeIcon
            className="w-24 h-24"
            color={"red"}
            size={"4x"}
            icon={faCircleExclamation}
          />
          <div className="mt-[2rem] mb-6">Sorry, Your Payment is Failed</div>
          <Button
            onClick={gotoPayment}
            className="!text-white !font-bold normal-case bg-gradient-to-r from-blue-500 to-pink-500 flex flex-row justify-center items-center sm:!text-[22px] text-lg bg-gray w-[80%] border-gray border-2 !rounded-[10px] sm:h-[60px] h-[50px]"
          >
            Try Again
          </Button>
        </div>
      )}
    </div>
  );
}
