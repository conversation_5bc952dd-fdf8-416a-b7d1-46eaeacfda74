import { apiService } from "@/service/Api";
import Image from "next/image";
import React from "react";
import Link from "next/link";
import { ROUTES } from "@/constant";
import { ChatDetailTabs, StartChatButton } from "@/components/ChatDetail";
import SoulmateCard from "@/components/SoulChat/SoulmateCard";
import BackButton from "@/components/ChatDetail/BackButton";
import FavoriteButton from "@/components/SoulChat/FavoriteButton";
import VideoAvatar from "@/components/VideoAvatar";

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{ id: string; locale: string }>;
}) => {
  const { id, locale } = await params;
  const {
    data: { template },
  } = await apiService.getTemplateBasicInfo(id, locale);
  const { title, description } = template;
  const { tags_desc = [] } = template.character_tags;
  return {
    title: title,
    description: description,
    keywords: tags_desc.join(","),
    openGraph: {
      title: title,
      description: description,
      images: [template.icon],
    },
  };
};

const ChatDetail = async ({
  params,
}: {
  params: Promise<{ id: string; locale: string }>;
}) => {
  const { id, locale } = await params;
  const {
    data: { template },
  } = await apiService.getTemplateBasicInfo(id, locale);
  const { tags_desc = [] } = template.character_tags;
  return (
    <div>
      <div className="p-4 sm:p-8 text-white">
        {/* Back Button */}
        <BackButton />

        <div className="flex flex-col sm:flex-row gap-8">
          {/* Left Image Section */}
          <div className="sm:w-1/4">
            {template.icon.includes(".mp4") ? (
              <VideoAvatar
                src={template.icon}
                className="rounded-2xl object-cover w-full"
              />
            ) : (
              <Image
                src={template.icon}
                alt={template.title}
                width={400}
                height={400}
                className="rounded-2xl object-cover w-full"
              />
            )}
          </div>

          {/* Right Content Section */}
          <div className="sm:w-3/4 flex flex-col justify-between">
            <div>
              <div className="flex items-center gap-4 mb-4">
                <h1 className="text-3xl font-bold">{template.title}</h1>
                <FavoriteButton templateId={id} />
              </div>

              <p className="text-muted-foreground mb-6">
                {template.description}
              </p>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-6">
                {tags_desc.map((tag) => (
                  <Link
                    key={tag}
                    href={`/${locale}/${ROUTES.TAGS}/${encodeURIComponent(
                      tag
                    )}`}
                    className="cursor-pointer px-4 py-2 rounded-full bg-primary-color text-sm hover:bg-primary-color/80 transition-colors"
                  >
                    {tag}
                  </Link>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div>
              <div className="flex gap-4">
                <StartChatButton
                  templateId={id}
                  templateName={template.title}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <ChatDetailTabs
          SoulmateCardComponent={
            <SoulmateCard
              page={1}
              tag={tags_desc.join(",")}
              locale={locale}
              showPagination={false}
              pageSize={10}
            />
          }
        />
      </div>
    </div>
  );
};

export default ChatDetail;
