import { Suspense } from "react";
import type { <PERSON>ada<PERSON> } from "next";
import <PERSON>rip<PERSON> from "next/script";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { Toaster } from "react-hot-toast";
import { NextIntlClientProvider } from "next-intl";
import { getMessages } from "next-intl/server";
import { cookies } from "next/headers";

import "./globals.css";
import Navbar from "@/components/Navbar";
import { headers } from "next/headers";
import Footer from "@/components/Footer";
import { DOMAIN, ROUTES } from "@/constant";
import ConsentModal from "@/components/ConsentModal";
import Loading from "@/components/Loading";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export async function generateMetadata(): Promise<Metadata> {
  const headerList = await headers();
  const host = headerList.get("host");
  const baseUrl = `https://${host}`;

  const cookieStore = await cookies();
  const pathname =
    headerList.get("x-pathname") || cookieStore.get("x-pathname")?.value || "/";

  const langMatch = pathname.match(/\/(en|es|fr|ja|ko|pt)/);
  const currentLang = langMatch ? langMatch[1] : "en";

  const canonicalPath = pathname.replace(/\/(en|es|fr|ja|ko|pt)/, "") || "/";

  let metaData = {} as Metadata;

  switch (host) {
    case DOMAIN.NSFWCHAT:
      metaData = {
        title: "NSFW Chat App: Free and Unlimited AI Chat",
        description:
          "Experience the ultimate NSFW AI chat with no filters or limits. Dive into immersive roleplay and seamless AI interactions. Join now for free!",
      };
      break;

    case DOMAIN.PERCHANCEAI:
      metaData = {
        title: "NSFW Chat App: Free, Unlimited AI Chat & Perchance Roleplay",
        description:
          "Discover the ultimate NSFW AI chat experience with perchance roleplay. No filters, no limits—dive into seamless AI interactions and immersive storytelling today!",
      };
      break;

    case DOMAIN.EHE:
      metaData = {
        title: "Explore Ehentai: The Ultimate NSFW Chat & Roleplay Guide",
        description:
          "Discover the world of ehentai, from English content to breast expansion categories. Explore our guide and experience seamless NSFW AI chat and roleplay today!",
      };
      break;

    case DOMAIN.SWEETAI:
      metaData = {
        title:
          "Best Free NSFW AI Sex Chat & Image Gen | gptgirlfriend https://www.sweetai.chat ",
        description:
          "Discover the world of fantasies with sweetai.chat, Free NSFW & Sex AI Characters! Create your AI girl & gptgirlfriend - Try NOW!!",
        keywords:
          "gptgirlfriend,nsfw ai, sex ai, free sex chat, ai girl, nsfw character, image generator, sweetai.chat",
      };
      break;

    default:
      metaData = {
        title:
          "Best Free NSFW AI Sex Chat & Image Gen | AI Girl https://www.sweetai.chat ",
        description:
          "Discover the world of fantasies with sweetai.chat, Free NSFW & Sex AI Characters! Create your AI girl - Try NOW!!",
        keywords:
          "nsfw ai, sex ai, free sex chat, ai girl, nsfw character, image generator, sweetai.chat",
      };
      break;
  }

  const alternates = {
    canonical: `${baseUrl}${canonicalPath}`,
    languages: {
      en: `${baseUrl}${pathname.replace(`/${currentLang}`, "/en") || "/en"}`,
      es: `${baseUrl}${pathname.replace(`/${currentLang}`, "/es") || "/es"}`,
      fr: `${baseUrl}${pathname.replace(`/${currentLang}`, "/fr") || "/fr"}`,
      ja: `${baseUrl}${pathname.replace(`/${currentLang}`, "/ja") || "/ja"}`,
      ko: `${baseUrl}${pathname.replace(`/${currentLang}`, "/ko") || "/ko"}`,
      pt: `${baseUrl}${pathname.replace(`/${currentLang}`, "/pt") || "/pt"}`,
    },
  };
  metaData.alternates = alternates;

  return metaData;
}

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}>) {
  const { locale } = await params;
  const messages = await getMessages();
  return (
    <html lang={locale} suppressHydrationWarning>
      <Script
        strategy="afterInteractive"
        async
        src="https://www.googletagmanager.com/gtag/js?id=AW-16449874415"
      />
      <Script
        id="google-ads-script"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'AW-16449874415');
          `,
        }}
      />
      <Script
        src="//cdn.trackdesk.com/tracking.js"
        strategy="afterInteractive"
      />
      <Script
        id="trackdesk-init"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
              (function(t,d,k){
                (t[k]=t[k]||[]).push(d);
                t[d]=t[d]||t[k].f||function(){
                  (t[d].q=t[d].q||[]).push(arguments)
                }
              })(window,"trackdesk","TrackdeskObject");
              
              trackdesk('sweetaichat', 'click');
            `,
        }}
      />
      <head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=5.0, viewport-fit=cover"
        />
        <meta name="_foundr" content="4613e38b523655ce9253b357c3985b8d" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        <Suspense fallback={<Loading />}>
          <GoogleOAuthProvider clientId={process.env.AUTH_GOOGLE_ID ?? ""}>
            <Toaster />
            <NextIntlClientProvider messages={messages}>
              <ConsentModal />
              <div className="flex sm:flex-row flex-col">
                <Navbar initialLocale={locale} />
                <div
                  className={`layout-container root-container mx-auto w-full max-w-screen-[1920px] flex-1 overflow-y-auto px-4`}
                >
                  {children}
                  <Footer />
                </div>
              </div>
            </NextIntlClientProvider>
          </GoogleOAuthProvider>
        </Suspense>
      </body>
    </html>
  );
}
