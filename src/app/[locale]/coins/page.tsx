"use client";

import React, { useEffect } from "react";
import { useSubscribeStores } from "@/stores/SubscribeStores";
import { changeLoginDialogState, useUserStores } from "@/stores/UserStores";
import { useRouter } from "@/i18n/routing";
import { ProductList } from "@/components/subscribe";
import {
  EVENT_ACTION_CREATE_PAYMENT,
  EVENT_PAGE_SUBSCRIBE,
} from "@/utils/event";
import { firebaseLogEvent } from "@/utils/utils";
import PriceBanner from "@/components/subscribe/PriceBanner";
import { ROUTES, SUBSCRIPTION_TYPE } from "@/constant";
import { useLocale, useTranslations } from "next-intl";

export default function Subscribe() {
  const t = useTranslations("HomePage");
  const locale = useLocale();
  const {
    paymentStatus,
    createPayment,
    createResp,
    fetchProducts,
    resetRefresh,
    needRefreshProduct,
  } = useSubscribeStores();
  const { fetchUserInfo, userEntity } = useUserStores();
  const router = useRouter();

  useEffect(() => {
    if (paymentStatus == "success") {
      if (
        createResp &&
        createResp?.data &&
        createResp.data.stripe_params &&
        createResp.data.stripe_params.checkout_url
      ) {
        const checkoutUrl = createResp?.data.stripe_params.checkout_url;
        const newWindow = window.open(checkoutUrl);

        if (newWindow === null) {
          window.location.href = checkoutUrl;
        }
      }
    }
  }, [paymentStatus]);

  useEffect(() => {
    if (needRefreshProduct) {
      fetchProducts(SUBSCRIPTION_TYPE.NON_RENEWABLE);
    }
  }, [needRefreshProduct]);

  useEffect(() => {
    firebaseLogEvent(EVENT_PAGE_SUBSCRIBE);
    return () => {
      resetRefresh();
    };
  }, []);

  const openPay = () => {
    if (userEntity && !userEntity.guest_flag) {
      firebaseLogEvent(EVENT_ACTION_CREATE_PAYMENT, {
        page_path: EVENT_ACTION_CREATE_PAYMENT,
      });
      createPayment(window.location.origin, locale);
    } else {
      changeLoginDialogState(true);
    }
  };

  return (
    <div>
      <h2 className="text-white text-3xl font-bold text-left">{t("coins")}</h2>
      <div>
        <div className="flex flex-col justify-center items-center">
          <PriceBanner />
          <ProductList
            openPay={openPay}
            paymentStatus={paymentStatus}
            subscription_type={SUBSCRIPTION_TYPE.NON_RENEWABLE}
          />
        </div>
      </div>
    </div>
  );
}
