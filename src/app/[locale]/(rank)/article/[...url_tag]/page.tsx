import React from "react";
import { apiService } from "@/service/Api";
import { headers } from "next/headers";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";

import CardBox from "@/components/SoulChat/SoulmateCard/CardBox";
import { BotTemplate } from "@/types";
import Link from "next/link";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ url_tag: string }>;
}) {
  const headersList = await headers();
  const host = headersList.get("host");
  const { url_tag } = await params;
  const data = await apiService.getArticleByTag(url_tag);
  const { title, desc, image_list } = data?.data?.article || {};
  return {
    metadataBase: new URL(`https://${host}`),
    title,
    description: desc,
    openGraph: {
      title,
      description: desc,
      url: `https://${host}/article/${url_tag}`,
      siteName: host,
      images: image_list,
      locale: "en_US",
      type: "website",
    },
  };
}

export default async function ArticlePage({
  params,
}: {
  params: Promise<{ url_tag: string }>;
}) {
  const { url_tag } = await params;
  const data = await apiService.getArticleByTag(url_tag);
  const { title, content, inner_links } = data.data.article || {};

  // 用于存储连续的 bot-card 数据
  let botCardGroup: BotTemplate[] = [];

  return (
    <div className="max-w-screen-2xl mx-auto px-4 py-8 min-h-screen">
      <h2 className="text-4xl font-bold flex justify-center">{title}</h2>
      <ReactMarkdown
        rehypePlugins={[rehypeRaw]}
        components={{
          h1: ({ node, ...props }) => (
            <h1 className="text-3xl font-bold my-3" {...props} />
          ),
          h2: ({ node, ...props }) => (
            <h2 className="text-2xl font-bold my-2" {...props} />
          ),
          h3: ({ node, ...props }) => (
            <h3 className="text-xl font-bold my-1" {...props} />
          ),
          img: ({ node, ...props }) => {
            const src = node.properties.src || null;
            return <img {...props} src={src === "" ? null : src} />;
          },
          p: ({ node, ...props }) => {
            // 在遇到非 bot-card 标签时，渲染并清空当前的 botCardGroup
            if (botCardGroup.length > 0) {
              const groupToRender = [...botCardGroup];
              botCardGroup = [];
              return (
                <>
                  <div className="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4 py-3">
                    {groupToRender.map(
                      (bot, index) =>
                        bot.icon && (
                          <CardBox key={bot.id} soulmate={bot} index={index} />
                        )
                    )}
                  </div>
                  <div className="my-1" {...props} />
                </>
              );
            }
            return <div className="my-1" {...props} />;
          },
          "bot-card": ({ node, ...props }) => {
            const data = JSON.parse(node.properties.template);
            if (Array.isArray(data)) {
              botCardGroup.push(...data);
            } else {
              botCardGroup.push(data);
            }
            return null;
          },
        }}
      >
        {content}
      </ReactMarkdown>
      {inner_links && (
        <div>
          <h2 className="flex justify-center text-4xl my-14 mb-10 font-bold">
            {url_tag[0]?.toUpperCase()} with AI Companions
          </h2>
          <div className="grid grid-cols-4">
            {inner_links.split(",").map((link, index) => (
              <Link
                key={index}
                href={`/article/${link}`}
                className="mb-16 text-[24px] font-bold text-[#fffc] font-[Poppins-Medium]"
              >
                {link}
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
