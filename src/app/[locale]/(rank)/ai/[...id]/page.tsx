import Image from "next/image";
import { Link } from "@/i18n/routing";
import React from "react";
import { Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { apiService } from "@/service/Api";
import {
  Capabilities,
  ChatbotsCollection,
  PageArea2,
  PageArea3,
  QuestionAnswers,
} from "@/types/aiArticle";
import { PageArea1 } from "@/types/aiArticle";
import { notFound } from "next/navigation";
import TryITButton from "@/components/TryITButton";

const FAQSection = ({
  questionAnswers,
}: {
  questionAnswers: QuestionAnswers;
}) => {
  const { title = "", qa_list = [] } = questionAnswers;
  return (
    <section className="text-white py-16">
      <div className="max-w-4xl mx-auto px-4">
        <h2 className="text-4xl font-bold text-center mb-12">{title}</h2>
        <div>
          {qa_list.map((qa, index) => (
            <div key={index}>
              <Accordion
                sx={{
                  background: "#1a1a1a",
                  color: "white",
                  "&:before": {
                    display: "none",
                  },
                  "& .MuiAccordionSummary-root": {
                    borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
                  },
                  "& .MuiAccordionSummary-expandIconWrapper": {
                    color: "white",
                  },
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls="panel1-content"
                  id="panel1-header"
                >
                  <h3 className="text-xl font-semibold">{qa.title}</h3>
                </AccordionSummary>
                <AccordionDetails>
                  <p className="text-gray-300">{qa.desc}</p>
                </AccordionDetails>
              </Accordion>
            </div>
          ))}
        </div>
      </div>
      <div className="flex justify-center mt-12">
        <TryITButton />
      </div>
    </section>
  );
};

export default async function AIGeneratorPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const SEOPageList = await apiService.getSEOPageList();

  if (!SEOPageList.data.title_list.includes(id?.[0])) {
    return notFound();
  }
  const { data } = await apiService.getWebsiteConfig(id?.[0]);
  const {
    page_area_1 = {},
    page_area_2 = {},
    page_area_3 = {},
    question_answers = {},
    capabilities = {},
    chatbots_collection = {} as ChatbotsCollection,
  } = data;
  const {
    h1_title = "",
    h1_title_blue = "",
    h2_title = "",
    h3_title = "",
    image_url = "",
  } = (page_area_1 as PageArea1) || {};
  const {
    image_url: image_url_2 = "",
    key_word = "",
    steps = [],
  } = (page_area_2 as PageArea2) || {};
  const { key_word: key_word_3 = "", character_list = [] } =
    (page_area_3 as PageArea3) || {};
  const { title: capabilities_title = "", capability_list = [] } =
    (capabilities as Capabilities) || {};
  return (
    <main className="min-h-screen">
      {/* Page Area 1 Section */}
      {page_area_1 && (
        <section className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white sm:py-16 py-8 h-screen overflow-hidden">
          {/* Glow effects */}
          <div className="absolute inset-0">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-color/30 rounded-full blur-[128px]" />
            <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-primary-color/30 rounded-full blur-[128px]" />
          </div>

          <div className="relative max-w-screen-xl  mx-auto px-4 sm:px-6 lg:px-8 flex flex-col md:flex-row items-center justify-between">
            {/* Left content */}
            <div className="md:w-1/2 space-y-6">
              <h1 className="text-5xl md:text-6xl font-bold">
                {h1_title}
                <span className="block text-primary-color">
                  {h1_title_blue}
                </span>
              </h1>
              <p className="text-gray-300 text-xl">{h2_title}</p>
              <p className="text-gray-400 text-lg max-w-xl">{h3_title}</p>
              <TryITButton />
            </div>

            {/* Right content - Image */}
            <div className="md:w-1/2 mt-8 md:mt-0">
              <div className="relative w-full max-w-lg mx-auto">
                <Image
                  width={428}
                  height={428}
                  src={image_url}
                  alt="AI Character"
                  className="rounded-lg shadow-2xl"
                />
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Page Area 2 Section */}
      {page_area_2 && (
        <section className="max-w-screen-xl  mx-auto py-16 px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-primary-color">
            {key_word}
          </h2>

          {/* Content Container */}
          <div className="flex flex-col md:flex-row gap-12">
            {/* Left Image */}
            <div className="md:w-1/3">
              <Image
                src={image_url_2}
                alt="AI Character Examples"
                width={428}
                height={428}
                className="rounded-lg shadow-lg w-full object-cover"
              />
            </div>

            {/* Right Content - Steps */}
            <div className="md:w-1/2 space-y-12">
              {/* Step  */}
              {steps.map((step, index) => (
                <div className="space-y-4" key={index}>
                  <div className="text-gray-400">Step {index + 1}</div>
                  <h3 className="text-2xl font-bold">{step.step_title}</h3>
                  <p className="text-gray-600 leading-relaxed">
                    {step.step_desc}
                  </p>
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-center mt-12">
            <TryITButton />
          </div>
        </section>
      )}

      {/* Page Area 3 Section */}
      {page_area_3 && (
        <>
          <h2 className="text-2xl text-primary-color text-center -mb-16">
            {key_word_3}
          </h2>
          {character_list.map((character, index) => (
            <section
              key={index}
              className=" bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white py-24"
            >
              <div className="max-w-screen-xl mx-auto px-4">
                <div
                  className={`flex flex-col items-center gap-12 ${
                    (index + 1) % 2 === 0
                      ? "md:flex-row-reverse"
                      : "md:flex-row"
                  }`}
                >
                  {/* Left Content */}
                  <div className="flex-1 space-y-6">
                    <h2 className="text-4xl md:text-5xl font-bold">
                      {character.character_title}
                    </h2>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      {character.character_desc}
                    </p>
                  </div>

                  {/* Right Image */}
                  <div className="md:w-1/3">
                    <div className="relative">
                      <Image
                        src={character.image_url}
                        alt="Advanced AI Generator Example"
                        width={428}
                        height={428}
                        className="rounded-2xl shadow-2xl"
                      />
                      {/* Optional decorative elements */}
                      <div className="absolute -z-10 top-4 right-4 w-full h-full bg-blue-500/10 rounded-2xl" />
                    </div>
                  </div>
                </div>
              </div>
            </section>
          ))}
        </>
      )}

      {/* AI Capabilities Section */}
      {capabilities && (
        <>
          <section className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white py-16">
            <div className="max-w-screen-xl mx-auto px-4">
              <h2 className="text-4xl font-bold mb-12">{capabilities_title}</h2>

              <div className="space-y-12">
                {capability_list.map((capability, index) => (
                  <div key={index}>
                    <h3 className="text-2xl font-bold mb-4">
                      # {index + 1}&nbsp;&nbsp;{capability.title}
                    </h3>
                    <p className="mb-4">{capability.desc}</p>
                  </div>
                ))}
              </div>
            </div>
          </section>
        </>
      )}

      {/* AI Characters Collection Section */}
      {chatbots_collection?.title && (
        <section className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white py-16">
          <div className="max-w-screen-xl mx-auto px-4">
            <h2 className="text-4xl font-bold text-center mb-12">
              {chatbots_collection?.title}
            </h2>
            <p className="mx-auto flex flex-col gap-2 justify-center items-center">
              {chatbots_collection?.desc?.split("\n").map((line, index) => (
                <span key={index}>
                  {line}
                  <br />
                </span>
              ))}
            </p>
            <div className="flex justify-center mt-12">
              {chatbots_collection?.image_url && (
                <Image
                  src={chatbots_collection?.image_url}
                  alt="AI Chatbots Collection"
                  width={768}
                  height={768}
                  className="rounded-lg shadow-2xl"
                />
              )}
            </div>
            <div className="flex justify-center mt-12">
              <TryITButton />
            </div>
          </div>
        </section>
      )}

      {/* FAQ Section */}
      {question_answers && (
        <FAQSection questionAnswers={question_answers as QuestionAnswers} />
      )}
    </main>
  );
}
