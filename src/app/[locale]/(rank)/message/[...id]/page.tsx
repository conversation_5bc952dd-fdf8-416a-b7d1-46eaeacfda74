import { apiService } from "@/service/Api";
import { Metadata } from "next";
import Image from "next/image";
import { Link } from "@/i18n/routing";
import VideoAvatar from "@/components/VideoAvatar";

async function getTemplateData(id: string) {
  const {
    data: { template },
  } = await apiService.getTemplateBasicInfo(id);
  return template;
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ id: string }>;
}): Promise<Metadata> {
  const { id } = await params;
  const template = await getTemplateData(id);
  const { title: name, description, icon } = template;

  return {
    title: "Meet " + name,
    openGraph: {
      images: [icon],
    },
    description,
  };
}

const WelcomePage = async ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params;
  const template = await getTemplateData(id);
  const {
    welcome_message,
    title: name,
    description,
    character_tags: { personality, tags_desc, hobbies },
    icon,
  } = template;

  return (
    <main className="bg-gray-950 flex items-center justify-center p-6">
      <div className="max-w-5xl w-full">
        <div
          className="bg-gray-900/40 backdrop-blur-md rounded-3xl overflow-hidden
          border border-gray-800/50 shadow-xl"
        >
          <div className="grid md:grid-cols-[1.2fr,1fr] gap-0">
            {/* Content Section */}
            <div className="p-8 md:p-12 flex flex-col justify-between space-y-8">
              <div className="space-y-6">
                <h1
                  className="text-3xl md:text-4xl font-bold 
                  bg-gradient-to-r from-blue-400 to-violet-400 
                  bg-clip-text text-transparent"
                >
                  Welcome Message
                </h1>

                <p className="text-gray-300 text-lg leading-relaxed">
                  {welcome_message}
                </p>
              </div>

              <div className="space-y-6">
                <div
                  className="h-px bg-gradient-to-r from-transparent 
                  via-gray-700 to-transparent opacity-50"
                />

                <div className="space-y-4">
                  <h2 className="text-2xl font-semibold text-white">
                    Meet {name}
                  </h2>
                  <p className="text-gray-400 leading-relaxed">{description}</p>
                </div>

                <div className="flex flex-wrap gap-2">
                  {personality
                    .concat(tags_desc ?? [])
                    .concat(hobbies ?? [])
                    .map((tag, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 rounded-full text-sm
                        bg-violet-500/10 text-violet-300 border border-violet-500/20"
                      >
                        {tag}
                      </span>
                    ))}
                </div>

                <Link
                  href="/"
                  className="group relative inline-flex items-center justify-center
                    px-6 py-3 bg-gradient-to-r from-blue-500 to-violet-500 
                    rounded-xl text-white font-medium transition-all duration-300
                    hover:scale-105 hover:shadow-lg hover:shadow-blue-500/25"
                >
                  Get Started
                  <div
                    className="absolute inset-0 rounded-xl bg-gradient-to-r 
                    from-blue-400 to-violet-400 opacity-0 group-hover:opacity-20 
                    blur-xl transition-opacity duration-300"
                  />
                </Link>
              </div>
            </div>

            {/* Image Section */}
            <div className="relative h-full min-h-[400px] md:min-h-full">
              {icon.includes(".mp4") ? (
                <VideoAvatar
                  src={icon}
                  className="h-full w-full object-cover"
                />
              ) : (
                <Image
                  src={icon}
                  alt={name}
                  fill
                  className="object-cover"
                  priority
                />
              )}
              <div
                className="absolute inset-0 bg-gradient-to-r 
                from-gray-900/80 via-transparent to-transparent md:hidden"
              />
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default WelcomePage;
