import { apiService } from "@/service/Api";
import { <PERSON> } from "@/i18n/routing";
import React from "react";

const Tags = async ({ params }: { params: Promise<{ locale: string }> }) => {
  const { locale } = await params;
  const data = await apiService.getTemplateTags({ locale });
  return (
    <div className={`flex w-full gap-2 flex-wrap `}>
      {Object.keys(data?.data?.template_id_list || {})?.map((tag) => (
        <Link
          key={tag}
          href={`/tags/${tag}`}
          className="h-8 bg-secondary-color px-2 py-1 rounded-2xl text-sm text-secondary-light-color cursor-pointer hover:bg-secondary-color/80"
        >
          {tag}
        </Link>
      ))}
    </div>
  );
};

export default Tags;
