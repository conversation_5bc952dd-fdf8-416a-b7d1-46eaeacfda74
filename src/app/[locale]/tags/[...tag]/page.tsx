import SoulmateCard from "@/components/SoulChat/SoulmateCard";
import { getTranslations } from "next-intl/server";
import { headers } from "next/headers";
import { Metadata } from "next";
import React from "react";
import { getDomainTitle } from "@/utils/domain";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ tag: string }>;
}): Promise<Metadata> {
  const { tag } = await params;
  const tagFinal = decodeURIComponent(tag);
  const t = await getTranslations("HomePage");

  return {
    title: `${tagFinal} ${t("aiChatbots")}`,
    description: `${tagFinal} ${t("aiChatbots")}`,
  };
}

const TagPage = async ({
  params,
  searchParams,
}: {
  params: Promise<{ tag: string; locale: string }>;
  searchParams: Promise<{ page: string }>;
}) => {
  const { tag, locale } = await params;
  const { page = 1 } = await searchParams;
  const tagFinal = decodeURIComponent(tag);
  const headerList = await headers();
  const host = headerList.get("host");
  const title = getDomainTitle(host);
  const t = await getTranslations("HomePage");
  return (
    <div>
      <h3 className="text-4xl font-bold mb-3">
        <span className="text-primary-color">{tagFinal}</span> {t("aiChatbots")}
      </h3>
      <div className="text-sm mb-3 text-bold">
        {t("explore")} {tagFinal} {t("aiChatbots")} on {title} {t("ai")}
      </div>
      <SoulmateCard
        tag={tag}
        page={Number(page)}
        prefix={`/tags/${tagFinal}`}
        locale={locale}
      />
    </div>
  );
};

export default TagPage;
