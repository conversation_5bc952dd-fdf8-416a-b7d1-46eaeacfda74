"use client";

import { useGoogleLogin } from "@react-oauth/google";
import { useUserStores } from "../../../stores/UserStores";
import React, { useEffect } from "react";
import GoogleLoginBtn from "@/components/Login/GoogleLoginBtn";
import Image from "next/image";
import { CircularProgress } from "@mui/material";
import { useRouter } from "@/i18n/routing";
import { firebaseLogEvent, getUtmParams } from "@/utils/utils";
import { EVENT_PAGE_LOGIN } from "@/utils/event";
import { ROUTES } from "@/constant";
import { useTranslations } from "next-intl";

export default function Login() {
  const t = useTranslations("LoginPage");
  const { authGoogle, userEntity, loading } = useUserStores();
  const router = useRouter();

  const login = useGoogleLogin({
    onSuccess: async (tokenResponse) => {
      await authGoogle(tokenResponse.access_token, {
        ...getUtmParams(),
        user_agent: navigator.userAgent,
      });
    },
    flow: "implicit",
  });

  useEffect(() => {
    firebaseLogEvent(EVENT_PAGE_LOGIN);
  }, []);

  useEffect(() => {
    if (userEntity && !userEntity.guest_flag) {
      router.push(ROUTES.HOME);
    }
  }, [userEntity, router]);

  return (
    <div className="select-none h-screen w-full text-white text-[30px] flex flex-row justify-center lg:justify-start items-center">
      <div className="w-[0px] lg:w-[50%] relative h-full overflow-hidden flex justify-center items-center">
        <Image
          alt="login_bg"
          src="/login_bg.webp"
          className="w-full blur-md object-cover bg-center"
          width={1200}
          height={1800}
        ></Image>
        <div className="text-transparent lg:text-white absolute font-bold inset-0 flex flex-col items-center justify-center ">
          NSFWChat
          <div className="text-[12px]">{t("createYourOwnGirlfriend")}</div>
        </div>
      </div>
      <div className="text-[10px] flex-1  max-w-[80%] lg:max-w-[70%] lg:w-[70%] justify-center items-center flex flex-col gap-10">
        {loading && (
          <CircularProgress
            className="h-50 w-50 text-gray-900/50 mr-[10px]"
            color="primary"
          />
        )}
        <div className="flex flex-col items-start w-full  lg:w-[70%] font-bold">
          <div className="text-white text-[30px] select-none">Login</div>
        </div>
        <div className="w-[100%] lg:w-[70%] flex flex-col justify-start gap-10">
          <GoogleLoginBtn
            loading={loading}
            onClick={() => {
              login();
            }}
          ></GoogleLoginBtn>
        </div>
      </div>
    </div>
  );
}
