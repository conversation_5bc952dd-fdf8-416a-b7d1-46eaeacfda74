"use client";

import { apiService } from "@/service/Api";
import { ProjectEntity } from "@/types";
import { useEffect, useState, useRef, useCallback } from "react";
import ImageGallery from "@/components/ImageGallery";

export default function GalleryPage() {
  const [images, setImages] = useState<
    { id: string; url: string; index: number }[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const sentinelRef = useRef<HTMLDivElement>(null);

  const getImageUrls = (entity: ProjectEntity) => {
    if (entity?.cover_img?.length > 0) {
      return [{ id: entity.id + "&0", url: entity.cover_img, index: 0 }];
    }

    if (entity?.project_segments?.length > 0) {
      return entity.project_segments
        .filter((segment) => segment.image_url?.length > 0 && !segment.deleted)
        .map((segment) => ({
          id: `${entity.id}&${segment.index}`,
          url: segment.image_url,
          index: segment.index,
        }));
    }

    return [];
  };

  const fetchImages = useCallback(async (pageNum: number) => {
    try {
      setIsLoading(true);
      const response = await apiService.QueryTasks({
        page: pageNum,
      });
      const data = response?.data?.data || [];

      setHasMore(response?.data?.has_next);

      const newImages = data.map((entity) => getImageUrls(entity)).flat();
      setImages((prev) =>
        pageNum === 1 ? newImages : [...prev, ...newImages]
      );
    } catch (err) {
      setError("Failed to load images");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchImages(1);
  }, [fetchImages]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !isLoading && hasMore) {
          setPage((prev) => prev + 1);
        }
      },
      {
        rootMargin: "600px",
        threshold: 0,
      }
    );

    if (sentinelRef.current) {
      observer.observe(sentinelRef.current);
    }

    return () => observer.disconnect();
  }, [isLoading, hasMore]);

  useEffect(() => {
    if (page > 1) {
      fetchImages(page);
    }
  }, [page, fetchImages]);

  if (error) {
    return (
      <div className="mx-auto py-8">
        <h1 className="text-3xl font-bold mb-6">User Gallery</h1>
        <div className="text-red-500 text-center">
          {error}
          <button
            className="ml-4 px-4 py-2 border border-gray-300 rounded hover:bg-gray-100"
            onClick={() => {
              setPage(1);
              setImages([]);
              setHasMore(true);
              fetchImages(1);
            }}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">User Gallery</h1>
      {isLoading ? (
        <div className="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4 py-3">
          {[...Array(10)].map((_, index) => (
            <div
              key={index}
              className="h-64 w-full bg-gray-dark animate-pulse rounded-lg"
            ></div>
          ))}
        </div>
      ) : (
        <ImageGallery images={images} setImages={setImages} />
      )}
      {!isLoading && hasMore && <div ref={sentinelRef} className="h-1" />}
    </div>
  );
}
