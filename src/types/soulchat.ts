import { Segment } from "next/dist/server/app-render/types";
import { ResponseWrap } from ".";

export interface GetSoulmatesRequest {
  app_id: string;
  is_system_template: boolean;
  template_tag_filter?: string;
  page?: number;
  page_size?: number;
}

export interface BotTemplate {
  id: string;
  description: string;
  title: string;
  background_image: string;
  icon: string;
  welcome_message: string;
  character_identity: string;
  character_tags: CharacterTags;
  model_name: string;
  voice_id: string;
  sys_prompt: string;
  create_time?: number;
  chat_id?: string;
  face_model_id?: string;
  photo_array: string;
  is_nsfw?: boolean;
  is_public?: boolean;
  publish_status?: number;
  video_url: string;
}

export interface CharacterTags {
  age: number;
  personality: string[];
  hobbies: string[];
  tags_desc?: string[];
  is_public?: boolean;
  is_nsfw?: boolean;
}

export interface AddSoulmateDialogDataType {
  selectedOtherTags: string[];
  personalityAndHobbiesTags: string[];
  create_four_pics_UrlList: string[];
  create_four_pics_timerId: NodeJS.Timeout | null;
  soulmate_age: number;
  soulmate_name: string;
  selectedFace: FaceModel | null;
  voice_id: string;
  group_id: string;
}

export interface FaceModel {
  id: string;
  index: number;
  user_id?: string;
  url: string;
  resize_url: string;
  detection_result: boolean;
  status?: [number, string];
}

export interface VoiceIdListVoice {
  id: string;
  name: string;
  url: string;
}

export interface Chat {
  id: string;
  name: string;
  user: string;
  bot: string;
  create_time: string;
  latest_message_time: string;
  last_message_type: string;
  latest_message: string;
  tag: string;
  welcome: string;
  latest_message_time_diff: string;
  icon: string;
  bot_name: string;
  bot_template_id: string;
}

export type CreateCharacterParams = {
  icon?: string;
  app_id: string;
  model_name?: string;
  provider?: string;
  group_id?: string;
  bot_name?: string;
  character_tags?: {
    age?: number;
    personality?: string[];
    hobbies?: string[];
  };
  is_system_template?: boolean;
  voice_id?: string;
  photo_array?: string;
  description?: string;
  sys_prompt?: string;
  face_model_id?: string;
};

export interface CreateCharacterParamsV2 {
  icon: string | null;
  bot_name: string;
  description: string;
  character_tags: CharacterTagsV2;
  scenario: string;
  welcome_message: string;
  example_conversations: string;
}

export interface CharacterTagsV2 {
  personality?: string[];
  tags_desc?: string[];
  is_nsfw?: boolean;
  is_public?: boolean;
}

export interface CreateMessageRequest {
  content: string;
  chat: string;
  is_hide_user_message?: boolean;
  message_type?: string;
  video_type?: string;
}

export interface CreateRequest {
  project_name: string;
  project_novel_text: string;
  project_type: string;
  project_config: ProjectConfig;
  type?: string;
  suggest_template_id?: number;
}

export interface CreateV2Request {
  project_novel_text: string;
  negative_prompt: string;
  auto_enhance: boolean;
  safe_mode: boolean;
  style: string;
  number_of_images: number;
  creativity: number;
  face_model_id: string;
  bot_template_id: string;
  type: string;
  chat_id: string;
  face_url: string;
  suggest_template_id: number;
}

export interface ProjectConfig {
  sd_model_name: string;
  sd_image_ratio: string;
  batch_number: number;
  lora_model_name: string;

  voice_name: string;
  voice_speed: number;
  voice_volume: number;
  background_music_url: string;
  face_model_id: string | null;
  face_url?: string;
  bot_template_id?: string;
  chat_id?: string;
}

export interface Message {
  id: string;
  chat: string;
  content: any;
  type: string;
  feedback_type: any;
  is_from_bot: boolean;
  sender: string;
  create_time: number;
  loading: boolean;
  isAskPic?: boolean;
  is_need_subscribe?: boolean;
  is_need_signup?: boolean;
  message_voice_url?: string;
}

export interface CreateResponse extends ResponseWrap<CreateEntity> {
  code: number;
  message: string;
  data: CreateEntity;
}

export interface CreateEntity {
  id: string;
  project_config?: ProjectConfig;
}

export interface TaskQueryRequest {
  project_id: string;
}

export interface ProjectEntity {
  id: string;
  cover_img: string;
  project_status: ProjectStatus;
  project_config: ProjectConfig;
  project_segments: Segment[];
  project_novel_text: string;
}

type ProjectStatus = [statusCode: number, statusText: string];

export interface EditCharacterParams {
  description: string;
  sys_prompt: string;
  template_id: string;
  photo_array: string;
}

export interface EditCustomCharacterParams {
  icon: string;
  bot_name: string;
  description: string;
  character_tags: Partial<CharacterTags>;
  scenario: string;
  welcome_message: string;
  example_conversations: string;
  template_id: string;
}

export interface VoiceIdList {
  tabs: VoiceIdListTab[];
  videoSizes: VoiceIdListVideoSize[];
  voices: VoiceIdListVoice[];
}

export interface VoiceIdListTab {
  name: string;
  tags: VoiceIdListTag[];
}

export interface VoiceIdListTag {
  name: string;
  index: number;
}

export interface VoiceIdListVoice {
  id: string;
  name: string;
  url: string;
}
export interface VoiceIdListVideoSize {
  id: number;
  sizeName: string;
  width: number;
  height: number;
}

export interface BotTemplateGroup {
  id: string;
  title: string;
  description: string;
  templates: BotTemplate[];
}

export interface CreateMessageResponseData {
  data: string;
}

export interface CreateTabEntity {
  tags: PromptTagEntity[];
  name: string;
}

export interface PromptTagEntity {
  index: number;
  name: string;
}
export interface MaterialData {
  tabs: CreateTabEntity[];
}

export interface MaterialRequest {
  scene?: string;
}

export interface ChatModelItem {
  aliases: string;
  desc: string;
  index: number;
  support_level: number[];
}

export interface ChatModel {
  chat_model_list: ChatModelItem[];
  current_model_index: number;
}

export interface ImageStyle {
  current_style_index: number;
  image_gen_styles: ImageGenStyleItem[];
}

export interface ImageGenStyleItem {
  index: number;
  example_image_url: string;
  name: string;
}

export interface InterestInListResp {
  interested_in_list: InterestedInListItem[];
}

export interface InterestedInListItem {
  index: number;
  title: string;
  image_url: string;
}

export interface ImageListItem {
  width: number;
  height: number;
  url: string;
}

export interface ArticleResp {
  article: {
    id: string;
    title: string;
    content: string;
    article_type: number;
    create_time: string;
    desc: string;
    image_list: ImageListItem[];
    inner_links: string;
  };
}

export interface HomeListRequestLazyLoader {
  interested_in?: TemplateGenderEnum;
  style?: TemplateStyleEnum;
  page_size?: number;
  page: number;
  app_id?: string;
  encoded_uuids?: any[];
}

export enum TemplateStyleEnum {
  Realistic = "Realistic",
  Anime = "Anime",
}

export enum TemplateGenderEnum {
  Female = "Female",
  Male = "Male",
  Trans = "Trans",
}

export interface UploadFaceCreateEntity {
  bears: number;
  project_id: string;
  prompt: string;
  promptTags: PromptTagEntity[];
  style: StylesEntity;
  model: StylesEntity;
  faceModel: FaceModel;
  batchNumber: number;
}

export interface StylesEntity {
  index: number;
  name: string;
  model_name: string;
  image: string;
}

export interface InteractListResp {
  interact_list: InteractList[];
}

export interface InteractList {
  id: number;
  title: string;
  desc: string;
  emoji: string;
}

export interface GenerateVideoParams {
  template_id: string;
  image_url: string;
  prompt: string;
  duration: number;
  video_type: string;
  message_id: string;
}

export interface GenerateVideoResp {
  video_url: string | null;
  template_video_id: string;
}
