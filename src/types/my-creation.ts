import { Segment } from "next/dist/server/app-render/types";
import { ResponseWrap } from "./api";
import { ProjectConfig } from "./soulchat";

export interface ProjectEntity {
  id: string;
  cover_img: string;
  project_status: ProjectStatus;
  project_config: ProjectConfig;
  project_segments: Segment[];
  project_novel_text: string;
}

type ProjectStatus = [statusCode: number, statusText: string];

export interface TasksQueryResponse extends ResponseWrap<ProjectListResp> {
  data: ProjectListResp;
}

export interface TaskDeleteRequest {
  product_id: string;
  segment_index: number;
}

export interface TaskDeleteResponse extends ResponseWrap<{ id: string }> {
  data: { id: string };
}

export interface ProjectListResp {
  //总数据条数
  total: number;
  //总页数
  number_page: number;
  //当前页
  page: number;
  //页码大小
  size: number;
  has_next: boolean;
  data: ProjectEntity[];
}

export interface TasksQueryRequest {
  page: number;
  size?: number;
  project_type?: string | undefined;
}
