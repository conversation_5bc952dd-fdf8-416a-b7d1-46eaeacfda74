export interface StyleItem {
  displayName: string;
  internalName: string;
  exampleImage: string;
  label?: string;
}

export interface ImageGeneratorConfigItem {
  id: number;
  heading: string;
  description: string;
  imgUrlPc: string;
  imgUrlMobile: string;
  imgUrlThumbnails: string;
  label?: string;
}

export type ImageGeneratorConfig = {
  [key: string]: ImageGeneratorConfigItem;
};

export interface InspirationItem {
  id: number;
  imgUrl: string;
  text: string;
  style?: string;
}

export interface InstantInspirationConfig {
  [key: string]: InspirationItem[];
}
