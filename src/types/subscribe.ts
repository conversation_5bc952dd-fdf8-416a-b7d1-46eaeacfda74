import { ResponseWrap } from "./api";

export interface CreatePaymentEntity {
  channel: SubscriptionChannel;
  product_id: string;
  stripe_params: StripeParams;
  wechat_params: string;
}

export interface ProductEntity {
  // id
  id: string;
  //AppStore 商品/订阅 id
  //商品展示名称
  name: string;
  //订阅类型
  //价格
  amount: number;
  origin_amount: number;
  //生效时长（月）
  discount_label?: string;
  level: number;
  rights?: string[];
  title?: string;
  month_period?: number;
  description?: string;
  price_per_month?: number;
  ProratedCharge?: number;
}

export interface StripeParams {
  trade_no: string;
  client_params: string;
  checkout_url: string;
}

export enum SubscriptionChannel {
  Stripe = "Stripe",
}

export interface CreatePaymentRequest {
  channel: SubscriptionChannel;
  product_id: string;
  success_url?: string;
  failed_url?: string;
  app: string;
}

export type CreatePaymentResponse = ResponseWrap<CreatePaymentEntity>;

export type GetProductResponse = ResponseWrap<ProductEntity[]>;
export type GetProductResponseForLoginUser = ResponseWrap<{
  products: ProductEntity[];
  is_upgrade: boolean;
}>;
