export interface WebsiteConfig {
  title: string;
  page_area_1: PageArea1;
  page_area_2: PageArea2;
  page_area_3: PageArea3;
  chatbots_collection: ChatbotsCollection;
  capabilities: Capabilities;
  question_answers: QuestionAnswers;
}

export interface PageArea1 {
  h1_title: string;
  h1_title_blue: string;
  h2_title: string;
  h3_title: string;
  image_url: string;
}

export interface PageArea2 {
  image_url: string;
  key_word: string;
  steps: Step[];
}

export interface Step {
  step_title: string;
  step_desc: string;
}

export interface PageArea3 {
  character_list: CharacterList[];
  key_word: string;
}

export interface CharacterList {
  character_title: string;
  character_desc: string;
  image_url: string;
}

export interface ChatbotsCollection {
  title: string;
  desc: string;
  image_url: string;
}

export interface Capabilities {
  title: string;
  capability_list: CapabilityList[];
}

export interface CapabilityList {
  title: string;
  desc: string;
}

export interface QuestionAnswers {
  title: string;
  qa_list: QaList[];
}

export interface QaList {
  title: string;
  desc: string;
}
