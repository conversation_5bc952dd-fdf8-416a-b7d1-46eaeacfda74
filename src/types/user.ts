import { ResponseWrap } from "./api";

export interface AuthData {
  token: string;
  expires: number;
}

export interface AuthResponse extends ResponseWrap<AuthData> {
  data: AuthData;
}

export interface UserEntity {
  name: string;
  avatar_url?: string;
  id: string;
  channel?: string;
  access_token?: string;
  refresh_token?: string;
  token?: string;
  unionid: string;
  openid?: string;
  entitlement?: Entitlement;
  subscription?: Subscription;
  guest_flag: boolean;
  check_nsfw?: boolean;
  email: string;
  stripe_subscription_status: StripeSubscriptionStatus;
}

export enum StripeSubscriptionStatus {
  NoSubscription = 0, // 未订阅
  InSubscription = 1, // 订阅中
  CancelSubscription = 2, // 订阅已取消
}

export interface Entitlement {
  credit_point?: number;
  project_unit_day?: number;
  expires?: string;
  member_level?: [number, string];
}

export interface Subscription {
  purchase_data?: string;
  expires?: string;
  appstore_order_id?: string;
  status?: [];
  channel: string;
}

export interface AuthRequest {
  channel: string;
  token: string;
  inviteCode?: string | null;
  guest_user_id?: string;
  attribution?: AuthRequestAttribution;
}

export interface AuthRequestAttribution {
  ref: string;
  utm_source: string;
  utm_medium: string;
  user_agent: string;
}

export interface LoginResponse extends ResponseWrap<UserEntity> {
  data: UserEntity;
}
