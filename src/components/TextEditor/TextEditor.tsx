/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import Heading from "@tiptap/extension-heading";
import Underline from "@tiptap/extension-underline";
import Highlight from "@tiptap/extension-highlight";
import Code from "@tiptap/extension-code";
import Strike from "@tiptap/extension-strike";
import Subscript from "@tiptap/extension-subscript";
import Superscript from "@tiptap/extension-superscript";
import Link from "@tiptap/extension-link";
import Image from "@tiptap/extension-image";
import TextAlign from "@tiptap/extension-text-align";
import CodeBlockLowlight from "@tiptap/extension-code-block-lowlight";
import { all, createLowlight } from "lowlight";
import "./style.css";
import CustomBotNode from "./Custom/CustomBotNode";

const lowlight = createLowlight(all);

const TextEditor = ({ notes }: { notes?: string }) => {
  const CustomTextAlign = TextAlign.extend({
    addKeyboardShortcuts() {
      return {};
    },
  });
  const editor = useEditor({
    extensions: [
      StarterKit,
      CustomBotNode,
      Placeholder.configure({
        placeholder: "Write something …",
      }),
      Heading.configure({
        levels: [1, 2, 3],
      }),
      Underline,
      Code.configure({
        HTMLAttributes: {
          class:
            "bg-blue-50 text-blue-500 px-2 py-1 rounded-md font-mono text-sm border border-blue-200 inline-block mr-2 mb-2",
        },
      }),
      Highlight.configure({
        multicolor: true,
        HTMLAttributes: {
          class: "rounded-sm px-1 py-0.5",
        },
      }),
      Strike,
      Subscript,
      Superscript,
      Link.configure({
        HTMLAttributes: {
          class: "underline text-blue-500 cursor-pointer",
        },
        openOnClick: true,
        autolink: true,
        defaultProtocol: "https",
        protocols: ["http", "https"],
        isAllowedUri: (url, ctx) => {
          try {
            // construct URL
            const parsedUrl = url.includes(":")
              ? new URL(url)
              : new URL(`${ctx.defaultProtocol}://${url}`);

            // use default validation
            if (!ctx.defaultValidate(parsedUrl.href)) {
              return false;
            }

            // disallowed protocols
            const disallowedProtocols = ["ftp", "file", "mailto"];
            const protocol = parsedUrl.protocol.replace(":", "");

            if (disallowedProtocols.includes(protocol)) {
              return false;
            }

            // only allow protocols specified in ctx.protocols
            const allowedProtocols = ctx.protocols.map((p) =>
              typeof p === "string" ? p : p.scheme
            );

            if (!allowedProtocols.includes(protocol)) {
              return false;
            }

            // disallowed domains
            const disallowedDomains: string[] = [];
            const domain = parsedUrl.hostname;

            if (disallowedDomains.includes(domain)) {
              return false;
            }

            // all checks have passed
            return true;
          } catch {
            return false;
          }
        },
        shouldAutoLink: (url) => {
          try {
            // construct URL
            const parsedUrl = url.includes(":")
              ? new URL(url)
              : new URL(`https://${url}`);

            // only auto-link if the domain is not in the disallowed list
            const disallowedDomains: string[] = [];
            const domain = parsedUrl.hostname;

            return !disallowedDomains.includes(domain);
          } catch {
            return false;
          }
        },
      }),
      Image.configure({
        allowBase64: true,
      }),
      CustomTextAlign.configure({
        types: ["heading", "paragraph"],
        alignments: ["left", "center", "right"],
      }),
      CodeBlockLowlight.configure({
        lowlight,
      }),
    ],
    immediatelyRender: false,
    editorProps: {
      attributes: {
        class: "focus:outline-none overflow-y-auto",
      },
    },
    editable: false,
    content: notes,
  });

  if (!editor) return null;

  return (
    <div className="flex flex-col p-6 pr-0 rounded-lg">
      <EditorContent editor={editor} />
    </div>
  );
};

export default TextEditor;
