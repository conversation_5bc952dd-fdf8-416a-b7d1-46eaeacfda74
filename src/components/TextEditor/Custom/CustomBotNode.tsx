import { mergeAttributes, Node } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";

import BotCard from "./BotCard";

export default Node.create({
  name: "botCard",

  group: "inline",
  inline: true,

  atom: true,

  addAttributes() {
    return {
      template: {
        default: {},
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "bot-card",
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ["bot-card", mergeAttributes(HTMLAttributes)];
  },

  addNodeView() {
    return ReactNodeViewRenderer(BotCard);
  },
});
