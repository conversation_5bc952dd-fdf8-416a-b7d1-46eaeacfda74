/* eslint-disable @typescript-eslint/no-explicit-any */
import CardBox from "@/components/SoulChat/SoulmateCard/CardBox";
import { BotTemplate } from "@/types";
import { NodeViewWrapper } from "@tiptap/react";
import React from "react";

const BotCard = (props: any) => {
  const { template = "[]" } = props.node.attrs;
  const botList = JSON.parse(template);

  return (
    <NodeViewWrapper className="bot-card my-4 inline-flex mx-1 flex-wrap space-y-4 gap-2">
      {Array.isArray(botList) ? (
        <div className="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4 py-3">
          {botList?.map((bot: BotTemplate, index: number) => (
            <CardBox key={bot.id} soulmate={bot} index={index} />
          ))}
        </div>
      ) : (
        <CardBox key={botList.id} soulmate={botList} index={0} />
      )}
    </NodeViewWrapper>
  );
};

export default BotCard;
