"use client";
import React from "react";
import { useUserStores } from "@/stores/UserStores";
import { useRouter, usePathname } from "@/i18n/routing";
import Image from "next/image";
import { ROUTES } from "@/constant";

export default function ProfileMenu() {
  const { userEntity, logout } = useUserStores();
  const router = useRouter();
  const pathname = usePathname();

  return (
    <div className={`flex flex-row justify-center items-center gap-4`}>
      <Image
        src={userEntity?.avatar_url || ""}
        alt="avatar"
        width={40}
        height={40}
        className="rounded-full cursor-pointer"
        onClick={() => router.push(ROUTES.ACCOUNT_INFO)}
      />

      <div
        onClick={() => {
          logout();
          if (
            [ROUTES.AI_CHAT, ROUTES.ACCOUNT_INFO, ROUTES.CREATE_IMAGE].some(
              (path) => window.location.pathname.includes(path)
            )
          ) {
            router.push(ROUTES.HOME);
          }
        }}
        className={`cursor-pointer underline text-primary-color text-sm ${
          pathname.includes(ROUTES.AI_CHAT) ||
          pathname.includes(ROUTES.CREATE_IMAGE)
            ? "hidden"
            : "lg:block hidden"
        }`}
      >
        Sign out
      </div>
    </div>
  );
}
