"use client";

import React, { useState, useEffect, useTransition } from "react";
import { <PERSON><PERSON>, <PERSON>er, Popover } from "@mui/material";
import ExitToAppIcon from "@mui/icons-material/ExitToApp";
import { useInitOpt } from "@/hooks/UseInitOpt";
import { useUserStores } from "@/stores/UserStores";
import ProfileMenu from "./ProfileMenu";
import { EQUAL_OR_INCLUDE, ROUTES } from "@/constant";
import { useLocale, useTranslations } from "next-intl";
import {
  Language as LanguageIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  Menu as MenuIcon,
  MonetizationOn as MonetizationOnIcon,
} from "@mui/icons-material";
import { Images, Album } from "lucide-react";

import { routing, useRouter, Link, usePathname } from "@/i18n/routing";
import { updateLocale } from "@/service/Api";
import LoginModal from "@/components/LoginModal";
import { saveUtmParams } from "@/utils/utils";
import { useOtherStores } from "@/stores/otherStore";
import useResponsive from "@/hooks/useResponsive";
import GlobalNSFWSwitch from "./GlobalNSFWSwitch";
import useTrackdesk from "@/hooks/useTrackdesk";

const Navbar = ({ initialLocale }: { initialLocale: string }) => {
  const t = useTranslations("HomePage");
  const pathname = usePathname();
  const [domain, setDomain] = useState<string | null>(null);
  const { templateTags, fetchTemplateTags } = useOtherStores();
  const { isLargeScreen } = useResponsive();
  useTrackdesk();

  useEffect(() => {
    if (typeof window !== "undefined") {
      const currentDomain = window.location.hostname;
      setDomain(currentDomain);
      saveUtmParams();
    }
    fetchTemplateTags({ locale: initialLocale });
    updateLocale(initialLocale);
  }, []);
  useInitOpt();
  const { userEntity, changeLoginDialogState, loginDialogOpen } =
    useUserStores();
  const router = useRouter();
  const locale = useLocale();
  const [isPending, startTransition] = useTransition();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    setMobileMenuOpen(false);
  }, [pathname]);

  const handleMobileMenuToggle = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const getTitle = () => {
    if (domain === "www.nsfwchat.app") {
      return "NSFWChat";
    } else if (domain === "www.perchanceai.chat") {
      return "Perchance AI";
    } else {
      return "SweetAI";
    }
  };
  const isShowSmallBar = [ROUTES.AI_CHAT, ROUTES.CREATE_IMAGE].some((route) =>
    pathname.includes(route)
  );
  const isTagPage = pathname.includes(ROUTES.TAGS);

  const isCurrentSelectedPage = (path: string, extra: string) => {
    if (extra === EQUAL_OR_INCLUDE.EQUAL) {
      return path === pathname ? "text-primary-color" : "text-white";
    } else if (extra === EQUAL_OR_INCLUDE.INCLUDES) {
      return pathname.includes(path) ? "text-primary-color" : "text-white";
    }
  };

  const handleLanguageClick = (event: React.MouseEvent<HTMLDivElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleLanguageClose = () => {
    setAnchorEl(null);
  };

  const handleLanguageChange = (newLocale: string) => {
    startTransition(() => {
      router.push(pathname, { locale: newLocale });
      handleLanguageClose();
    });
  };

  const navigationContent = ({ isSideBar }: { isSideBar?: boolean } = {}) => (
    <nav className="flex flex-col justify-between h-full">
      <div className="overflow-auto scrollbar-hide">
        <div className="flex flex-col gap-6">
          <Link
            className={`text-[24px] font-bold text-white mb-2 ${
              !mobileMenuOpen && (isShowSmallBar ? "hidden" : "hidden lg:block")
            }`}
            href="/"
          >
            {getTitle()}
          </Link>
          <Link
            href="/"
            className={`${isCurrentSelectedPage(
              ROUTES.HOME,
              EQUAL_OR_INCLUDE.EQUAL
            )} font-bold text-sm flex gap-4 items-center hover:text-primary-color`}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="512"
              height="512"
              data-name="Layer 1"
              fill="currentColor"
              viewBox="0 0 24 24"
              className="w-6 h-6"
            >
              <path d="M20.466 1.967L14.78.221a5.011 5.011 0 00-6.224 3.24L8.368 4H5a5.006 5.006 0 00-5 5v10a5.006 5.006 0 005 5h6a4.975 4.975 0 003.92-1.934 5.029 5.029 0 00.689.052 4.976 4.976 0 004.775-3.563L23.8 8.156a5.021 5.021 0 00-3.334-6.189zM11 22H5a3 3 0 01-3-3V9a3 3 0 013-3h6a3 3 0 013 3v10a3 3 0 01-3 3zM21.887 7.563l-3.412 10.4a2.992 2.992 0 01-2.6 2.134A4.992 4.992 0 0016 19V9a5.006 5.006 0 00-5-5h-.507a3 3 0 013.7-1.867l5.686 1.746a3.006 3.006 0 012.008 3.684zM12 13c0 1.45-1.544 3.391-2.714 4.378a1.991 1.991 0 01-2.572 0C5.544 16.391 4 14.45 4 13a2 2 0 014 0 2 2 0 014 0z"></path>
            </svg>
            <span
              className={`${
                !mobileMenuOpen &&
                (isShowSmallBar ? "hidden" : "hidden lg:block")
              }`}
            >
              {t("explore")}
            </span>
          </Link>
          <Link
            href="/create"
            className={`${isCurrentSelectedPage(
              ROUTES.CREATE,
              EQUAL_OR_INCLUDE.EQUAL
            )} font-bold text-sm flex gap-4 items-center hover:text-primary-color`}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="512"
              height="512"
              viewBox="0 0 24 24"
              className="w-6 h-6"
            >
              <path
                fill="currentColor"
                d="m10 19l-2.5-5.5L2 11l5.5-2.5L10 3l2.5 5.5L18 11l-5.5 2.5L10 19Zm8 2l-1.25-2.75L14 17l2.75-1.25L18 13l1.25 2.75L22 17l-2.75 1.25L18 21Z"
              ></path>
            </svg>
            <span
              className={`${
                !mobileMenuOpen &&
                (isShowSmallBar ? "hidden" : "hidden lg:block")
              }`}
            >
              {t("create")}
            </span>
          </Link>
          <Link
            href="/create-image"
            className={`${isCurrentSelectedPage(
              "/create-image",
              EQUAL_OR_INCLUDE.EQUAL
            )} font-bold text-sm flex gap-4 items-center hover:text-primary-color`}
          >
            <Images />
            <span
              className={`${
                !mobileMenuOpen &&
                (isShowSmallBar ? "hidden" : "hidden lg:block")
              }`}
            >
              {t("createImage")}
            </span>
          </Link>

          {userEntity && !userEntity.guest_flag && (
            <Link
              href={ROUTES.MY_CREATIONS}
              className={`${isCurrentSelectedPage(
                ROUTES.MY_CREATIONS,
                EQUAL_OR_INCLUDE.EQUAL
              )} font-bold text-sm flex gap-4 items-center hover:text-primary-color`}
            >
              <Album className="w-6 h-6" />
              <span
                className={`${
                  !mobileMenuOpen &&
                  (isShowSmallBar ? "hidden" : "hidden lg:block")
                }`}
              >
                {t("myCreations")}
              </span>
            </Link>
          )}

          {userEntity && !userEntity.guest_flag && (
            <Link
              href="/account-info"
              className={`${isCurrentSelectedPage(
                ROUTES.ACCOUNT_INFO,
                EQUAL_OR_INCLUDE.EQUAL
              )} font-bold text-sm flex gap-4 items-center hover:text-primary-color`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                data-name="Layer 1"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="w-6 h-6"
              >
                <path d="m12,0C5.383,0,0,5.383,0,12s5.383,12,12,12,12-5.383,12-12S18.617,0,12,0Zm-4,21.164v-.164c0-2.206,1.794-4,4-4s4,1.794,4,4v.164c-1.226.537-2.578.836-4,.836s-2.774-.299-4-.836Zm9.925-1.113c-.456-2.859-2.939-5.051-5.925-5.051s-5.468,2.192-5.925,5.051c-2.47-1.823-4.075-4.753-4.075-8.051C2,6.486,6.486,2,12,2s10,4.486,10,10c0,3.298-1.605,6.228-4.075,8.051Zm-5.925-15.051c-2.206,0-4,1.794-4,4s1.794,4,4,4,4-1.794,4-4-1.794-4-4-4Zm0,6c-1.103,0-2-.897-2-2s.897-2,2-2,2,.897,2,2-.897,2-2,2Z"></path>
              </svg>
              <span
                className={`${
                  !mobileMenuOpen &&
                  (isShowSmallBar ? "hidden" : "hidden lg:block")
                }`}
              >
                {t("profile")}
              </span>
            </Link>
          )}
          {userEntity?.entitlement?.member_level?.[0] !== 2 && (
            <Link
              href="/subscribe"
              className={`${isCurrentSelectedPage(
                ROUTES.SUBSCRIBE,
                EQUAL_OR_INCLUDE.EQUAL
              )} font-bold text-sm flex gap-4 items-center hover:text-primary-color`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="512"
                height="512"
                viewBox="0 0 24 24"
                className="w-6 h-6"
              >
                <path
                  fill="currentColor"
                  d="M22.766 4.566a1.994 1.994 0 00-2.18.434L18 7.586 13.414 3a2 2 0 00-2.828 0L6 7.586 3.414 5A2 2 0 000 6.414V17a5.006 5.006 0 005 5h14a5.006 5.006 0 005-5V6.414a2 2 0 00-1.234-1.848zM22 17a3 3 0 01-3 3H5a3 3 0 01-3-3V6.414l3.293 3.293a1 1 0 001.414 0L12 4.414l5.293 5.293a1 1 0 001.414 0L22 6.414z"
                ></path>
              </svg>
              <span
                className={`${
                  !mobileMenuOpen &&
                  (isShowSmallBar ? "hidden" : "hidden lg:block")
                }`}
              >
                {t("joinPremium")}
              </span>
            </Link>
          )}
          <Link
            href={ROUTES.COINS}
            className={`${isCurrentSelectedPage(
              ROUTES.COINS,
              EQUAL_OR_INCLUDE.EQUAL
            )} font-bold text-sm flex gap-4 items-center hover:text-primary-color`}
          >
            <MonetizationOnIcon className="w-6 h-6" />
            <span
              className={`${
                !mobileMenuOpen &&
                (isShowSmallBar ? "hidden" : "hidden lg:block")
              }`}
            >
              {t("coins")}
            </span>
          </Link>
        </div>
        {(!isLargeScreen || mobileMenuOpen) &&
          !isShowSmallBar &&
          templateTags.length > 0 && (
            <div className="flex flex-col gap-2 my-4 pt-8 border-t border-[#fff]/10">
              <h4 className="text-base font-medium text-white mb-2">
                {t("explore")}
              </h4>
              <div className="text-sm text-white mb-2">{t("categories")}</div>
              {templateTags.map((tag) => (
                <Link
                  href={`/tags/${tag}`}
                  key={tag}
                  className={`flex items-center gap-2 px-3 py-2 text-sm text-white hover:bg-[#3a3b3c] max-w-full ${
                    tag ===
                    decodeURIComponent(pathname.split("/")?.pop?.() || "")
                      ? "bg-primary-color/80"
                      : ""
                  } rounded-lg cursor-pointer transition-colors`}
                >
                  <span className="truncate w-full text-ellipsis">{tag}</span>
                </Link>
              ))}
            </div>
          )}
      </div>

      <div className="flex flex-col gap-3 justify-start lg:border-t border-white/10 pt-4">
        {!isShowSmallBar && !isTagPage && (
          <>
            <div className="px-2 py-1">
              <GlobalNSFWSwitch isSideBar={isSideBar} />
            </div>
            <div>
              <div
                onClick={handleLanguageClick}
                className={`flex items-center gap-2 text-white cursor-pointer hover:text-primary-color px-2 py-1 rounded-lg transition-colors justify-start`}
              >
                <LanguageIcon className="w-5 h-5" />
                <>
                  <span className="hidden lg:block uppercase">{locale}</span>
                  <KeyboardArrowDownIcon className="hidden lg:block w-4 h-4" />
                </>
              </div>

              <Popover
                open={Boolean(anchorEl)}
                anchorEl={anchorEl}
                onClose={handleLanguageClose}
                anchorOrigin={{
                  vertical: "bottom",
                  horizontal: "center",
                }}
                transformOrigin={{
                  vertical: "top",
                  horizontal: "center",
                }}
                className="mt-1"
                slotProps={{
                  paper: {
                    className: "!bg-transparent !shadow-none",
                  },
                }}
              >
                <div className="bg-dark-bg border border-[#fff]/10 rounded-lg py-1 min-w-[80px]">
                  {routing.locales.map((lang) => (
                    <div
                      key={lang}
                      onClick={() => handleLanguageChange(lang)}
                      className={`px-4 py-2 cursor-pointer hover:bg-[#fff]/5 transition-colors text-center
                    ${locale === lang ? "text-primary-color" : "text-white"}
                  `}
                    >
                      {lang.toUpperCase()}
                    </div>
                  ))}
                </div>
              </Popover>
            </div>
          </>
        )}

        <div className="flex flex-row items-center sm:gap-5 gap-1">
          {userEntity && !userEntity.guest_flag ? (
            <ProfileMenu />
          ) : (
            <div
              onClick={() => changeLoginDialogState(true)}
              className="cursor-pointer w-full"
            >
              {isShowSmallBar ? (
                <ExitToAppIcon className="hover:text-primary-color" />
              ) : (
                <Button className="!text-white w-full text-[18px] font-bold flex flex-row justify-center items-center normal-case bg-gradient-to-r from-primary-color to-secondary-color/80 pl-[10px] pr-[10px] h-[40px]">
                  {t("login")}
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </nav>
  );

  return (
    <>
      {/* more than 1024 */}
      <div
        className={`hidden sm:flex select-none py-6 px-7 h-screen max-h-[100dvh] ${
          isShowSmallBar ? "w-[90px]" : "lg:w-[240px]"
        } w-[90px] border-r-2 border-[#fff]/10`}
      >
        {navigationContent()}
      </div>

      {/* less than 1024 */}
      <div
        className={`flex items-center sm:hidden py-2 px-4 w-screen bg-dark-bg h-[50px] ${
          isShowSmallBar ? "hidden" : "block"
        }`}
      >
        <div className="text-[18px] text-white flex justify-between items-center w-full">
          <div className="flex items-center">
            <MenuIcon
              className="w-6 h-6 mr-2 cursor-pointer"
              onClick={handleMobileMenuToggle}
            />
            <Link className={`text-[24px] font-bold text-white`} href="/">
              {getTitle()}
            </Link>
          </div>
          <div className="flex items-center gap-4">
            <GlobalNSFWSwitch />
            <div
              onClick={handleLanguageClick}
              className="flex items-center gap-1 text-white cursor-pointer hover:text-primary-color"
            >
              <LanguageIcon className="w-5 h-5" />
              <span className="uppercase text-sm">{locale}</span>
              <KeyboardArrowDownIcon className="w-4 h-4" />
            </div>

            {userEntity && !userEntity.guest_flag ? (
              <ProfileMenu />
            ) : (
              <div
                onClick={() => changeLoginDialogState(true)}
                className="cursor-pointer w-full"
              >
                <Button className="rounded-xl !text-white w-full text-[18px] font-bold flex flex-row justify-center items-center normal-case bg-gradient-to-r from-primary-color to-secondary-color/80 pl-[10px] pr-[10px] h-[40px]">
                  {t("login")}
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Drawer */}
      <Drawer
        anchor="left"
        open={mobileMenuOpen}
        onClose={handleMobileMenuToggle}
        sx={{
          "& .MuiDrawer-paper": {
            width: "160px",
            backgroundColor: "#282c34",
            borderRight: "2px solid rgba(255, 255, 255, 0.1)",
            padding: "24px 8px",
          },
        }}
      >
        {navigationContent({ isSideBar: true })}
      </Drawer>

      <LoginModal
        open={loginDialogOpen}
        onClose={() => changeLoginDialogState(false)}
      />
    </>
  );
};

export default Navbar;
