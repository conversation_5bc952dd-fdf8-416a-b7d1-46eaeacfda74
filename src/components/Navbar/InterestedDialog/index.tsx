import React, { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { Dialog } from "@mui/material";
import { Error as ErrorIcon } from "@mui/icons-material";

import { apiService } from "@/service/Api";
import { ImageGenStyleItem } from "@/types";
import { getDomainTitle } from "@/utils/domain";
import { useRouter } from "@/i18n/routing";
import InterestImageList from "@/components/InterestImageList";

const InterestedDialog = () => {
  const t = useTranslations("OtherTranslations");
  const router = useRouter();

  const [interestInList, setInterestInList] = useState<ImageGenStyleItem[]>([]);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [title, setTitle] = useState("");

  useEffect(() => {
    if (typeof window !== "undefined") {
      const title = getDomainTitle(window.location.hostname);
      setTitle(title);
      const hasInterestType = document.cookie.includes("interested_in=");
      setOpenEditDialog(!hasInterestType && interestInList.length > 0);
    }
  }, [interestInList.length]);

  useEffect(() => {
    apiService.getInterestInList().then((res) => {
      if (res.code === 200 && res.data) {
        setInterestInList(
          res.data.interested_in_list.map((item) => ({
            index: item.index,
            name: item.title,
            example_image_url: item.image_url,
          }))
        );
      }
    });
  }, []);
  return (
    <Dialog
      open={openEditDialog}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: "#1A1B1E",
          color: "white",
          borderRadius: "16px",
          padding: {
            xs: "6px",
            sm: "24px",
          },
          maxWidth: "800px",
        },
      }}
    >
      <div className="text-center sm:mb-4">
        <h1 className="text-3xl font-bold m-0 font-sans">
          <span className="text-pink-500 font-bold">{title}</span>
        </h1>
        <h2 className="text-2xl font-bold m-2 text-white">
          {t("interested_in")}
        </h2>
      </div>
      <InterestImageList
        interestInList={interestInList}
        onSelect={(style) => {
          setOpenEditDialog(false);
          document.cookie = `interested_in=${style.name}; path=/`;
          router.refresh();
        }}
      />

      <div className="flex items-center justify-center text-[#A1A1AA] sm:text-sm text-xs">
        <ErrorIcon sx={{ fontSize: "14px", mr: 0.5 }} />
        {t("youCanUpdateYourPreferencesLaterInProfileSettings")}
      </div>
    </Dialog>
  );
};

export default InterestedDialog;
