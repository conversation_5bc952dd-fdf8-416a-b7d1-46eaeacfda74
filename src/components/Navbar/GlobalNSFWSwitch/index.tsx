"use client";

import React from "react";
import NSFWSwitch from "@/components/Navbar/NSFWSwitch";
import NSFWConfirmDialog from "@/components/NSFWConfirmDialog";
import { useNSFWSwitch } from "@/hooks/useNSFWSwitch";

const GlobalNSFWSwitch = ({ isSideBar = false }: { isSideBar?: boolean }) => {
  const {
    globalNSFWStatus,
    openNSFWDialog,
    setOpenNSFWDialog,
    handleChangeNSFW,
    handleConfirmNSFW,
  } = useNSFWSwitch();

  return (
    <>
      <NSFWSwitch
        isSideBar={isSideBar}
        checkNSFW={globalNSFWStatus}
        handleChangeNSFW={handleChangeNSFW}
      />
      {openNSFWDialog && (
        <NSFWConfirmDialog
          open={openNSFWDialog}
          onClose={() => setOpenNSFWDialog(false)}
          onConfirm={() => handleConfirmNSFW(true)}
        />
      )}
    </>
  );
};

export default GlobalNSFWSwitch;
