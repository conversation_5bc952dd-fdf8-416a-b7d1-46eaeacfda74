"use client";

import React from "react";
import { Switch } from "@mui/material";
import clsx from "clsx";

const NSFWSwitch = ({
  isSideBar = false,
  checkNSFW,
  handleChangeNSFW,
}: {
  isSideBar?: boolean;
  checkNSFW: boolean;
  handleChangeNSFW: () => void;
}) => {
  return (
    <div className="flex items-center">
      <span
        className={clsx(
          "text-primary-color",
          isSideBar ? "block mr-3" : "lg:block hidden"
        )}
      >
        NSFW
      </span>
      <Switch
        checked={checkNSFW}
        onChange={handleChangeNSFW}
        className="-mx-4 lg:mx-0"
        sx={{
          "& .MuiSwitch-switchBase.Mui-checked": {
            color: "var(--primary-color)",
            "&:hover": {
              backgroundColor: "rgba(var(--primary-color-pure), 0.04)",
            },
          },
          "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
            backgroundColor: "var(--primary-color)",
          },
        }}
      />
    </div>
  );
};

export default NSFWSwitch;
