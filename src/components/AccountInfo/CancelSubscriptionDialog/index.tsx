import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslations } from "next-intl";
import React from "react";

interface CancelSubscriptionDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading?: boolean;
}

const CancelSubscriptionDialog = ({
  open,
  onClose,
  onConfirm,
  isLoading = false,
}: CancelSubscriptionDialogProps) => {
  const t = useTranslations("accountPage");

  return (
    <Dialog
      open={open}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: "#1A1B1E",
          color: "white",
          borderRadius: "16px",
          backgroundImage:
            "linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.4)",
          position: "relative",
        },
      }}
    >
      <IconButton
        onClick={onClose}
        sx={{
          position: "absolute",
          right: 8,
          top: 8,
          color: "white",
          "&:hover": {
            backgroundColor: "rgba(255, 255, 255, 0.1)",
          },
        }}
      >
        <CloseIcon />
      </IconButton>
      <DialogTitle
        sx={{
          color: "white",
          textAlign: "center",
          fontSize: { xs: 20, sm: 24 },
          fontWeight: 600,
          pt: 4,
          pb: 2,
        }}
      >
        {t("cancelSubscription")}
      </DialogTitle>
      <DialogContent sx={{ px: { xs: 2, sm: 4 }, pb: 1 }}>
        <div className="text-white/80 space-y-4">
          <p className="mb-4 text-base leading-relaxed">
            {t("cancelSubscriptionConfirmation")}
          </p>
        </div>
      </DialogContent>
      <DialogActions sx={{ pb: 2, gap: 1, justifyContent: "center" }}>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{
            color: "white",
            borderColor: "white",
            py: 1,
            px: 4,
            borderRadius: "8px",
            textTransform: "none",
            fontSize: "16px",
            "&:hover": {
              borderColor: "white",
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
          }}
        >
          {t("cancel")}
        </Button>
        <Button
          onClick={onConfirm}
          variant="contained"
          disabled={isLoading}
          sx={{
            backgroundColor: "var(--primary-color)",
            color: "white",
            py: 1,
            px: 4,
            borderRadius: "8px",
            textTransform: "none",
            fontSize: "16px",
            "&:hover": {
              backgroundColor: "var(--primary-color)",
            },
          }}
        >
          {isLoading ? t("processing") : t("confirm")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CancelSubscriptionDialog;
