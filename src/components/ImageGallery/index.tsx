"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Download, Trash } from "lucide-react";
import Lightbox from "react-image-lightbox";
import "react-image-lightbox/style.css";
import { downloadFiles } from "@/utils/utils";
import { apiService } from "@/service/Api";
import { showToast } from "@/utils/toast";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button,
} from "@mui/material";

interface ImageGalleryProps {
  images: { id: string; url: string; index: number }[];
  setImages: (images: { id: string; url: string; index: number }[]) => void;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({ images, setImages }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [photoIndex, setPhotoIndex] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [imageToDelete, setImageToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteClick = (id: string) => {
    setImageToDelete(id);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!imageToDelete) return;

    try {
      setIsDeleting(true);
      const res = await apiService.DeleteGalleryImage({
        product_id: imageToDelete.split("&")[0],
        segment_index: parseInt(imageToDelete.split("&")[1]),
      });

      if (res.code === 200) {
        showToast("Delete image successfully", "success");
        const newImages = images.filter((item) => item.id !== imageToDelete);
        setImages(newImages);
      } else {
        showToast(res.message || "Delete image failed", "error");
      }
    } catch (error) {
      showToast("Error occurred while deleting image", "error");
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setImageToDelete(null);
    }
  };

  const EmptyState = () => (
    <div className="flex flex-col items-center justify-center py-16 px-4">
      <div className="relative mb-6">
        <div className="absolute inset-0 w-24 h-24 bg-gradient-to-br from-primary-color/20 to-secondary-color/20 rounded-full animate-pulse" />

        <div className="relative w-24 h-24 bg-gray-light rounded-full flex items-center justify-center">
          <svg
            className="w-12 h-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        </div>
      </div>

      <h3 className="text-xl font-semibold text-white mb-2">No images yet</h3>

      <p className="text-gray-400 text-center max-w-md leading-relaxed">
        Your created images will appear here. Start creating some amazing
        artwork to build your gallery!
      </p>
    </div>
  );

  return (
    <>
      {images.length === 0 ? (
        <EmptyState />
      ) : (
        <div className="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4 py-3">
          {images.map((image, index) => (
            <div key={image.id} className="rounded-lg overflow-hidden relative">
              <div className="aspect-[2/3] relative cursor-pointer group">
                <Image
                  src={image.url}
                  width={1024}
                  height={1536}
                  alt=""
                  className="w-full h-full object-cover"
                  onClick={() => {
                    setPhotoIndex(index);
                    setIsOpen(true);
                  }}
                />
                <div className="absolute top-2 right-2 flex items-center gap-1 sm:gap-2 opacity-100 sm:opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="bg-red-500 rounded-full p-1">
                    <Trash
                      className="w-4 h-4 sm:w-6 sm:h-6 cursor-pointer text-white"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteClick(image.id);
                      }}
                    />
                  </div>
                  <div className="bg-black bg-opacity-50 rounded-full p-1">
                    <Download
                      className="w-4 h-4 sm:w-6 sm:h-6 cursor-pointer text-white"
                      onClick={(e) => {
                        e.stopPropagation();
                        downloadFiles([image.url]);
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      {isOpen && (
        <Lightbox
          mainSrc={images[photoIndex].url}
          nextSrc={images[(photoIndex + 1) % images.length].url}
          prevSrc={images[(photoIndex + images.length - 1) % images.length].url}
          onCloseRequest={() => setIsOpen(false)}
          onMovePrevRequest={() =>
            setPhotoIndex((photoIndex + images.length - 1) % images.length)
          }
          onMoveNextRequest={() =>
            setPhotoIndex((photoIndex + 1) % images.length)
          }
        />
      )}

      <Dialog
        open={deleteDialogOpen}
        onClose={() => !isDeleting && setDeleteDialogOpen(false)}
        PaperProps={{
          sx: {
            backgroundColor: "#1a1a1a",
            color: "white",
            borderRadius: "8px",
            padding: { xs: "12px", sm: "16px" },
            width: "100%",
            maxWidth: { xs: "90%", sm: "400px" },
            margin: { xs: "16px", sm: "32px" },
          },
        }}
      >
        <DialogTitle
          sx={{
            padding: 0,
            marginBottom: { xs: "12px", sm: "16px" },
            fontSize: { xs: "1.1rem", sm: "1.25rem" },
            fontWeight: "bold",
            color: "white",
          }}
        >
          Delete Confirm
        </DialogTitle>
        <DialogContent
          sx={{
            padding: 0,
            marginBottom: { xs: "16px", sm: "24px" },
          }}
        >
          <DialogContentText
            sx={{
              color: "white",
              fontSize: { xs: "0.9rem", sm: "1rem" },
            }}
          >
            Are you sure to delete this creation? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions
          sx={{
            padding: 0,
            display: "flex",
            justifyContent: "flex-end",
            flexDirection: { xs: "row", sm: "row" },
            gap: { xs: "8px", sm: "12px" },
          }}
        >
          <Button
            onClick={() => setDeleteDialogOpen(false)}
            disabled={isDeleting}
            sx={{
              textTransform: "uppercase",
              color: "white",
              fontWeight: "bold",
              padding: { xs: "6px 12px", sm: "8px 16px" },
              fontSize: { xs: "0.85rem", sm: "0.875rem" },
              minWidth: { xs: "80px", sm: "100px" },
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            disabled={isDeleting}
            sx={{
              textTransform: "uppercase",
              backgroundColor: "#ff4444",
              color: "white",
              fontWeight: "bold",
              padding: { xs: "6px 12px", sm: "8px 16px" },
              fontSize: { xs: "0.85rem", sm: "0.875rem" },
              borderRadius: "20px",
              minWidth: { xs: "80px", sm: "100px" },
              "&:hover": {
                backgroundColor: "#ff6666",
              },
              "&:disabled": {
                backgroundColor: "#ff4444",
                opacity: 0.5,
                color: "white",
              },
            }}
          >
            {isDeleting ? "Deleting..." : "Confirm"}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ImageGallery;
