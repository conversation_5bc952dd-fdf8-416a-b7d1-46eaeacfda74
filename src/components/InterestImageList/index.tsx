"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import { ImageGenStyleItem } from "@/types";

const InterestImageList = ({
  interestInList,
  onSelect,
}: {
  interestInList: ImageGenStyleItem[];
  onSelect: (style: ImageGenStyleItem) => void;
}) => {
  const [selectedStyleName, setSelectedStyleName] = useState<string>("");
  useEffect(() => {
    const interested_in = document.cookie
      ?.split(";")
      ?.find((c) => c.trim().startsWith("interested_in="));
    if (interested_in) {
      setSelectedStyleName(interested_in?.split("=")[1]);
    }
  }, [interestInList]);
  return (
    <div className="grid grid-cols-3 gap-4 p-4">
      {interestInList.map((style) => (
        <div
          key={style.index}
          onClick={() => {
            onSelect(style);
            setSelectedStyleName(style.name);
          }}
          className={`p-2 rounded-lg cursor-pointer overflow-hidden relative aspect-square group ${
            style.name === selectedStyleName
              ? "border-2 border-primary-color"
              : ""
          }`}
        >
          <Image
            src={style.example_image_url}
            alt={style.name}
            width={1024}
            height={1024}
            className="rounded-lg w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
          />
          <div
            className={`absolute inset-0 bg-opacity-50  hover:opacity-20 transition-opacity duration-300`}
          ></div>
          <div className="absolute bottom-4 left-4 flex items-center gap-2">
            <span className="text-lg font-medium text-white">{style.name}</span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default InterestImageList;
