import { FC, useEffect, useState } from "react";
import { Box, Typography } from "@mui/material";

interface CardLoadingProps {
  width?: number;
  height?: number;
  circleSize?: number;
  color?: string;
}

const CardLoading: FC<CardLoadingProps> = ({
  width,
  height,
  circleSize = 60,
  color = "var(--primary-color)",
}) => {
  const [progress, setProgress] = useState(0);
  useEffect(() => {
    const timer = setInterval(() => {
      setProgress((prevPercentage) => {
        if (prevPercentage >= 99) {
          clearInterval(timer);
          return 99;
        }
        return prevPercentage + 1;
      });
    }, 450);
    return () => clearInterval(timer);
  }, []);

  return (
    <Box
      className="flex items-center justify-center -m-3 bg-gradient-to-br from-primary-color/30 via-primary-color/50 to-secondary-color/60"
      sx={{
        width: width,
        height: height,
        position: "relative",
      }}
    >
      <Box
        className="absolute w-full h-full rounded-full"
        sx={{
          width: circleSize,
          height: circleSize,
          margin: "auto",
          border: `3px solid rgba(var(--primary-color-pure), 0.2)`,
          borderTopColor: color,
          animation: "spin 1s linear infinite",
          "@keyframes spin": {
            "0%": { transform: "rotate(0deg)" },
            "100%": { transform: "rotate(360deg)" },
          },
        }}
      />
      <Typography
        sx={{
          color: "#fff",
          fontWeight: "bold",
          fontSize: width ? width * 0.08 : "16px",
          transition: "all 0.1s ease-out",
        }}
      >
        {`${Math.round(progress)}%`}
      </Typography>
    </Box>
  );
};

export default CardLoading;
