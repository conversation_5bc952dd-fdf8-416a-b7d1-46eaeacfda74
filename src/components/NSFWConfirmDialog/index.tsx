import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslations } from "next-intl";

const NSFWConfirmDialog = ({
  open,
  onClose,
  onConfirm,
}: {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
}) => {
  const t = useTranslations("OtherTranslations");
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="xs"
      PaperProps={{
        style: {
          backgroundColor: "#23272d",
          color: "white",
          borderRadius: "8px",
          minWidth: "300px",
        },
      }}
    >
      {/* Close button */}
      <IconButton
        onClick={onClose}
        sx={{
          position: "absolute",
          right: 8,
          top: 8,
          color: "grey.500",
          backgroundColor: "#fff1",
          "&:hover": {
            backgroundColor: "#fff2",
          },
        }}
      >
        <CloseIcon sx={{ fontSize: "1rem" }} />
      </IconButton>

      {/* Title */}
      <DialogTitle
        sx={{
          color: "white",
          pb: 0,
          pt: 2,
          fontWeight: "bold",
        }}
      >
        {t("confirmYourAge")}
      </DialogTitle>

      {/* Content */}
      <DialogContent>
        <Typography
          sx={{
            color: "rgba(255, 255, 255, 0.7)",
            fontSize: "0.9rem",
            mt: 1,
          }}
        >
          {t("confirmYourAgeDescription")}
        </Typography>
      </DialogContent>

      {/* Actions */}
      <DialogActions sx={{ p: 2, pt: 0 }}>
        <Button
          onClick={onClose}
          sx={{
            color: "white",
            backgroundColor: "rgba(255, 255, 255, 0.1)",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.2)",
            },
            textTransform: "none",
            px: 3,
          }}
        >
          {t("goBack")}
        </Button>
        <Button
          onClick={onConfirm}
          variant="contained"
          sx={{
            backgroundColor: "var(--primary-color)",
            textTransform: "none",
            px: 3,
          }}
        >
          {t("iMOver18")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default NSFWConfirmDialog;
