const Loading = () => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-900">
      <div className="flex flex-col items-center gap-4">
        <div className="text-xl text-primary-color font-mono animate-pulse">
          Loading...
        </div>
        <div className="flex gap-2">
          {[0, 1, 2].map((index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full bg-primary-color animate-bounce ${
                index === 1 ? "delay-200" : index === 2 ? "delay-400" : ""
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Loading;
