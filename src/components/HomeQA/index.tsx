import React from "react";
import { ChevronDown } from "lucide-react";

const faqData = [
  {
    question: "What Is NSFW AI Chat, and What Is Its Core Experience?",
    answer:
      "NSFW AI chat merges cutting-edge AI technology with interactive adult entertainment. At its essence, it provides free sex chat, voice chat, and adult images, allowing users to engage with a variety of AI companions. Participants can enjoy deeply immersive conversations with AI characters, exploring their fantasies in a secure and private environment. This innovative approach is transforming the concept of intimacy through advanced AI.",
  },
  {
    question: "Can I Have Unfiltered Free Sex Chat in www.sweet.ai?",
    answer:
      "Yes, www.sweet.ai offers unfiltered free sex chat with a variety of AI characters, allowing users to explore their fantasies in a safe and private environment.",
  },
  {
    question: "How to Use AI Image Generator on www.sweet.ai?",
    answer:
      "To utilize the AI image generator on Sweet.ai, choose or customize your character, specify your desired theme or scenario, and let the AI produce captivating visuals. ",
  },
  {
    question:
      "What Makes www.sweet.ai Stand Out as One of the Best NSFW & Sex AI Products?",
    answer:
      "Sweet.ai stands out as a premier NSFW AI platform by offering free sex chat, an enticing experience, and a sophisticated AI image generator. Its AI characters and customizable sex AI companions provide an unparalleled experience, merging intimacy, creativity, and interactivity. The platform’s user-friendly interface and premium features redefine the possibilities of NSFW AI entertainment.",
  },
  {
    question: "What Are the Best NSFW AI Generators for Adults in 2025?",
    answer:
      "In 2025, top NSFW AI generators include www.sweet.ai, known for its diverse character options and high-quality outputs, alongside other emerging platforms catering to adult content creation.",
  },
  {
    question: "What About Voice Chat & Video Features in www.sweet.ai?",
    answer:
      "www.sweet.ai offers voice chat capabilities, with plans to expand video features, enhancing the immersive experience with lifelike interactions.",
  },
  {
    question: "Where Can I Discover the Best Anime & Hentai AI?",
    answer:
      "For the best anime and hentai AI, www.sweet.ai provides a wide range of stylized characters, perfect for fans of this genre, available through their platform.",
  },
  {
    question:
      "How can I cancel or get a refund for my subscription in www.sweet.ai?",
    answer:
      "To cancel or request a refund for your www.sweet.ai subscription, visit the account settings page, follow the cancellation steps, and contact support for refund inquiries.",
  },
];

const QASection: React.FC = () => {
  return (
    <section className="px-2 md:py-12 md:px-4 sm:px-6 lg:px-8">
      <div className="mx-auto">
        <div className="mb-12 bg-gradient-to-r from-purple-800 via-purple-600 to-pink-600 p-4 md:p-10 rounded-lg shadow-lg">
          {/* H1 Heading */}
          <h1 className="md:text-3xl text-xl font-bold text-white md:mb-10 mb-4">
            Best NSFW AI: Free Sex Chat, Image Generator, Characters for Adults
          </h1>
          {/* H2 Sections */}
          <div className="space-y-1 ">
            <h2 className="text-sm font-semibold text-gray-200">
              Free uncensored sexy and immersive chatting, your dream girls/guys
              awaits
            </h2>
            <h2 className="text-sm font-semibold text-gray-200">
              Generate hot AI images as you wish, unlock albums of each charming
              characters
            </h2>
            <h2 className="text-sm font-semibold text-gray-200">
              10000+ diverse sexy characters, e.g. milf, waifu, horny, BDSM etc.
              in private chat rooms
            </h2>
          </div>
        </div>

        {/* FAQ Section */}
        <div>
          <h2 className="md:text-3xl text-xl font-bold text-center text-white mb-8">
            Frequently Asked Questions
          </h2>
          <div className="space-y-4">
            {faqData.map((faq, index) => (
              <details
                className="group border border-white/10 rounded-md"
                key={index}
              >
                <summary className="flex justify-between items-center md:p-4 p-2 group-open:bg-gradient-to-r group-open:from-purple-800/10 group-open:via-purple-600 group-open:to-pink-600/80 text-white cursor-pointer transition-colors duration-300">
                  <h3 className="md:text-xl text-md font-medium">
                    {faq.question}
                  </h3>
                  <ChevronDown className="w-6 h-6 transform transition-transform group-open:-rotate-180" />
                </summary>
                <div className="group-open:bg-gradient-to-r group-open:from-purple-800/10 group-open:via-purple-600 group-open:to-pink-600/80 p-4 text-gray-300">
                  {faq.answer}
                </div>
              </details>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default QASection;
