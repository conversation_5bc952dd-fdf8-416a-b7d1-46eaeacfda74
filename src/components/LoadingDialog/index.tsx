import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  CircularProgress,
} from "@mui/material";
import React from "react";

interface IProps {
  show: boolean;
  msg: string;
  title: string;
  onCancel: () => void;
}

export function LoadingDialog(props: IProps) {
  const { show, msg, onCancel, title } = props;
  return (
    <Dialog open={show}>
      <DialogTitle className="bg-[#2A2B3D] text-white w-96">
        <CircularProgress size={24} className="mr-[10px]" color="inherit" />
        {title}
      </DialogTitle>
      <DialogContent className="text-white bg-[#2A2B3D]">{msg}</DialogContent>
      <DialogActions className="bg-[#2A2B3D]">
        <Button
          variant="text"
          color="secondary"
          onClick={(e) => {
            e.stopPropagation();
            onCancel();
          }}
          className="mr-1 !text-white hover:!text-primary-color"
        >
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
}
