import React from "react";
import { Skeleton, Box } from "@mui/material";

const ProfileCustomSkeleton = () => {
  return (
    <div className="grid gap-4 grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {Array.from({ length: 6 }, (_, index) => (
        <div key={index}>
          <Box
            sx={{
              borderRadius: 4,
              overflow: "hidden",
              bgcolor: "#1E293B",
              height: "100%",
              backdropFilter: "blur(8px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            <Skeleton
              variant="rectangular"
              width="100%"
              animation="wave"
              sx={{
                bgcolor: "rgba(255, 255, 255, 0.1)",
                height: { xs: 160, sm: 240 },
              }}
            />

            <Box sx={{ p: 2 }}>
              <Skeleton
                variant="text"
                width="75%"
                height={32}
                sx={{
                  bgcolor: "rgba(255, 255, 255, 0.1)",
                  mb: 1,
                }}
              />

              <Box sx={{ mb: 2 }}>
                <Skeleton
                  variant="text"
                  width="100%"
                  sx={{ bgcolor: "rgba(255, 255, 255, 0.1)" }}
                />
                <Skeleton
                  variant="text"
                  width="85%"
                  sx={{ bgcolor: "rgba(255, 255, 255, 0.1)" }}
                />
              </Box>

              <Box sx={{ display: "flex", gap: 1, mb: 2, flexWrap: "wrap" }}>
                <Skeleton
                  variant="rounded"
                  width={64}
                  height={24}
                  sx={{ bgcolor: "rgba(255, 255, 255, 0.1)" }}
                />
                <Skeleton
                  variant="rounded"
                  width={80}
                  height={24}
                  sx={{ bgcolor: "rgba(255, 255, 255, 0.1)" }}
                />
              </Box>

              <Skeleton
                variant="rounded"
                width={48}
                height={48}
                sx={{ bgcolor: "rgba(255, 255, 255, 0.1)" }}
              />
            </Box>
          </Box>
        </div>
      ))}
    </div>
  );
};

export default ProfileCustomSkeleton;
