import { TemplateGenderEnum, TemplateStyleEnum } from "@/types";
import React from "react";

// 导入 lucide-react 图标
import {
  Mars,
  Venus,
  LucideTransgender,
  PawPrint,
  LucideUser,
} from "lucide-react";
import { STORAGE_KEY } from "@/constant";

const FilterSearch = ({
  loading,
  gender,
  style,
  setGender,
  setStyle,
  resetAndReload,
}: {
  loading: boolean;
  gender?: TemplateGenderEnum;
  style?: TemplateStyleEnum;
  setGender: (gender: TemplateGenderEnum) => void;
  setStyle: (style: TemplateStyleEnum) => void;
  resetAndReload: () => void;
}) => {
  const renderStyle = (
    current: TemplateGenderEnum | TemplateStyleEnum | undefined,
    compare: TemplateGenderEnum | TemplateStyleEnum
  ) => {
    if (current === compare) {
      return "bg-pink-600 text-white";
    }
    return "bg-gradient-to-r from-gray-700 to-gray-900 text-white shadow-md hover:shadow-lg transition-all duration-300";
  };

  const handleGenderChange = (newGender: TemplateGenderEnum) => {
    if (gender !== newGender && !loading) {
      setGender(newGender);
      localStorage.setItem(STORAGE_KEY.GENDER, newGender);
      resetAndReload();
    }
  };

  const handleStyleChange = (newStyle: TemplateStyleEnum) => {
    if (style !== newStyle && !loading) {
      setStyle(newStyle);
      localStorage.setItem(STORAGE_KEY.STYLE, newStyle);
      resetAndReload();
    }
  };

  return (
    <div className="flex flex-row gap-4 mb-6 p-4 pt-0 bg-gradient-to-b from-gray-800 to-black rounded-lg shadow-xl">
      {/* 性别选择 */}
      <div className="flex gap-4 justify-center pr-4 border-r-2 border-primary-color">
        <button
          disabled={loading}
          className={`flex items-center gap-2 sm:px-4 sm:py-2 p-2 py-1 rounded-2xl border-2 border-transparent hover:border-pink-600 ${renderStyle(
            gender,
            TemplateGenderEnum.Female
          )} transition-all duration-300`}
          onClick={() => handleGenderChange(TemplateGenderEnum.Female)}
        >
          <Venus className="w-6 h-6" />
          <span>Female</span>
        </button>
        <button
          disabled={loading}
          className={`flex items-center gap-2 sm:px-4 sm:py-2 p-2 py-1 rounded-2xl border-2 border-transparent hover:border-pink-600 ${renderStyle(
            gender,
            TemplateGenderEnum.Male
          )} transition-all duration-300`}
          onClick={() => handleGenderChange(TemplateGenderEnum.Male)}
        >
          <Mars className="w-6 h-6" />
          <span>Male</span>
        </button>
        <button
          disabled={loading}
          className={`flex items-center gap-2 sm:px-4 sm:py-2 p-2 py-1 rounded-2xl border-2 border-transparent hover:primary-color ${renderStyle(
            gender,
            TemplateGenderEnum.Trans
          )} transition-all duration-300`}
          onClick={() => handleGenderChange(TemplateGenderEnum.Trans)}
        >
          <LucideTransgender className="w-6 h-6" />
          <span>Trans</span>
        </button>
      </div>
      {/* 风格选择 */}
      <div className="flex gap-4 justify-center">
        <button
          disabled={loading}
          className={`flex items-center gap-2 sm:px-4 sm:py-2 p-2 py-1 rounded-2xl border-2 border-transparent hover:border-primary-color ${renderStyle(
            style,
            TemplateStyleEnum.Realistic
          )} transition-all duration-300`}
          onClick={() => handleStyleChange(TemplateStyleEnum.Realistic)}
        >
          <LucideUser className="w-6 h-6" />
          <span>Realistic</span>
        </button>
        <button
          disabled={loading}
          className={`flex items-center gap-2 sm:px-4 sm:py-2 p-2 py-1 rounded-2xl border-2 border-transparent hover:border-primary-500 ${renderStyle(
            style,
            TemplateStyleEnum.Anime
          )} transition-all duration-300`}
          onClick={() => handleStyleChange(TemplateStyleEnum.Anime)}
        >
          <PawPrint className="w-6 h-6" />
          <span>Anime</span>
        </button>
      </div>
    </div>
  );
};

export default FilterSearch;
