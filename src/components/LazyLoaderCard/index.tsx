"use client";

import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
import CardBox from "../SoulChat/SoulmateCard/CardBox";
import { apiService } from "@/service/Api";
import {
  BotTemplate,
  HomeListRequestLazyLoader,
  TemplateGenderEnum,
  TemplateStyleEnum,
} from "@/types";
import { Skeleton } from "@mui/material";
import { uuidToBase64 } from "@/utils/utils";
import useResponsive from "@/hooks/useResponsive";
import FilterSearch from "./_components/FilterSearch";
import { STORAGE_KEY } from "@/constant";

const LazyLoaderCard = () => {
  const [homeCards, setHomeCards] = useState<BotTemplate[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [gender, setGender] = useState<TemplateGenderEnum>();
  const [style, setStyle] = useState<TemplateStyleEnum>();
  const totalLength = useRef(0);
  const sentinelRef = useRef<HTMLDivElement>(null);
  const isLoading = useRef(false);
  const { isSmallScreen } = useResponsive();

  const resetAndReload = () => {
    setHomeCards([]);
    setPage(1);
    totalLength.current = 0;
  };

  useEffect(() => {
    const rootContainer = document.querySelector(".root-container");
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          if (!isLoading.current) setPage((prev) => prev + 1);
        }
      },
      {
        root: isSmallScreen ? null : rootContainer,
        rootMargin: "800px",
        threshold: 0,
      }
    );

    if (sentinelRef.current) {
      observer.observe(sentinelRef.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (isLoading.current) return;

    const fetchHomeCards = async () => {
      isLoading.current = true;
      setLoading(true);
      const params = {
        page,
        encoded_uuids: homeCards.map((card) => uuidToBase64(card.id)),
        interested_in:
          localStorage.getItem(STORAGE_KEY.GENDER) ?? TemplateGenderEnum.Female,
      } as HomeListRequestLazyLoader;
      const storage_style = localStorage.getItem(STORAGE_KEY.STYLE);
      if (storage_style) params.style = storage_style as TemplateStyleEnum;

      const res = await apiService.getHomeListForLazyLoader(params);
      setHomeCards((prevCards) => [...prevCards, ...res.data.bot_templates]);
      totalLength.current = res.data.total_count;
      isLoading.current = false;
      setLoading(false);
    };
    if (totalLength.current !== homeCards.length || homeCards.length === 0) {
      fetchHomeCards();
    }
  }, [page, gender, style]);

  useEffect(() => {
    const storage_gender =
      localStorage.getItem(STORAGE_KEY.GENDER) ?? TemplateGenderEnum.Female;
    setGender(storage_gender as TemplateGenderEnum);
    const storage_style = localStorage.getItem(STORAGE_KEY.STYLE);

    if (storage_style) {
      setStyle(storage_style as TemplateStyleEnum);
    }
  }, []);
  return (
    <div className="overflow-hidden">
      <FilterSearch
        loading={loading}
        gender={gender}
        style={style}
        setGender={setGender}
        setStyle={setStyle}
        resetAndReload={resetAndReload}
      />
      <div className="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4 py-3">
        {homeCards.length
          ? homeCards.map((soulmate, index) => (
              <CardBox key={soulmate.id} soulmate={soulmate} index={index} />
            ))
          : Array.from({ length: 20 }).map((_, index) => (
              <Skeleton key={index} variant="rounded" height={350} />
            ))}
        <div ref={sentinelRef} className="h-1" />
      </div>
      {loading && homeCards.length > 0 && (
        <div className="flex justify-center items-center gap-2 p-4">
          <div className="w-2 h-2 bg-primary-color rounded-full animate-bounce [animation-duration:0.5s]"></div>
          <div className="w-2 h-2 bg-primary-color rounded-full animate-bounce [animation-duration:0.5s] [animation-delay:0.1s]"></div>
          <div className="w-2 h-2 bg-primary-color rounded-full animate-bounce [animation-duration:0.5s] [animation-delay:0.2s]"></div>
        </div>
      )}
    </div>
  );
};

export default LazyLoaderCard;
