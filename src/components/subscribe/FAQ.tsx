import React from "react";
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import { ItemBullet } from "./ItemBullet";

export function FAQ() {
  const [expanded, setExpanded] = React.useState<string | false>(false);

  const handleChange =
    (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpanded(isExpanded ? panel : false);
    };
  return (
    <div className=" flex flex-col justify-center items-center  w-full rounded-[20px] pb-[10px] pt-[10px] mt-[10px]">
      <Accordion
        className="bg-dark-bg w-full"
        expanded={expanded === "panel1"}
        onChange={handleChange("panel1")}
      >
        <AccordionSummary
          sx={{
            overflow: "hidden",
            boxShadow: "0px",
            color: "red",
            backgroundColor: "rgb(25,27,32)",
          }}
          className="bg-dark-bg "
          aria-controls="panel1bh-content"
          id="panel1bh-header"
        >
          <div className="text-white select-none w-full flex justify-start items-center">
            <ItemBullet></ItemBullet>
            What payment services do you support
          </div>
        </AccordionSummary>
        <AccordionDetails
          sx={{
            backgroundColor: "rgb(25,27,32)",
          }}
        >
          <div className="text-white flex justify-start">
            We accept all major credit cards
          </div>
        </AccordionDetails>
      </Accordion>

      <Accordion
        sx={{}}
        className="bg-red-500  bg-dark-bg w-full"
        expanded={expanded === "panel2"}
        onChange={handleChange("panel2")}
      >
        <AccordionSummary
          sx={{
            overflow: "hidden",
            boxShadow: "0px",
            backgroundColor: "rgb(25,27,32)",
          }}
          className="bg-dark-bg "
          aria-controls="panel2bh-content"
          id="panel2bh-header"
        >
          <div className="text-white   w-full flex justify-start items-center">
            <ItemBullet></ItemBullet>
            Is this a secure site for purchases
          </div>
        </AccordionSummary>
        <AccordionDetails
          sx={{
            borderTop: "1px solid gary",
            backgroundColor: "rgb(25,27,32)",
          }}
        >
          <div className="text-left text-white w-full flex justify-start">
            We work with Stripe, which guarantees your safety and security
          </div>
        </AccordionDetails>
      </Accordion>

      <Accordion
        sx={{}}
        className="bg-red-500  bg-dark-bg w-full"
        expanded={expanded === "panel3"}
        onChange={handleChange("panel3")}
      >
        <AccordionSummary
          sx={{
            overflow: "hidden",
            boxShadow: "0px",
            backgroundColor: "rgb(25,27,32)",
          }}
          className="bg-dark-bg "
          aria-controls="panel3bh-content"
          id="panel3bh-header"
        >
          <div className="text-left text-white w-full flex justify-start items-center">
            <ItemBullet></ItemBullet>
            Can I cancel my subscription
          </div>
        </AccordionSummary>
        <AccordionDetails
          sx={{
            borderTop: "1px solid gary",
            backgroundColor: "rgb(25,27,32)",
          }}
        >
          <div className="text-left text-white flex justify-start">
            Monthly subscriptions are billed month to month
          </div>
        </AccordionDetails>
      </Accordion>

      <Accordion
        sx={{}}
        className="bg-red-500  bg-dark-bg w-full"
        expanded={expanded === "panel4"}
        onChange={handleChange("panel4")}
      >
        <AccordionSummary
          sx={{
            overflow: "hidden",
            boxShadow: "0px",
            backgroundColor: "rgb(25,27,32)",
          }}
          className="bg-dark-bg "
          aria-controls="panel4bh-content"
          id="panel4bh-header"
        >
          <div className="text-white   w-full flex justify-start items-center">
            <ItemBullet></ItemBullet>
            Can I update my card details
          </div>
        </AccordionSummary>
        <AccordionDetails
          sx={{
            borderTop: "1px solid gary",
            backgroundColor: "rgb(25,27,32)",
          }}
        >
          <div className="text-left text-white flex justify-start">
            Go to the billing section of your account and update your payment
            information
          </div>
        </AccordionDetails>
      </Accordion>
    </div>
  );
}
