import React from "react";
import { useRouter } from "@/i18n/routing";

export function TeamsService() {
  const router = useRouter();
  const gotoTeamsOfService = () => {
    router.push("/tos");
  };

  const gotoPrivacy = () => {
    router.push("/pp");
  };
  return (
    <div className="flex mt-[20px] mb-[20px] text-gray-d  text-[14px]  flex-row  justify-center items-center">
      <div
        onClick={gotoTeamsOfService}
        className="select-none flex hover:cursor-pointer hover:text-white flex-row ml-2 mr-2"
      >
        Teams of service
      </div>
      <div> & </div>
      <div
        onClick={gotoPrivacy}
        className="select-none flex hover:cursor-pointer hover:text-white flex-row ml-2 "
      >
        Privacy policy
      </div>
    </div>
  );
}
