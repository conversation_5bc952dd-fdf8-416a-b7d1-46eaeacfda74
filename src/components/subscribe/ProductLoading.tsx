import { SUBSCRIPTION_TYPE } from "@/constant";
import { Skeleton } from "@mui/material";
import React from "react";

const ProductLoading = ({
  subscription_type,
}: {
  subscription_type: (typeof SUBSCRIPTION_TYPE)[keyof typeof SUBSCRIPTION_TYPE];
}) => {
  return subscription_type === SUBSCRIPTION_TYPE.AUTO_RENEWABLE ? (
    <div className="w-full flex flex-col items-center mt-10">
      <div className="mb-12 text-center">
        <Skeleton variant="text" width={200} height={48} className="mb-2" />
        <Skeleton variant="text" width={280} height={24} />
      </div>

      <div className="w-full flex justify-center sm:gap-6 flex-wrap">
        {[1, 2].map((item) => (
          <div key={item} className="flex-1 min-w-[280px] max-w-[400px] mb-6">
            <div className="relative h-full flex flex-col mt-5 border-2 rounded-xl p-6 bg-[#22242a] border-transparent">
              <Skeleton
                variant="text"
                width={120}
                height={40}
                className="mb-4"
              />
              <Skeleton
                variant="text"
                width={80}
                height={60}
                className="mb-2"
              />
              <Skeleton
                variant="text"
                width={200}
                height={24}
                className="mb-6"
              />

              {[1, 2, 3, 4].map((line) => (
                <div key={line} className="flex items-center gap-2 mb-4">
                  <Skeleton variant="circular" width={20} height={20} />
                  <Skeleton variant="text" width={200} height={24} />
                </div>
              ))}

              <Skeleton
                variant="rectangular"
                height={48}
                className="mt-4 rounded-[10px]"
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  ) : (
    <div className="w-full flex justify-center gap-4 flex-wrap mt-10">
      {[1, 2, 3].map((item) => (
        <div
          key={item}
          className={`relative bg-[#22242a] rounded-xl p-6 min-w-[280px] ${
            item === 3 ? "border-primary-color border-[1px]" : ""
          }`}
        >
          {item > 1 && (
            <div className="absolute -top-2 right-4">
              <Skeleton
                variant="rectangular"
                width={80}
                height={24}
                className="!rounded-full"
              />
            </div>
          )}

          <div className="flex items-center gap-2 mb-4">
            <Skeleton variant="circular" width={24} height={24} />
            <Skeleton variant="text" width={80} height={32} />
          </div>

          <Skeleton variant="text" width={200} height={24} className="mb-6" />

          <Skeleton
            variant="rectangular"
            height={40}
            className="!rounded-full mt-4"
          />
        </div>
      ))}
    </div>
  );
};

export default ProductLoading;
