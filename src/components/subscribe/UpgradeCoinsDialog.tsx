import React from "react";
import { Dialog, DialogContent, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import SecurityIcon from "@mui/icons-material/Security";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import Image from "next/image";
import { useTranslations } from "next-intl";

interface UpgradeCoinsDialogProps {
  open: boolean;
  onClose: () => void;
  onUpgrade: () => void;
  imgSrc: string;
  desc1?: string;
  desc2?: string;
  desc3?: string;
}

export const UpgradeCoinsDialog: React.FC<UpgradeCoinsDialogProps> = ({
  open,
  onClose,
  onUpgrade,
  imgSrc,
  desc1,
  desc2,
  desc3,
}) => {
  const t = useTranslations("SubscribePage");
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          bgcolor: "#1e293b",
          color: "white",
          width: "350px",
        },
      }}
    >
      <IconButton
        onClick={onClose}
        sx={{
          position: "absolute",
          right: 8,
          top: 8,
          color: "grey.500",
          zIndex: 2,
          bgcolor: "rgba(255, 255, 255, 0.1)",
          width: "20px",
          height: "20px",
        }}
      >
        <CloseIcon sx={{ width: 16, height: 16 }} />
      </IconButton>

      <DialogContent sx={{ p: 0, position: "relative" }}>
        <div>
          <Image
            width={350}
            height={500}
            src={imgSrc}
            alt="Character"
            priority
            loading="eager"
            quality={75}
            style={{
              width: "350px",
              height: "500px",
              objectFit: "cover",
              objectPosition: "center",
            }}
          />
        </div>

        <div className="p-3 absolute bottom-0 w-full pb-2 bg-gradient-to-b from-transparent to-black/70">
          <div className="text-base">{desc1 || t("getMoreCoins")}</div>
          <div className="text-base font-bold mb-1">
            {desc2 || t("characterAudioVoices")}
          </div>
          <div className="text-xs text-gray-400 mb-4">
            {desc3 || t("upgradeNowToListenToYourCharacterVoices")}
          </div>

          <button
            className="bg-primary-color text-base text-white font-bold py-2 px-4 rounded-full mb-2 w-full"
            onClick={onUpgrade}
          >
            {t("upgradeNow")}
          </button>

          <div className="flex justify-center gap-5 text-gray-500 text-[10px] mb-1">
            <div className="flex items-center gap-0.5">
              <SecurityIcon fontSize="small" />
              <span>{t("safeAndSecurePayments")}</span>
            </div>
            <div className="flex items-center gap-0.5">
              <AccountBalanceIcon fontSize="small" />
              <span>{t("privacyInBankTransaction")}</span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
