"use client";
import React, { useState } from "react";
import { useSubscribeStores } from "@/stores/SubscribeStores";
import { Check as CheckIcon } from "@mui/icons-material";
import { Button, CircularProgress, Tabs, Tab } from "@mui/material";
import { styled } from "@mui/material/styles";
import {
  BILLING_CYCLE,
  SUBSCRIBE_PRODUCT_PRO,
  SUBSCRIPTION_TYPE,
} from "@/constant";
import { useTranslations } from "next-intl";
import { CrownIcon, DiamondIcon, DiscountArrowIcon } from "@/utils/svg";
import { useUserStores } from "@/stores/UserStores";
import ProductLoading from "@/components/subscribe/ProductLoading";

const StyledTabs = styled(Tabs)({
  minHeight: "40px",
  "& .MuiTabs-indicator": {
    display: "none",
  },
  backgroundColor: "#1e293b",
  padding: "4px",
  borderRadius: "12px",
  width: "fit-content",
});

const StyledTab = styled(Tab)({
  color: "#94a3b8 !important",
  minHeight: "32px",
  padding: "6px 16px",
  borderRadius: "8px",
  textTransform: "none",
  fontSize: "14px",
  fontWeight: 500,
  "&.Mui-selected": {
    color: "#fff !important",
    backgroundColor: "#2e3b4e",
  },
});

const RightsCard = ({
  openPay,
  paymentStatus,
}: {
  openPay: (isUpgrade?: boolean) => void;
  paymentStatus: string;
}) => {
  const t = useTranslations("SubscribePage");
  const { products, selectProduct, selectProductId, memberUpgrading } =
    useSubscribeStores();
  const [billingCycle, setBillingCycle] = useState<
    (typeof BILLING_CYCLE)[keyof typeof BILLING_CYCLE]
  >(BILLING_CYCLE.MONTHLY);
  const { userEntity } = useUserStores();

  const handleBillingChange = (
    event: React.SyntheticEvent,
    newValue: "monthly" | "yearly"
  ) => {
    setBillingCycle(newValue);
  };

  return (
    <div className="w-full flex flex-col items-center mt-10">
      <div className="mb-12 text-center">
        <h2 className="mt-2 text-2xl font-bold tracking-tight text-gray-900 sm:text-4xl">
          Choose your plan
        </h2>
        <p className="text-muted-foreground text-sm font-medium text-gray-400">
          100% anonymous. You can cancel anytime.
        </p>
      </div>
      <div className="relative w-fit">
        {products.length > 1 && (
          <>
            <div className="absolute -top-8 right-6 bg-primary-color text-white text-xs px-2 py-0.5 rounded-full flex items-center gap-1">
              -30%
              <div className="absolute left-full top-1 rotate-30">
                <DiscountArrowIcon />
              </div>
            </div>
            <StyledTabs
              value={billingCycle}
              onChange={handleBillingChange}
              aria-label="billing cycle tabs"
            >
              <StyledTab label={t("monthly")} value={BILLING_CYCLE.MONTHLY} />
              <StyledTab label={t("yearly")} value={BILLING_CYCLE.YEARLY} />
            </StyledTabs>
          </>
        )}
      </div>

      {/* Product Cards */}
      <div className="mb-8 w-full flex justify-center sm:gap-6 cursor-pointer flex-wrap">
        {(products.length === 1
          ? products
          : products?.filter(
              (product) =>
                product.month_period ===
                (billingCycle === BILLING_CYCLE.MONTHLY ? 1 : 12)
            )
        )?.map((item) => {
          const selected = item.id === selectProductId;
          return (
            <div
              className="flex-1 min-w-[280px] max-w-[400px] mb-6"
              key={item.id}
            >
              <div
                className={`relative h-full flex flex-col mt-5 border-2 
                rounded-xl p-6 ${
                  item.title?.includes(SUBSCRIBE_PRODUCT_PRO)
                    ? "bg-primary-color/10 border-primary-color"
                    : "bg-[#22242a] border-transparent"
                }`}
              >
                <div className="flex-1">
                  <div className="flex justify-between items-center mb-4">
                    <div className="flex items-center gap-2">
                      <span className="text-white font-bold text-2xl flex gap-2 items-center">
                        {!item.title?.includes(SUBSCRIBE_PRODUCT_PRO) ? (
                          <DiamondIcon />
                        ) : (
                          <CrownIcon />
                        )}
                        {item.title}
                      </span>
                    </div>
                    {item?.title?.includes(SUBSCRIBE_PRODUCT_PRO) && (
                      <span className="bg-primary-color text-white text-sm px-2 py-1 rounded-full">
                        Best Value
                      </span>
                    )}
                  </div>
                  <div>
                    {billingCycle === BILLING_CYCLE.YEARLY && (
                      <span className="mb-0 rounded bg-primary-color px-2 py-1 text-xs font-semibold text-white">
                        {item.discount_label}
                      </span>
                    )}
                  </div>

                  {/* Price section */}
                  <div className="flex flex-col items-baseline mb-6">
                    {item.ProratedCharge ? (
                      <div className="text-white text-[1.6rem] font-bold mt-2">
                        Prorated charge ${item.ProratedCharge}
                      </div>
                    ) : (
                      <>
                        <div>
                          <span className="text-white text-[3rem] font-bold">
                            $
                            {billingCycle === BILLING_CYCLE.YEARLY
                              ? item.price_per_month
                              : item.amount}
                          </span>
                          <span className="text-gray-400 text-sm text-muted-foreground">
                            /month
                          </span>
                        </div>
                        {billingCycle === BILLING_CYCLE.YEARLY && (
                          <div>
                            <span>or</span>
                            <span className="mx-2 text-base font-semibold">
                              ${item.amount}/year
                            </span>
                            <span className="line-through text-base font-semibold text-red-400">
                              ${item.origin_amount}/year
                            </span>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                  {/* description */}
                  <div className="text-muted-foreground my-2 overflow-hidden text-muted-foreground md:mb-6 md:mt-4">
                    {item.description}
                  </div>

                  {/* Rights list */}
                  <div className="space-y-4">
                    {item.rights?.map((right, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <CheckIcon
                          fontSize="small"
                          className="text-primary-color"
                        />
                        <span className="text-white">{right}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <Button
                  onClick={() => {
                    selectProduct(item);
                    openPay(userEntity?.entitlement?.member_level?.[0] === 1);
                  }}
                  disabled={memberUpgrading}
                  className="!font-bold !text-white !mt-4 bg-gradient-to-r normal-case from-primary-color to-secondary-color/80 flex flex-row justify-center items-center bg-gray border-gray border-2 !rounded-[10px]"
                >
                  <div className="flex gap-8">
                    {paymentStatus === "creating" && selected && (
                      <CircularProgress
                        className="h-50 w-50 text-gray-900/50 mr-[10px]"
                        color="primary"
                      />
                    )}
                  </div>
                  <div>
                    {userEntity?.entitlement?.member_level?.[0] === 1
                      ? memberUpgrading
                        ? "Upgrading..."
                        : "Upgrade"
                      : "Subscribe"}
                  </div>
                </Button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const CreditsCard = ({
  openPay,
  paymentStatus,
}: {
  openPay: () => void;
  paymentStatus: string;
}) => {
  const { products, selectProduct, selectProductId } = useSubscribeStores();

  return (
    <div className="mb-8 sm:mt-10 w-full pl-[5%] pr-[5%] lg:pl-[0%] lg:pr-[0%] lg:w-1/2">
      {products.map((item) => {
        const selected = item.id === selectProductId;
        return (
          <div
            key={item.id}
            onClick={() => {
              selectProduct(item);
            }}
            className={`relative flex mt-5 flex-row justify-between w-full ${
              selected ? "border-primary-color" : "border-gray-d"
            }  ${
              selected ? "border-[2px]" : "border-[2px]"
            } rounded-xl p-2 pt-3 pb-3`}
          >
            <div className="flex flex-row ">
              <div className="text-white  font-bold text-[18px] pl-3">
                {item.name.split(" ")[0]}
              </div>
              <div className="text-white text-[16px]  ml-aut pl-1 flex flex-col justify-end">
                <div>{item.name.split(" ")[1]}</div>
              </div>
            </div>
            <div className="flex flex-row items-center pr-3">
              <div className="text-white font-bold text-[18px] ml-1">
                ${item.amount}
              </div>
            </div>
            <div className="absolute top-[-10px] left-[10px]"></div>
            {item.discount_label && (
              <div className="absolute top-[-11px] left-4 bg-red-500 h-[20px] rounded-[12.5px] text-white text-[12px] font-bold pl-2 pr-2">
                {item.discount_label}
              </div>
            )}
          </div>
        );
      })}
      <Button
        onClick={() => {
          openPay();
        }}
        className="w-full sm:!mt-8 !mt-4 !font-bold !text-white bg-gradient-to-r normal-case from-primary-color to-secondary-color flex flex-row justify-center items-center sm:!text-[22px] bg-gray  border-gray border-2 !rounded-[10px] h-[50px]"
      >
        <div className="flex gap-8">
          {paymentStatus === "creating" && (
            <CircularProgress
              className="h-50 w-50 text-gray-900/50 mr-[10px]"
              color="primary"
            />
          )}
        </div>
        <div>Subscribe</div>
      </Button>
    </div>
  );
};

export function ProductList({
  openPay,
  paymentStatus,
  subscription_type,
}: {
  openPay: (isUpgrade?: boolean) => void;
  paymentStatus: string;
  subscription_type: (typeof SUBSCRIPTION_TYPE)[keyof typeof SUBSCRIPTION_TYPE];
}) {
  const { productLoading, products } = useSubscribeStores();

  if (productLoading || !products?.length) {
    return <ProductLoading subscription_type={subscription_type} />;
  }

  return subscription_type === SUBSCRIPTION_TYPE.AUTO_RENEWABLE ? (
    <RightsCard openPay={openPay} paymentStatus={paymentStatus} />
  ) : (
    <CreditsCard openPay={openPay} paymentStatus={paymentStatus} />
  );
}
