import Image from "next/image";
import React from "react";
import { useTranslations } from "next-intl";

const PriceBanner = () => {
  const t = useTranslations("SubscribePage");
  return (
    <div className="justify-between items-center sm:mt-20 mt-4 flex bg-price-banner w-full h-[200px] p-4 bg-cover rounded-2xl  bg-primary-color">
      <div className="w-[280px] h-[300px] object-cover relative left-8 -top-[50px] hidden sm:block">
        <Image
          src="https://s3.us-east-2.amazonaws.com/www.deloris-ai.com/character/delorisai_20250108150835.webp"
          alt="a girl"
          className="w-full h-full object-cover"
          width={280}
          height={300}
        />
      </div>
      <div className="flex-1 sm:ml-16">
        <h3 className="text-white text-2xl sm:text-4xl font-bold">
          {t("enhanceYourExperience")}
        </h3>
        <p className="text-white sm:text-lg font-medium">
          {t("enhanceYourExperienceDescription")}
        </p>
      </div>
    </div>
  );
};

export default PriceBanner;
