import React from "react";
import {
  ArrowForwardIos as ArrowForwardIosIcon,
  ArrowBackIosNew as ArrowBackIosNewIcon,
} from "@mui/icons-material";
import { Link } from "@/i18n/routing";
import { SOULMATE_PAGE_SIZE } from "@/constant";
import { apiService } from "@/service/Api";
import CardBox from "../SoulChat/SoulmateCard/CardBox";
import {
  HomeListRequestLazyLoader,
  TemplateGenderEnum,
  TemplateStyleEnum,
} from "@/types/soulchat";

async function fetchPaginationList(req: HomeListRequestLazyLoader) {
  const homeCardList = (await apiService.getHomeListForLazyLoader(req)).data;
  return {
    soulmateList: homeCardList?.bot_templates || [],
    total_count: homeCardList?.total_count || 0,
  };
}
const PaginationCard = async ({
  page,
  pageSize = SOULMATE_PAGE_SIZE,
  interested_in,
  style,
}: {
  page: number;
  pageSize?: number;
  interested_in?: string;
  style?: string;
}) => {
  const req = {
    page,
    page_size: pageSize,
  } as HomeListRequestLazyLoader;
  if (interested_in) {
    req.interested_in = interested_in as TemplateGenderEnum;
  }
  if (style) {
    req.style = style as TemplateStyleEnum;
  }

  const { soulmateList, total_count } = await fetchPaginationList(req);

  const queryParams = new URLSearchParams();
  if (interested_in) {
    queryParams.append("interested_in", interested_in);
  }
  if (style) {
    queryParams.append("style", style);
  }

  const href = queryParams.toString() ? `&${queryParams.toString()}` : "";

  return (
    <>
      <div className="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4 py-3">
        {soulmateList.map((soulmate, index) => (
          <CardBox key={soulmate.id} soulmate={soulmate} index={index} />
        ))}
      </div>
      <div className="flex justify-between items-center py-8 flex-wrap">
        <div className="flex gap-2 bg-gray-light p-2 rounded-lg flex-wrap mt-2">
          {Array.from(
            { length: Math.ceil(total_count / pageSize) },
            (_, index) => (
              <Link
                key={index}
                href={`?page=${index + 1}${href}`}
                className={`w-10 h-10 rounded-lg cursor-pointer flex justify-center items-center ${
                  page === index + 1 ? "bg-primary-color" : "bg-gray-dark"
                }`}
              >
                {index + 1}
              </Link>
            )
          )}
        </div>
        <div className="flex gap-2 justify-center items-center bg-gray-light rounded-lg p-2 mt-2">
          <Link
            href={page > 1 ? `?page=${page - 1}${href}` : `?page=1${href}`}
            className="w-10 h-10 bg-gray-dark rounded-lg cursor-pointer flex justify-center items-center"
          >
            <ArrowBackIosNewIcon />
          </Link>
          <Link
            href={
              page >= Math.ceil(total_count / pageSize)
                ? `?page=1${href}`
                : `?page=${page + 1}${href}`
            }
            className="w-10 h-10 bg-gray-dark rounded-lg cursor-pointer flex justify-center items-center"
          >
            <ArrowForwardIosIcon />
          </Link>
        </div>
      </div>
    </>
  );
};

export default PaginationCard;
