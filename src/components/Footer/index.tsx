"use client";
import { ROUTES } from "@/constant";
import Image from "next/image";
import { Link, usePathname } from "@/i18n/routing";
import React, { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { getDomainTitle } from "@/utils/domain";
import clsx from "clsx";

const blogList = [
  {
    href: "https://www.sweetai.chat/article/nsfw-ai-chat",
    text: "nsfw aichat",
  },
  {
    href: "https://www.sweetai.chat/article/chat-with-onlyfans",
    text: "chat-with-onlyfans",
  },
  {
    href: "https://www.sweetai.chat/article/chat-with-onlyfans",
    text: "reddit-dirty-talk",
  },
  {
    href: "https://www.sweetai.chat/article/1-on-1-sexy-chat",
    text: "1-on-1-sexy-chat",
  },
  {
    href: "https://www.sweetai.chat/article/femboy-ai-art-generator",
    text: "femboy-ai-art-generator",
  },
  {
    href: "https://www.sweetai.chat/article/ai-girlfriend-generator",
    text: "ai-girlfriend-generator",
  },
];

const legalList = [
  {
    href: ROUTES.PRIVACY_POLICY,
    text: "Privacy Policy",
  },
  {
    href: ROUTES.TERMS_OF_SERVICE,
    text: "Terms of Service",
  },
  {
    href: ROUTES.RETURN_POLICY,
    text: "Return Policy",
  },
  {
    href: ROUTES.COMMUNITY_GUIDELINES,
    text: "Community Guidelines",
  },
  {
    href: ROUTES.UNDERAGE_POLICY,
    text: "Underage Policy",
  },
  {
    href: ROUTES.CONTENT_REMOVAL_POLICY,
    text: "Content Removal Policy",
  },
  {
    href: ROUTES.BLOCKED_CONTENT_POLICY,
    text: "Blocked Content Policy",
  },
  {
    href: ROUTES.DMCA_POLICY,
    text: "DMCA Policy",
  },
  {
    href: ROUTES.COMPLAINT_POLICY,
    text: "Complaint Policy",
  },
  {
    href: ROUTES.USC_2257_EXEMPTION,
    text: "USC 2257 Exemption",
  },
];

const partnersList = [
  {
    href: "https://porntourist.com/?ref=deloris-ai",
    text: "porntourist",
    title: "porntourist",
    image: {
      src: "https://porntourist.com/static/images/brand/i.svg",
      width: 14,
      height: 16,
      alt: "porntourist",
    },
  },
  {
    href: "https://dokeyai.com/",
    text: "DokeyAI",
    title: "Dokey AI Tools Directory",
  },
  {
    href: "https://thepornlinks.com/",
    text: "ThePornLinks.com",
    title: "Best Porn Sites",
  },
  {
    href: "https://dokeyai.com/submit?url=https%3A%2F%2Fwww.perchanceai.chat",
    text: "dokeyai perchanceai",
    title: "Best Porn Sites",
  },
  {
    href: "https://huntai.ai/",
    text: "HuntAI.ai Tools Directory",
    title: "HuntAI.ai Tools Directory",
  },
  {
    href: "https://hentaipulse.com/ ",
    text: "Hentai Streaming",
    title: "Hentai Streaming",
  },
  {
    href: "https://porntoplinks.net ",
    text: "Porn Top Links",
    title: "Porn Top Links",
  },
  {
    href: "https://bestnsfw.io/",
    text: "bestnsfw.io",
    title: "bestnsfw.io",
  },
  {
    href: "https://sexualai.net",
    text: "sexualai.net",
    title: "sexualai.net",
  },
  {
    href: "https://ainavhub.com/",
    text: "AI NavHub Tools Directory",
    title: "AI NavHub Tools Directory",
  },
  {
    href: "https://beyondaiporn.com ",
    text: "Best AI NSFW Sites",
    title: "Best AI NSFW Sites",
  },
  {
    href: "https://www.toolpilot.ai/",
    text: "toolpilot",
    title: "toolpilot",
    hidden: true,
    image: {
      src: "https://www.toolpilot.ai/cdn/shop/files/tp-b-h.svg?v=1694516793",
      width: 150,
      height: 31,
      alt: "toolpilot",
    },
  },
  {
    href: "https://theresanaiforthat.com/ai/sweetai/?ref=featured&v=995943",
    text: "Theresanaiforthat",
    title: "Theresanaiforthat",
    hidden: true,
    image: {
      src: "https://media.theresanaiforthat.com/featured-on-taaft.png?width=600",
      width: 150,
      height: 31,
      alt: "Theresanaiforthat",
    },
  },
  {
    href: "https://www.cloudbooklet.com/",
    text: "cloudbooklet",
    title: "cloudbooklet",
    hidden: true,
    image: {
      src: "https://media.cloudbooklet.com/logo/cloudbooklet-logo.svg",
      width: 150,
      height: 31,
      alt: "cloudbooklet",
    },
  },
  {
    href: "https://nsfw.tools/",
    text: "nsfw.tools",
    title: "nsfw.tools",
    hidden: true,
    image: {
      src: "https://nsfw.tools/cdn/shop/files/nsfw-1000-w.jpg?v=1711462037&width=1500",
      width: 150,
      height: 31,
      alt: "nsfw.tools",
    },
  },
  {
    href: "https://thebestfetishsites.com",
    text: "thebestfetishsites",
    title: "thebestfetishsites",
  },
  {
    href: "https://www.thepornmap.net/",
    text: "thepornlist.net",
    title: "thepornlist.net",
  },
  {
    href: "https://thepornmap.com/",
    text: "thepornmap.com",
    title: "thepornmap.com",
  },
  {
    href: "https://fazier.com/",
    text: "fazier",
    title: "fazier",
    hidden: true,
    image: {
      src: "https://fazier.com/api/v1//public/badges/launch_badges.svg?badge_type=launched&theme=neutral",
      width: 103,
      height: 44,
      alt: "fazier",
    },
  },
];

const Footer: React.FC = () => {
  const pathname = usePathname();
  const [title, setTitle] = useState("NSFW Chat AI");
  const t = useTranslations("HomePage");
  useEffect(() => {
    if (typeof window !== "undefined") {
      const title = getDomainTitle(window.location.hostname);
      setTitle(title);
    }
  }, []);

  return (
    <footer
      className={`${
        [ROUTES.AI_CHAT, ROUTES.LOGIN, ROUTES.CREATE_IMAGE].some((route) =>
          pathname.includes(route)
        )
          ? "hidden"
          : ""
      } mb-2 bg-gradient-to-r mt-10 from-primary-color to-secondary-color/80 text-white p-6 rounded-lg`}
    >
      <div className="md:flex md:justify-between ">
        <Link href="/" className="text-2xl font-bold flex-1 mb-2">
          {title}
        </Link>
        <div className="grid grid-cols-1 gap-8 sm:gap-1 sm:grid-cols-4">
          <div>
            <h4 className="font-semibold mb-2">Legal</h4>
            <ul>
              {legalList.map((legal) => (
                <li key={legal.text}>
                  <Link href={legal.href} className="hover:underline">
                    {legal.text}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-2">{t("contact")}</h4>
            <ul>
              <li>
                <Link
                  href="mailto:<EMAIL>?subject=Feedback&body=Body%20Content"
                  className="hover:underline"
                >
                  {t("contactUs")}
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-2">{t("partners")}</h4>
            <ul className="flex flex-col gap-1">
              {partnersList.map((partner) => (
                <li key={partner.href}>
                  <Link
                    href={partner.href}
                    title={partner.title}
                    target="_blank"
                    className={clsx(
                      "hover:cursor-pointer hover:text-white flex-row",
                      { "flex flex-row items-center": partner.image }
                    )}
                  >
                    {partner.image && (
                      <Image
                        width={partner.image.width}
                        height={partner.image.height}
                        src={partner.image.src}
                        alt={partner.image.alt}
                      />
                    )}

                    <span
                      className={clsx({ hidden: partner.hidden }, "flex-row")}
                    >
                      {partner.text}
                    </span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-2">Blog</h4>
            <ul>
              {blogList.map((blog) => (
                <li key={blog.href}>
                  <Link
                    href={blog.href}
                    className="hover:underline"
                    target="_blank"
                  >
                    {blog.text}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
      <div className="mt-4"> 2025 {title}. All Rights Reserved.</div>
    </footer>
  );
};

export default Footer;
