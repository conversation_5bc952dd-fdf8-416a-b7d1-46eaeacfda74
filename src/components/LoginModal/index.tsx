"use client";

import { useGoogleLogin } from "@react-oauth/google";
import { useUserStores } from "@/stores/UserStores";
import React, { useEffect, useState } from "react";
import GoogleLoginBtn from "@/components/Login/GoogleLoginBtn";
import Image from "next/image";
import { Dialog, DialogContent, styled, IconButton } from "@mui/material";
import { Link, useRouter } from "@/i18n/routing";
import { firebaseLogEvent, getUtmParams } from "@/utils/utils";
import { EVENT_PAGE_LOGIN } from "@/utils/event";
import { ROUTES } from "@/constant";
import { useTranslations } from "next-intl";
import CloseIcon from "@mui/icons-material/Close";
import { getDomainTitle } from "@/utils/domain";

const StyledDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialog-paper": {
    backgroundColor: "rgba(20, 20, 25, 0.85)",
    backdropFilter: "blur(30px) saturate(1.8)",
    border: "1px solid rgba(255, 255, 255, 0.1)",
    color: "white",
    maxWidth: "460px",
    minHeight: "540px",
    width: "100%",
    margin: "16px",
    overflow: "hidden",
    position: "relative",
    borderRadius: "32px",
    boxShadow: `
      0 0 0 1px rgba(255, 255, 255, 0.05),
      0 50px 100px -20px rgba(0, 0, 0, 0.5)
    `,
  },
}));

export default function LoginModal({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) {
  const t = useTranslations("LoginPage");
  const { authGoogle, loading } = useUserStores();
  const [shouldClose, setShouldClose] = useState(false);
  const [title, setTitle] = useState("");
  const router = useRouter();
  const login = useGoogleLogin({
    onSuccess: async (tokenResponse) => {
      await authGoogle(tokenResponse.access_token, {
        ...getUtmParams(),
        user_agent: navigator.userAgent,
      });
      if (
        [ROUTES.AI_CHAT, ROUTES.ACCOUNT_INFO].some((path) =>
          window.location.pathname.includes(path)
        )
      ) {
        router.push(ROUTES.HOME);
      }
      onClose();
    },
    flow: "implicit",
  });

  useEffect(() => {
    if (typeof window !== "undefined") {
      const title = getDomainTitle(window.location.hostname);
      setTitle(title);
    }
    firebaseLogEvent(EVENT_PAGE_LOGIN);
  }, []);

  useEffect(() => {
    if (shouldClose && open) {
      onClose();
      setShouldClose(false);
    }
  }, [shouldClose, open, onClose]);

  return (
    <StyledDialog open={open} maxWidth="sm" fullWidth>
      <IconButton
        onClick={onClose}
        sx={{
          position: "absolute",
          right: 24,
          top: 24,
          color: "rgba(255,255,255,0.8)",
          backgroundColor: "rgba(255,255,255,0.03)",
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(255,255,255,0.05)",
          zIndex: 20,
          padding: "8px",
          transition: "all 0.2s ease",
          "&:hover": {
            backgroundColor: "rgba(255,255,255,0.1)",
            transform: "translateY(-1px)",
          },
        }}
      >
        <CloseIcon sx={{ fontSize: 20 }} />
      </IconButton>

      <div className="absolute inset-0 z-0">
        <Image
          alt="login_bg"
          src="/login_bg.webp"
          className="w-full h-full object-cover opacity-25 scale-105"
          width={1200}
          height={1800}
          priority
        />
        <div className="absolute inset-0 bg-[rgba(0,0,0,0.2)]" />
      </div>

      <DialogContent className="relative z-10 flex flex-col items-center justify-center px-12 py-16">
        <div className="w-full max-w-[85%] space-y-10">
          <div className="text-center">
            <h2 className="text-[2.75rem] font-bold tracking-tight mb-5 bg-clip-text text-transparent bg-gradient-to-b from-white to-gray-300">
              {t("signInTo")} {title}
            </h2>
            <p className="text-[1.1rem] text-gray-300/90 font-medium">
              {t("welcomeBack")}
            </p>
          </div>

          <div className="w-full space-y-8">
            <div className="relative group">
              <div className="absolute -inset-[1px] bg-white/[0.08] blur-lg rounded-2xl group-hover:bg-white/[0.12] transition-all duration-300"></div>
              <div className="relative w-full bg-white/[0.03] hover:bg-white/[0.05] p-7 rounded-2xl backdrop-blur-md border border-white/[0.05] transition-all duration-300">
                <GoogleLoginBtn
                  onClick={() => {
                    login();
                  }}
                  loading={loading}
                />
              </div>
            </div>

            <div className="text-center mt-10">
              <p className="text-[0.925rem] text-gray-400/90">
                {t("byContinuing")}{" "}
                <Link
                  href={ROUTES.TERMS_OF_SERVICE}
                  className="text-primary-color/90 hover:text-primary-color transition-colors"
                  onClick={() => setShouldClose(true)}
                >
                  {t("termsOfService")}
                </Link>{" "}
                {t("and")}{" "}
                <Link
                  href={ROUTES.PRIVACY_POLICY}
                  className="text-primary-color/90 hover:text-primary-color transition-colors"
                  onClick={() => setShouldClose(true)}
                >
                  {t("privacyPolicy")}
                </Link>
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </StyledDialog>
  );
}
