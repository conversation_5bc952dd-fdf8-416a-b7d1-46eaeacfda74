import {
  ArrowForwardIos as ArrowForwardIosIcon,
  ArrowBackIosNew as ArrowBackIosNewIcon,
} from "@mui/icons-material";

interface PaginationProps {
  currentPage: number;
  totalCount: number;
  pageSize: number;
  onHandlePageChange: (page: number) => void;
}

const Pagination = ({
  currentPage,
  totalCount,
  pageSize,
  onHandlePageChange,
}: PaginationProps) => {
  const totalPages = Math.ceil(totalCount / pageSize);

  const handlePageChange = (page: number) => {
    onHandlePageChange(page);
  };

  return (
    <div className="flex justify-between items-center py-8 flex-wrap">
      <div className="flex gap-2 bg-gray-light p-2 rounded-lg flex-wrap mt-2">
        {Array.from({ length: totalPages }, (_, index) => (
          <div
            key={index}
            onClick={() => {
              handlePageChange(index + 1);
            }}
            className={`w-10 h-10 rounded-lg cursor-pointer flex justify-center items-center ${
              currentPage === index + 1 ? "bg-primary-color" : "bg-gray-dark"
            }`}
          >
            {index + 1}
          </div>
        ))}
      </div>
      <div className="flex gap-2 justify-center items-center bg-gray-light rounded-lg p-2 mt-2">
        <div
          onClick={() => {
            if (currentPage === 1) return;
            handlePageChange(currentPage - 1);
          }}
          className="w-10 h-10 bg-gray-dark rounded-lg cursor-pointer flex justify-center items-center"
        >
          <ArrowBackIosNewIcon />
        </div>
        <div
          onClick={() => {
            if (currentPage === totalPages) return;
            handlePageChange(currentPage + 1);
          }}
          className="w-10 h-10 bg-gray-dark rounded-lg cursor-pointer flex justify-center items-center"
        >
          <ArrowForwardIosIcon />
        </div>
      </div>
    </div>
  );
};

export default Pagination;
