"use client";
import React, { useState, useEffect } from "react";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import { DOMAIN } from "@/constant";
import { useTranslations } from "next-intl";

const ConsentModal: React.FC = () => {
  const t = useTranslations("OtherTranslations");
  const [title, setTitle] = useState("NSFWChatAI");
  const [isOpen, setIsOpen] = useState(false);
  useEffect(() => {
    setIsOpen(JSON.parse(localStorage.getItem("consentModal") ?? "true"));
    if (typeof window !== "undefined") {
      const host = window.location.hostname;
      const title = host?.includes(DOMAIN.NSFWCHAT)
        ? "NSFWChatAI"
        : host?.includes(DOMAIN.PERCHANCEAI)
        ? "PerchanceAI"
        : "NSFWChatAI";
      setTitle(title);
    }
  }, []);
  return (
    <Dialog
      open={isOpen}
      fullWidth
      maxWidth="xs"
      sx={{
        "& .MuiPaper-root": {
          backgroundColor: "#1e293b",
          color: "#fff",
          borderRadius: "12px",
          padding: "16px",
        },
      }}
    >
      <DialogTitle
        sx={{
          fontSize: "1.5rem",
          fontWeight: "bold",
          mb: 2,
          borderBottom: "2px solid #334155",
          textAlign: "center",
        }}
      >
        {t("welcomeTo")} {title}!
      </DialogTitle>
      <DialogContent>
        <Typography>{t("pleaseNoteTheFollowing")}</Typography>
        <List sx={{ listStyleType: "decimal", pl: 2 }}>
          <ListItem sx={{ display: "list-item", pl: 0 }}>
            {t("thisSiteIsIntendedForUsersWhoAre")}
            <span className="text-primary-color">18 and older</span>.
          </ListItem>
          <ListItem sx={{ display: "list-item", pl: 0 }}>
            {t("weUtilizeCookiesToImproveYourExperienceOnOurSite")}
          </ListItem>
        </List>
      </DialogContent>
      <DialogActions sx={{ justifyContent: "center" }}>
        {/* <Button
          onClick={onClose}
          sx={{ textDecoration: "underline"}}
        >
          Manage Cookies
        </Button> */}
        <Button
          onClick={() => {
            localStorage.setItem("consentModal", "false");
            setIsOpen(false);
          }}
          variant="contained"
          className="!bg-primary-color text-white hover:!bg-primary-color/80"
        >
          {t("acceptAll")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConsentModal;
