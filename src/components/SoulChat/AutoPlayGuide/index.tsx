"use client";
import { useEffect } from "react";
import { AUTO_PLAY_KEY } from "@/constant";
import { VolumeUp } from "@mui/icons-material";
import { useTranslations } from "next-intl";
import { useUserStores } from "@/stores/UserStores";

interface AutoPlayGuideProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  setAutoPlay: (autoPlay: boolean) => void;
}

const AutoPlayGuide: React.FC<AutoPlayGuideProps> = ({
  open,
  setOpen,
  setAutoPlay,
}) => {
  const t = useTranslations("OtherTranslations");
  const { userEntity } = useUserStores();
  useEffect(() => {
    const isAutoPlaying = localStorage.getItem(AUTO_PLAY_KEY);
    if (
      isAutoPlaying === null &&
      userEntity?.entitlement?.member_level?.[0] === 2
    ) {
      setOpen(true);
    }
  }, [setOpen, userEntity]);

  const handleChoice = (choice: boolean) => {
    localStorage.setItem(AUTO_PLAY_KEY, String(choice));
    setOpen(false);
    setAutoPlay(choice);
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 px-4 backdrop-blur-sm">
      <div className="w-full max-w-md transform sm:space-y-5 space-y-2 rounded-3xl bg-gradient-to-b from-slate-800 to-slate-900 p-8 shadow-2xl transition-all duration-300">
        {/* Header */}
        <div className="text-center">
          <div className="inline-flex items-center justify-center rounded-full bg-primary-color/10 p-3">
            <VolumeUp
              className="h-6 w-6 text-primary-color"
              strokeWidth={2.5}
            />
          </div>
          <h2 className="mt-4 bg-gradient-to-r from-white via-white to-slate-300 bg-clip-text text-2xl font-bold text-transparent sm:text-3xl">
            {t("autoPlaySettings")}
          </h2>
        </div>

        {/* Content */}
        <p className="text-center text-base leading-relaxed text-slate-300 sm:text-lg">
          {t("autoPlaySettingsDescription")}
        </p>
        <p className="text-center text-sm leading-relaxed text-muted-foreground sm:text-base">
          {t("audioPlayPointsNotice")}
        </p>

        {/* Actions */}
        <div className="flex flex-col-reverse gap-4 sm:flex-row sm:justify-center">
          <button
            onClick={() => handleChoice(false)}
            className="group relative w-full overflow-hidden rounded-xl border border-slate-700 px-6 py-3 text-sm font-medium text-white transition-all duration-300 hover:border-slate-600 hover:bg-slate-800/50 focus:outline-none focus:ring-2 focus:ring-primary-color focus:ring-offset-2 focus:ring-offset-slate-900 sm:w-auto sm:min-w-[160px]"
            tabIndex={0}
            aria-label="Disable auto play"
          >
            <span className="relative z-10">{t("disableAutoPlay")}</span>
            <div className="absolute inset-0 -z-0 bg-gradient-to-r from-transparent via-slate-700/10 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
          </button>
          <button
            onClick={() => handleChoice(true)}
            className="group relative w-full overflow-hidden rounded-xl bg-primary-color px-6 py-3 text-sm font-medium text-white transition-all duration-300 hover:bg-primary-color/90 focus:outline-none focus:ring-2 focus:ring-primary-color focus:ring-offset-2 focus:ring-offset-slate-900 sm:w-auto sm:min-w-[160px]"
            tabIndex={0}
            aria-label="Enable auto play"
          >
            <span className="relative z-10">{t("enableAutoPlay")}</span>
            <div className="absolute inset-0 -z-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default AutoPlayGuide;
