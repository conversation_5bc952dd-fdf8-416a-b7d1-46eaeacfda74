import React, { useEffect } from "react";
import { Button, CircularProgress } from "@mui/material";
import { APP_ID_DELORIS, COST_TYPE, TaskType } from "@/constant";
import { useSoulChatStore } from "@/stores/SoulChatStore";
import { useUserStores } from "@/stores/UserStores";
import SubscribeModal from "../SubscribeModal";
import { showToast } from "@/utils/toast";
import { firebaseLogEvent } from "@/utils/utils";
import { EVENT_ACTION_SOULMATES_CREATE } from "@/utils/event";

export default function CreateButton() {
  const [open, setOpen] = React.useState(false);
  const {
    createPicID,
    addSoulmateDialogData,
    customCharacter,
    createProcessLoading,
    getPicByID,
    template_id,
  } = useSoulChatStore();
  const {
    soulmate_name,
    selectedFace,
    selectedOtherTags,
    voice_id,
    soulmate_age,
    personalityAndHobbiesTags,
    group_id,
  } = addSoulmateDialogData;

  const { userEntity } = useUserStores();

  useEffect(() => {
    if (template_id) {
      // 改成串行，需要返回template_id后，再调用生成四张图片
      // 去生成四张图片
      createPicID({
        project_novel_text: selectedOtherTags.join(","),
        face_model_id: selectedFace?.id,
        bot_template_id: template_id,
        type: COST_TYPE.CUSTOM_CHARACTER,
        // project_config: {
        //   sd_model_name: "edge-of-realism",
        //   sd_image_ratio: "2:3",
        //   batch_number: 4,
        //   lora_model_name: "Real Girl",
        //   face_model_id: selectedFace?.id ?? null,
        //   voice_name: "",
        //   voice_speed: 1,
        //   voice_volume: 11,
        //   background_music_url: "",
        //   bot_template_id: template_id,
        // },
      }).then((pic_id: string) => {
        getPicByID(pic_id);
      });
    }
  }, [template_id]);

  const handleCreateClick = async () => {
    if ((userEntity?.entitlement?.credit_point ?? 0) < 10) {
      setOpen(true);
      return;
    }
    if (!soulmate_name || !selectedFace?.id) {
      showToast("The face and name fields are required", "error");
      return;
    }

    if (selectedOtherTags.join("").length < 20) {
      showToast(
        "The content word count needs to be within the range of 20 to 3000.",
        "error"
      );
      return;
    }
    firebaseLogEvent(EVENT_ACTION_SOULMATES_CREATE);
    // 创建角色,获取id和message_id
    customCharacter({
      icon: selectedFace?.url,
      app_id: APP_ID_DELORIS,
      description: "",
      sys_prompt: "",
      provider: "aimlapi",
      model_name: "meta-llama/Llama-3.3-70B-Instruct-Turbo",
      group_id,
      bot_name: soulmate_name,
      is_system_template: false,
      face_model_id: selectedFace?.id,
      character_tags: {
        age: soulmate_age,
        personality: personalityAndHobbiesTags
          .filter((tag: string) => tag.includes("personality__"))
          .map((tag: string) => tag.split("__")[1]),
        hobbies: personalityAndHobbiesTags
          .filter((tag: string) => tag.includes("hobbies__"))
          .map((tag: string) => tag.split("__")[1]),
      },
      voice_id,
    });
  };
  return (
    <>
      <Button
        onClick={handleCreateClick}
        disabled={createProcessLoading}
        className="relative bg-gradient-to-r from-blue-500 to-pink-500 flex flex-row justify-center items-center sm:text-[22px] text-base bg-gray w-full  h-500 border-gray border-4 !rounded-full h-[45px] sm:h-[60px] sm:mt-[30px] mt-3"
      >
        <div className="text-white text-xl font-bold">Create</div>
        <div className="absolute right-5 top-0 text-[12px] bg-white text-black pl-2 pr-2  rounded-bl-[10px] rounded-br-[10px] normal-case">
          {10} coins
        </div>
        {createProcessLoading && (
          <CircularProgress
            size={24}
            sx={{
              color: "white",
              ml: 2,
            }}
          />
        )}
      </Button>
      {open && <SubscribeModal open={open} setOpen={setOpen} />}
    </>
  );
}
