import React from "react";
import { BotTemplate } from "@/types";
import VideoAvatar from "@/components/VideoAvatar";

const VideoComp = ({ soulmate }: { soulmate: BotTemplate }) => {
  return (
    <VideoAvatar
      src={soulmate.video_url}
      poster={soulmate.icon}
      className="xl:h-[363px] lg:h-[325px] md:h-[580px] sm:h-[450px] h-[252px] w-full object-cover bg-gray-light transition-transform duration-300 group-hover:scale-110"
    />
  );
};

export default VideoComp;
