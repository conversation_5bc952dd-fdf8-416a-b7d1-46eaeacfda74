import React from "react";
import Card<PERSON><PERSON>ider from "../CardProvider";
import Image from "next/image";
import VideoComp from "./_comp/VideoComp";
import { BotTemplate } from "@/types";

const CardBox = ({ soulmate, index }: { soulmate: BotTemplate; index?: number }) => {
  return (
    <CardProvider key={soulmate.id} soulmate={soulmate}>
      <>
        {/* image container with overlay */}
        <div className="relative flex items-center justify-center w-full overflow-hidden">
          {soulmate.video_url ? (
            <VideoComp soulmate={soulmate} />
          ) : (
            <Image
              width={1024}
              height={1536}
              loading="eager"
              src={soulmate.icon}
              alt={soulmate.title}
              className={`xl:h-[363px] lg:h-[325px] md:h-[580px] sm:h-[450px] h-[252px] w-full object-cover bg-gray-light transition-transform duration-300 group-hover:scale-110`}
            />
          )}
          {/* Overlay with CHAT text */}
          <div className="absolute z-10 inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <span className="flex items-center gap-2 px-5 py-2.5 text-sm font-medium text-white border border-white/30 rounded-lg backdrop-blur-sm bg-white/10 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-1">
              <span>CHAT</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" />
              </svg>
            </span>
          </div>
        </div>
        {/* identity */}
        {soulmate.character_identity && (
          <div className="absolute top-1 left-1">
            <div className="h-6 whitespace-nowrap rounded-xl rounded-br-md px-1.5 text-xs leading-6 backdrop-blur-[10px] bg-[#0006] text-white ">
              {soulmate.character_identity}
            </div>
          </div>
        )}

        <div className="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black to-transparent">
          <div className="flex h-full w-full flex-col p-3 sm:gap-3">
            <div className="mb-0.5 flex items-center gap-1  xl:mb-1 justify-between">
              <div className="flex-1 text-xs sm:text-base font-semibold xl:text-xl text-left text-white">
                {soulmate.title}
              </div>
            </div>
            <div className="text-xs text-white/95 line-clamp-2 text-left">
              {soulmate.description}
            </div>
            <div className="hidden flex-wrap gap-1  group-hover:sm:flex transition-opacity duration-200">
              {soulmate?.character_tags?.hobbies
                ?.concat(soulmate?.character_tags?.personality)
                ?.map((tag: string) => (
                  <div
                    key={tag}
                    className="h-6 whitespace-nowrap rounded-xl px-1.5 text-xs leading-6 bg-[#343232d9] text-white"
                  >
                    {tag}
                  </div>
                ))}
            </div>
          </div>
        </div>
      </>
    </CardProvider>
  );
};

export default CardBox;
