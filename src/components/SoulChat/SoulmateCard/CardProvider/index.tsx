"use client";
import { apiService, updateToken } from "@/service/Api";
import { useUserStores } from "@/stores/UserStores";
import { firebaseLogEvent } from "@/utils/utils";
import {
  EVENT_ACTION_SOULMATES_CREATE_CHAT,
  EVENT_PAGE_SOUL_CHAT,
} from "@/utils/event";
import { useEffect, useState } from "react";
import { BotTemplate } from "@/types/soulchat";
import { generateUUID } from "@/utils/generate-uuid";
import { ROUTES, SOURCE_ID_KEY, USER_UUID } from "@/constant";
import { useRouter } from "@/i18n/routing";
import clsx from "clsx";
import NSFWConfirmDialog from "@/components/NSFWConfirmDialog";
import { useNSFWSwitch } from "@/hooks/useNSFWSwitch";
import { useSoulChatStore } from "@/stores/SoulChatStore";
import { useLoadingToast } from "@/hooks/useLoadingToast";
import { debounce } from "lodash";

interface CardProviderProps {
  children: React.ReactNode;
  soulmate: BotTemplate;
}

export default function CardProvider({
  children,
  soulmate,
}: CardProviderProps) {
  const router = useRouter();
  const { userEntity, setGlobalNSFWSwitch, globalNSFWStatus } = useUserStores();
  const [isShowBlur, setIsShowBlur] = useState<boolean>(!!soulmate.is_nsfw);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { openNSFWDialog, setOpenNSFWDialog, handleConfirmNSFW } =
    useNSFWSwitch();
  const { changeHistoryChatListHasMore, setHistoryChatList } =
    useSoulChatStore();

  const { showLoading, dismissLoading } = useLoadingToast();

  useEffect(() => {
    const globalNSFWSwitch = localStorage.getItem("global_nsfw_switch");
    setGlobalNSFWSwitch(globalNSFWSwitch === "true");
    setIsShowBlur(!!(soulmate.is_nsfw && globalNSFWSwitch === "false"));
    firebaseLogEvent(EVENT_PAGE_SOUL_CHAT);
  }, []);
  useEffect(() => {
    setIsShowBlur(!!(soulmate.is_nsfw && !globalNSFWStatus));
  }, [globalNSFWStatus]);

  const handleClick = async () => {
    if (isLoading) return;

    setIsLoading(true);

    const loadingId = showLoading();
    if (!userEntity) {
      // 匿名登录
      const uuid = generateUUID();
      localStorage.setItem(USER_UUID, uuid);
      const info = await apiService.GetSkipLoginUserInfo({
        user_id: uuid,
      });
      updateToken(info.data.token || "");
    }
    apiService.createChat({ bot_template: soulmate.id }).then((res) => {
      if (res.code === 200 && res.data.id) {
        const sourceId = localStorage.getItem(SOURCE_ID_KEY);
        firebaseLogEvent(EVENT_ACTION_SOULMATES_CREATE_CHAT, {
          page_path: EVENT_ACTION_SOULMATES_CREATE_CHAT,
          source_id: sourceId,
          character_id: soulmate.id,
          character_name: soulmate.title,
        });
        changeHistoryChatListHasMore(true);
        setHistoryChatList([]);
        router.push(`${ROUTES.AI_CHAT}?id=${res.data.id}`);
        dismissLoading(loadingId);
        setIsLoading(false);
      }
    });
    // router.push(`${ROUTES.CHAT_DETAIL}/${soulmate.id}`);
  };

  return (
    <div
      className="relative w-full rounded-2xl overflow-hidden bg-white cursor-pointer group min-h-[250px]"
      onClick={debounce(handleClick, 300)}
    >
      <div className={clsx(isShowBlur && "blur")}>{children}</div>
      {/* NSFW Warning Overlay */}
      {isShowBlur && (
        <div
          className="absolute z-20 inset-0 bg-black/60 flex flex-col items-center justify-center gap-2"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="px-3 py-1 rounded-xl bg-black/80 text-white text-sm">
            This Character is rated R
          </div>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setOpenNSFWDialog(true);
            }}
            className="px-6 py-1 rounded-full bg-primary-color/80 hover:bg-white/30 text-white text-sm transition-colors"
          >
            Show
          </button>
        </div>
      )}
      {openNSFWDialog && (
        <NSFWConfirmDialog
          open={openNSFWDialog}
          onClose={(e?: React.MouseEvent) => {
            e?.stopPropagation();
            setOpenNSFWDialog(false);
          }}
          onConfirm={(e?: React.MouseEvent) => {
            e?.stopPropagation();
            handleConfirmNSFW(true);
          }}
        />
      )}
    </div>
  );
}
