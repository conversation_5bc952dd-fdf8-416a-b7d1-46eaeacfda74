import React from "react";
import {
  ArrowForwardIos as ArrowForwardIosIcon,
  ArrowBackIosNew as ArrowBackIosNewIcon,
} from "@mui/icons-material";
import { Link } from "@/i18n/routing";
import { APP_ID_DELORIS, SOULMATE_PAGE_SIZE } from "@/constant";
import Card<PERSON>ox from "./CardBox";
import { apiService } from "@/service/Api";

async function fetchSoulmates({
  tag = "",
  page = 1,
  page_size = SOULMATE_PAGE_SIZE,
  locale = "en",
}: {
  tag?: string;
  page?: number;
  page_size?: number;
  locale: string;
}) {
  const soulmateList = (
    await apiService.GetSoulmatesList({
      app_id: APP_ID_DELORIS,
      is_system_template: true,
      template_tag_filter: tag,
      page,
      page_size,
      locale,
    })
  )?.data;
  return {
    soulmateList: soulmateList?.bot_templates || [],
    total_count: soulmateList?.total_count || 0,
  };
}
const SoulmateCard = async ({
  tag,
  page,
  prefix,
  locale,
  showPagination = true,
  pageSize = SOULMATE_PAGE_SIZE,
}: {
  tag?: string;
  page: number;
  prefix?: string;
  locale: string;
  showPagination?: boolean;
  pageSize?: number;
}) => {
  const req = { page, locale, page_size: pageSize } as {
    page: number;
    tag?: string;
    locale: string;
    page_size: number;
  };
  if (tag) req.tag = tag;
  const { soulmateList, total_count } = await fetchSoulmates(req);
  return (
    <>
      <div className="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4 py-3">
        {soulmateList.map((soulmate, index) => (
          <CardBox key={soulmate.id} soulmate={soulmate} index={index} />
        ))}
      </div>
      {showPagination && (
        <div className="flex justify-between items-center py-8 flex-wrap">
          <div className="flex gap-2 bg-gray-light p-2 rounded-lg flex-wrap mt-2">
            {Array.from(
              { length: Math.ceil(total_count / SOULMATE_PAGE_SIZE) },
              (_, index) => (
                <Link
                  key={index}
                  href={`${prefix || "/"}?page=${index + 1}`}
                  className={`w-10 h-10 rounded-lg cursor-pointer flex justify-center items-center ${
                    page === index + 1 ? "bg-primary-color" : "bg-gray-dark"
                  }`}
                >
                  {index + 1}
                </Link>
              )
            )}
          </div>
          <div className="flex gap-2 justify-center items-center bg-gray-light rounded-lg p-2 mt-2">
            <Link
              href={
                page > 1
                  ? `${prefix || "/"}?page=${page - 1}`
                  : `${prefix || "/"}?page=1`
              }
              className="w-10 h-10 bg-gray-dark rounded-lg cursor-pointer flex justify-center items-center"
            >
              <ArrowBackIosNewIcon />
            </Link>
            <Link
              href={
                page >= Math.ceil(total_count / SOULMATE_PAGE_SIZE)
                  ? `${prefix || "/"}?page=1`
                  : `${prefix || "/"}?page=${page + 1}`
              }
              className="w-10 h-10 bg-gray-dark rounded-lg cursor-pointer flex justify-center items-center"
            >
              <ArrowForwardIosIcon />
            </Link>
          </div>
        </div>
      )}
    </>
  );
};

export default SoulmateCard;
