"use client";

import { Box, Tooltip } from "@mui/material";
import { Cached as CachedIcon } from "@mui/icons-material";
import React from "react";
import { useSoulChatStore } from "@/stores/SoulChatStore";

const QuickOptCol = ({
  chat_id,
  onRegenerate,
  canSendMessage,
  setOpenCoinsModal,
  isCanRegenerate,
}: {
  chat_id?: string;
  onRegenerate: () => void;
  canSendMessage: boolean;
  setOpenCoinsModal: (open: boolean) => void;
  isCanRegenerate: boolean;
}) => {
  const { suggestReplyInfo, changeSuggestReplyInfo, querySuggestReply } =
    useSoulChatStore();
  return (
    <Box
      sx={{
        display: "flex",
        gap: 2,
        justifyContent: "start",
        px: 1,
      }}
    >
      <Tooltip title="costs 1 credit" arrow placement="bottom">
        <Box
          sx={{
            backgroundColor: "rgba(255, 255, 255, 0.1)",
            borderRadius: "20px",
            border: "1px solid var(--primary-color)",
            color: "white",
            cursor: "pointer",
            display: "flex",
            alignItems: "center",
            gap: 0.5,
            p: 0.5,
            px: 1,
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.2)",
            },
            fontSize: "14px",
          }}
          onClick={async () => {
            const loading = !suggestReplyInfo.loading;
            changeSuggestReplyInfo({
              loading,
            });
            if (loading) {
              const res = await querySuggestReply(chat_id || "");
              if (!res) {
                changeSuggestReplyInfo({
                  loading: false,
                });
                setOpenCoinsModal(true);
              }
            }
          }}
        >
          <span>💡</span>
          <span>Suggest Reply</span>
        </Box>
      </Tooltip>

      {isCanRegenerate && (
        <Tooltip
          title={!canSendMessage ? "Generating reply, please wait..." : ""}
          arrow
          placement="right"
        >
          <Box
            component="span"
            sx={{
              color: "#fff",
              opacity: canSendMessage ? 0.9 : 0.5,
              fontSize: "14px",
              display: "flex",
              alignItems: "center",
              gap: "4px",
              cursor: canSendMessage ? "pointer" : "not-allowed",
              backgroundColor: !canSendMessage
                ? "rgba(255, 255, 255, 0.05)"
                : suggestReplyInfo.loading
                ? "rgba(255, 255, 255, 0.1)"
                : "rgba(255, 255, 255, 0.2)",
              p: 0.5,
              px: 1,
              borderRadius: "20px",
              "&:hover": {
                backgroundColor: canSendMessage
                  ? "rgba(255, 255, 255, 0.3)"
                  : "rgba(255, 255, 255, 0.05)",
              },
            }}
            onClick={canSendMessage ? onRegenerate : undefined}
          >
            <CachedIcon sx={{ fontSize: 16 }} />
            <span>Regenerate</span>
          </Box>
        </Tooltip>
      )}
    </Box>
  );
};

export default QuickOptCol;
