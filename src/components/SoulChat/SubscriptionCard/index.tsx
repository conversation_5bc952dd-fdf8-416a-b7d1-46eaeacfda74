import { useMemo } from "react";
import { CircleDollarSign, Sparkles } from "lucide-react";

import { ROUTES } from "@/constant";
import { useRouter } from "@/i18n/routing";

const SubscriptionCard = ({ content }: { content?: string }) => {
  const router = useRouter();
  const contentInfo = useMemo(() => {
    try {
      return JSON.parse(content || "{}");
    } catch (error) {
      return {};
    }
  }, [content]);

  const isCoinRelated = contentInfo?.title?.includes("coin");

  return (
    <div className="max-w-md mx-auto p-6 ">
      {/* Icon */}
      <div className="flex justify-center mb-4">
        <div className="w-16 h-16 bg-gradient-to-br from-primary-color to-secondary-color rounded-full flex items-center justify-center shadow-lg">
          {isCoinRelated ? (
            <CircleDollarSign className="w-8 h-8 text-white" />
          ) : (
            <Sparkles className="w-8 h-8 text-white" />
          )}
        </div>
      </div>

      {/* Title */}
      <h3 className="text-xl sm:text-2xl font-bold text-white text-center mb-3">
        {contentInfo?.title || "Upgrade Required"}
      </h3>

      {/* Content */}
      <p className="text-gray-300 text-center text-sm sm:text-base mb-6 leading-relaxed">
        {contentInfo?.content ||
          "Get more coins to watch videos with Girlfriend"}
      </p>

      {/* Button */}
      <button
        className="w-full bg-gradient-to-r from-primary-color to-secondary-color hover:from-primary-color/90 hover:to-secondary-color/90 text-white font-semibold py-1 sm:py-3 smm:px-6 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95"
        onClick={() => {
          if (isCoinRelated) {
            router.push(ROUTES.COINS);
          } else {
            router.push(ROUTES.SUBSCRIBE);
          }
        }}
      >
        <span className="text-sm sm:text-base">
          {contentInfo?.actions?.[0] || "SUBSCRIBE NOW"}
        </span>
      </button>
    </div>
  );
};

export default SubscriptionCard;
