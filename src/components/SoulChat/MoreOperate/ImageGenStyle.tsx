import React from "react";
import { ImageGenStyleItem } from "@/types";
import clsx from "clsx";
import Image from "next/image";

interface ImageGenStyleProps {
  introEle?: React.ReactNode;
  current_style_index: number;
  image_gen_styles: ImageGenStyleItem[];
  onSelectStyle: (style: ImageGenStyleItem) => void;
}

const ImageGenStyle = ({
  introEle,
  current_style_index,
  image_gen_styles,
  onSelectStyle,
}: ImageGenStyleProps) => {
  return (
    <div>
      {introEle}
      <div className="flex gap-2 flex-wrap">
        {image_gen_styles.map((style) => (
          <div key={style.index}>
            <Image
              src={style.example_image_url}
              alt={style.name}
              width={128}
              height={128}
              className={clsx(
                "rounded-lg p-1 w-16 h-16",
                current_style_index === style.index &&
                  "border-2 border-primary-color"
              )}
              onClick={() => {
                onSelectStyle(style);
              }}
            />
            <span className="text-sm text-muted-foreground">{style.name}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ImageGenStyle;
