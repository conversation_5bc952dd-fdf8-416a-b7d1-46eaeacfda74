import { useTranslations } from "next-intl";
import React from "react";
import { Select, MenuItem } from "@mui/material";

import { apiService } from "@/service/Api";
import { useUserStores } from "@/stores/UserStores";
import { ChatModel } from "@/types";

const ModelSwitcher = ({
  chat_id,
  icon,
  selectedModel,
  setSelectedModel,
  modalInfo,
  onClose,
  handleOpenMemberModal,
}: {
  chat_id: string | null;
  icon: string;
  selectedModel: number;
  setSelectedModel: (model: number) => void;
  modalInfo: ChatModel;
  onClose?: () => void;
  handleOpenMemberModal: () => void;
}) => {
  const t2 = useTranslations("OtherTranslations");

  const { userEntity } = useUserStores();
  return (
    <div className="flex flex-col gap-2">
      <div className="text-sm text-[#D4D4D8]">{t2("chooseYourModel")}</div>
      <Select
        value={selectedModel}
        size="small"
        renderValue={(value) => {
          const model = modalInfo.chat_model_list.find(
            (m) => m.index === value
          );
          return model?.aliases || "";
        }}
        sx={{
          height: "32px",
          width: "280px",
          color: "white",
          backgroundColor: "rgba(255, 255, 255, 0.05)",
          borderRadius: "8px",
          "& .MuiSelect-select": {
            padding: "4px 10px",
          },
          ".MuiOutlinedInput-notchedOutline": {
            borderColor: "rgba(255, 255, 255, 0.1)",
            borderRadius: "8px",
          },
          "&:hover .MuiOutlinedInput-notchedOutline": {
            borderColor: "rgba(var(--primary-color-pure), 0.5)",
          },
          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderColor: "var(--primary-color)",
            borderWidth: "1px",
          },
          ".MuiSvgIcon-root": {
            color: "rgba(255, 255, 255, 0.7)",
            right: "8px",
          },
        }}
        MenuProps={{
          PaperProps: {
            sx: {
              bgcolor: "#23272d",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "8px",
              marginTop: "4px",
              "& .MuiMenuItem-root": {
                color: "white",
                padding: "2px 10px",
                "&:hover": {
                  bgcolor: "rgba(var(--primary-color-pure), 0.1)",
                },
                "&.Mui-selected": {
                  bgcolor: "rgba(var(--primary-color-pure), 0.2)",
                  "&:hover": {
                    bgcolor: "rgba(var(--primary-color-pure), 0.3)",
                  },
                },
              },
            },
          },
        }}
      >
        {modalInfo.chat_model_list.map((model) => (
          <MenuItem
            sx={{
              maxWidth: "280px",
              whiteSpace: "normal",
              lineHeight: "1.2",
            }}
            key={model.index}
            value={model.index}
            onClick={() => {
              const memberLevel =
                userEntity?.entitlement?.member_level?.[0] || 0;
              if (model.support_level.includes(memberLevel)) {
                setSelectedModel(model.index);
                apiService.changeChatModel({
                  chat_id: chat_id || "",
                  model_index: model.index,
                });
                onClose?.();
              } else {
                handleOpenMemberModal();
                onClose?.();
              }
            }}
          >
            <div className="flex flex-col p-1">
              <div className="text-[#FAFAF9] text-sm">{model.aliases}</div>
              <div className="mt-1 text-xs text-[#fff9]">{model.desc}</div>
            </div>
          </MenuItem>
        ))}
      </Select>
    </div>
  );
};

export default ModelSwitcher;
