import { FC, useEffect, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { Menu, MenuItem, IconButton, Tooltip, Divider } from "@mui/material";
import {
  Error as ErrorIcon,
  DeleteOutline as DeleteOutlineIcon,
  MoreHoriz as MoreHorizIcon,
} from "@mui/icons-material";

import { apiService } from "@/service/Api";
import { ChatModel, ImageGenStyleItem, ImageStyle } from "@/types";
import useModelSwitcher from "@/hooks/guideContent/useModelSwitcher";
import ModelSwitcher from "./ModelSwitcher";
import { showToast } from "@/utils/toast";
import ImageGenStyle from "./ImageGenStyle";
import { UpgradeCoinsDialog } from "@/components/subscribe/UpgradeCoinsDialog";
import { ROUTES } from "@/constant";
import { useRouter } from "@/i18n/routing";

interface ChatMenuProps {
  onClearChats?: () => void;
  chat_id?: string;
  icon?: string;
}

const MoreOperate: FC<ChatMenuProps> = ({ onClearChats, chat_id, icon }) => {
  const t = useTranslations("OtherTranslations");
  const t2 = useTranslations("SubscribePage");
  const locale = useLocale();
  const router = useRouter();
  const [openMemberModal, setOpenMemberModal] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedModel, setSelectedModel] = useState(0);
  const [modalInfo, setModalInfo] = useState<ChatModel>({
    chat_model_list: [],
    current_model_index: 0,
  });
  const [image_gen_stylesInfo, setImageGenStylesInfo] = useState<ImageStyle>({
    current_style_index: 0,
    image_gen_styles: [],
  });

  useModelSwitcher(".model-switcher");
  const open = Boolean(anchorEl);

  useEffect(() => {
    if (chat_id) {
      apiService.getChatModels(chat_id).then((res) => {
        if (res.code === 200 && res.data) {
          setModalInfo(res.data);
          setSelectedModel(res.data.current_model_index);
        }
      });
      apiService.getImageStyles(chat_id).then((res) => {
        if (res.code === 200 && res.data) {
          setImageGenStylesInfo(res.data);
        }
      });
    }
  }, [chat_id]);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleClearChats = () => {
    onClearChats?.();
    handleClose();
  };

  return (
    <>
      <IconButton sx={{ color: "white" }} onClick={handleClick}>
        <MoreHorizIcon className="model-switcher" />
      </IconButton>

      <Menu
        id="chat-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        transformOrigin={{ horizontal: "right", vertical: "top" }}
        anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
        PaperProps={{
          sx: {
            bgcolor: "var(--dark-bg)",
            color: "white",
            borderRadius: 4,
            py: 1,
            "& .MuiDivider-root": {
              borderColor: "rgba(255, 255, 255, 0.08)",
              margin: "4px 12px",
              height: "1px",
            },
          },
        }}
      >
        <MenuItem
          onClick={handleClearChats}
          sx={{
            gap: 1,
            fontSize: "14px",
            "&:hover": {
              bgcolor: "rgba(255, 255, 255, 0.1)",
            },
          }}
        >
          <DeleteOutlineIcon />
          {t("clearChats")}
        </MenuItem>

        <Divider />

        <MenuItem disableRipple onClick={(e) => e.preventDefault()}>
          <ModelSwitcher
            chat_id={chat_id || ""}
            icon={icon || ""}
            selectedModel={selectedModel}
            setSelectedModel={setSelectedModel}
            modalInfo={modalInfo}
            onClose={handleClose}
            handleOpenMemberModal={() => setOpenMemberModal(true)}
          />
        </MenuItem>

        <Divider />

        <MenuItem disableRipple onClick={(e) => e.preventDefault()}>
          <ImageGenStyle
            introEle={
              <div className="text-sm text-[#D4D4D8] flex gap-1 items-center mb-1">
                <div>{t("inChatImageGenStyle")}</div>
                <Tooltip title={t("setDefaultStyleForImageGenerationInChat")}>
                  <ErrorIcon sx={{ fontSize: 14 }} />
                </Tooltip>
              </div>
            }
            current_style_index={image_gen_stylesInfo.current_style_index}
            image_gen_styles={image_gen_stylesInfo.image_gen_styles}
            onSelectStyle={async (style: ImageGenStyleItem) => {
              const resp = await apiService.changeInChatImageGenStyle({
                chat_id: chat_id || "",
                style_index: style.index,
              });
              if (resp.code === 200) {
                setImageGenStylesInfo((prev) => ({
                  ...prev,
                  current_style_index: style.index,
                }));
              } else {
                showToast(resp.message, "error");
              }
            }}
          />
        </MenuItem>
      </Menu>
      {openMemberModal && (
        <UpgradeCoinsDialog
          open={openMemberModal}
          onClose={() => setOpenMemberModal(false)}
          onUpgrade={() => {
            setOpenMemberModal(false);
            router.push(ROUTES.SUBSCRIBE, { locale });
          }}
          imgSrc={icon || ""}
          desc1={t2("UpgradeToDeluxeMembership")}
          desc2={t2("toGenerateImagesInChat")}
          desc3={t2("hurryUpUpgradeNowToGenerateImagesInChat")}
        />
      )}
    </>
  );
};

export default MoreOperate;
