"use client";

import { forwardRef, useEffect } from "react";
import { useCreateStores } from "@/stores/CreateStores";
import { LoadingDialog } from "@/components/LoadingDialog";

const FaceUpload = forwardRef<HTMLInputElement>((_, ref) => {
  const {
    uploadFace,
    showHint,
    uploadingFaceModel,
    queryAnalysisResult,
    showUploadingDialog,
    uploadingDialogTitle,
    cancelFaceModel,
  } = useCreateStores();

  useEffect(() => {
    if (uploadingFaceModel) {
      queryAnalysisResult();
    }
  }, [uploadingFaceModel]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      if (!["image/jpeg", "image/png"].includes(files[0].type)) {
        showHint("Only jpg/png format images are allowed for upload.");
        return;
      }
      if (files[0].size > 2 * 1024 * 1024) {
        showHint("The file size cannot exceed 2MB, please choose again.");
        return;
      }
      uploadFace(files[0]);
    }
  };

  return (
    <>
      <input
        type="file"
        onChange={handleChange}
        id="face-upload"
        aria-label="Upload face image"
        ref={ref}
        className="hidden"
      />
      <LoadingDialog
        show={showUploadingDialog}
        title={uploadingDialogTitle}
        msg={""}
        onCancel={() => {
          cancelFaceModel();
        }}
      ></LoadingDialog>
    </>
  );
});

FaceUpload.displayName = "FaceUpload";

export default FaceUpload;
