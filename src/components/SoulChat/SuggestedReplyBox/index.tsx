"use client";

import { useEffect } from "react";
import { Box, CircularProgress, IconButton, Tooltip } from "@mui/material";
import {
  RefreshRounded as RefreshIcon,
  Edit as EditIcon,
  VoiceChat as VoiceChatIcon,
} from "@mui/icons-material";

import { MarkdownRenderer } from "@/utils/utils";
import { useSoulChatStore } from "@/stores/SoulChatStore";

interface SuggestedReplyProps {
  content: string;
  isRequesting: boolean;
  canSendMessage?: boolean;
  onReset?: () => void;
  onEdit?: () => void;
  onContentClick?: () => void;
}

const SuggestedReplyBox = ({
  content,
  isRequesting,
  canSendMessage,
  onReset,
  onEdit,
  onContentClick,
}: SuggestedReplyProps) => {
  const { changeSuggestReplyInfo } = useSoulChatStore();

  useEffect(() => {
    return () => {
      changeSuggestReplyInfo({
        content: "",
        loading: false,
        isRequesting: false,
      });
    };
  }, []);

  return (
    <Box
      sx={{
        backgroundColor: "#1E1F28",
        borderRadius: "16px",
        p: 1,
        px: 1,
        mx: 1,
        mb: 1,
        boxShadow: "0 2px 6px rgba(0,0,0,0.2)",
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 1,
        }}
      >
        <div className="text-sm font-medium">
          Here&apos;s a Suggested Reply for You
        </div>
        <div className="flex items-center">
          <IconButton
            onClick={onReset}
            sx={{
              color: "#fff",
              padding: 1,
              borderRadius: "28px",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
              "&.Mui-disabled": {
                color: "rgba(255, 255, 255, 0.5)",
              },
            }}
            disabled={isRequesting}
          >
            <div className="flex items-center gap-1">
              {isRequesting ? (
                <>
                  <CircularProgress size={16} sx={{ color: "#fff" }} />
                  <div className="text-sm">Suggesting</div>
                </>
              ) : (
                <>
                  <RefreshIcon sx={{ fontSize: 16 }} />
                  <div className="text-sm">Reset</div>
                </>
              )}
            </div>
          </IconButton>
        </div>
      </Box>

      {/* Content */}
      <Box
        sx={{
          display: "flex",
          gap: 1,
          borderRadius: "8px",
          alignItems: "center",
        }}
      >
        <Tooltip
          title={!canSendMessage ? "Generating reply, please wait..." : ""}
          arrow
          placement="top"
        >
          <Box
            component="span"
            sx={{
              color: "#fff",
              opacity: canSendMessage ? 0.9 : 0.5,
              fontSize: "14px",
              flex: 1,
              lineHeight: 1.6,
              cursor: canSendMessage ? "pointer" : "not-allowed",
              backgroundColor: "#2A2B36",
              p: 0.5,
              px: 1,
              display: "flex",
              alignItems: "center",
              borderRadius: "8px",
              "&:hover": {
                backgroundColor: canSendMessage
                  ? "rgba(255, 255, 255, 0.1)"
                  : "#2A2B36",
              },
            }}
            onClick={canSendMessage ? onContentClick : undefined}
          >
            <VoiceChatIcon sx={{ fontSize: 16, mr: 1 }} />
            <div className="max-h-16 overflow-y-auto">
              <MarkdownRenderer content={content} />
            </div>
          </Box>
        </Tooltip>
        <IconButton
          onClick={onEdit}
          sx={{
            color: "#fff",
            backgroundColor: "#2A2B36",
            p: 0.5,
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
          }}
        >
          <EditIcon sx={{ fontSize: 18 }} />
        </IconButton>
      </Box>
    </Box>
  );
};

export default SuggestedReplyBox;
