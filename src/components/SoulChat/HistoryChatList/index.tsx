"use client";

import React, { useEffect } from "react";
import { useRouter } from "@/i18n/routing";
import {
  Close as CloseIcon,
  ArrowForwardIos as ArrowForwardIosIcon,
} from "@mui/icons-material";
import toast from "react-hot-toast";
import { useSoulChatStore } from "@/stores/SoulChatStore";
import { useUserStores } from "@/stores/UserStores";
import { EVENT_ACTION_DELETE_SOULMATE } from "@/utils/event";
import { firebaseLogEvent } from "@/utils/utils";
import { ROUTES } from "@/constant";
import Image from "next/image";
import { Tooltip } from "@mui/material";
import { apiService } from "@/service/Api";
const HistoryChatList = () => {
  const router = useRouter();
  const {
    getHistoryChatList,
    historyChatList,
    deleteCharacter,
    historyChatListHasMore,
    setHistoryChatList,
    changeHistoryChatListHasMore,
  } = useSoulChatStore();
  const { userEntity } = useUserStores();

  useEffect(() => {
    if (userEntity) {
      getHistoryChatList();
    }
  }, [userEntity]);

  return (
    <div className="flex-1 overflow-x-auto">
      <div className="flex gap-6 pb-4 overflow-x-auto scrollbar-hide items-center">
        {historyChatList.map((item) => (
          <div
            key={item.id}
            className="relative group flex flex-col items-center cursor-pointer w-[80px] pt-2"
            onClick={() => {
              apiService
                .createChat({ bot_template: item.bot_template_id })
                .then((res) => {
                  if (res.code === 200 && res.data.id) {
                    changeHistoryChatListHasMore(true);
                    setHistoryChatList([]);
                    router.push(`${ROUTES.AI_CHAT}?id=${item.id}`);
                  }
                });
            }}
          >
            <div className="relative w-16 h-16 mb-2">
              <div className="absolute -inset-2 rounded-full border-2 border-transparent sm:group-hover:border-primary-color transition-colors duration-200"></div>
              <div className="w-16 h-16 rounded-full overflow-hidden">
                <Image
                  width={80}
                  height={80}
                  src={item.icon}
                  alt={item.bot_name}
                  className="w-full h-full object-cover"
                  loading="eager"
                />
              </div>
              <div className="absolute -top-3 right-1 sm:hidden sm:group-hover:block cursor-pointer bg-primary-color text-white rounded-full">
                <CloseIcon
                  sx={{
                    fontSize: 16,
                  }}
                  onClick={async (e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    firebaseLogEvent(EVENT_ACTION_DELETE_SOULMATE);
                    const resp = await deleteCharacter(item.id);
                    if (resp) {
                      toast.success("Delete success");
                      setHistoryChatList(
                        historyChatList.filter((chat) => chat.id !== item.id)
                      );
                    } else {
                      toast.error("Delete failed");
                    }
                  }}
                />
              </div>
            </div>
            <span className="text-xs text-white text-center truncate w-full">
              {item.bot_name}
            </span>
          </div>
        ))}
        {/* 右箭头按钮 */}
        {historyChatList.length !== 0 && historyChatListHasMore && (
          <Tooltip title="Load more">
            <div
              className="cursor-pointer rounded-full flex items-center justify-center"
              onClick={async () => {
                getHistoryChatList();
              }}
            >
              <ArrowForwardIosIcon
                className="text-white"
                sx={{ fontSize: 16 }}
              />
            </div>
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default HistoryChatList;
