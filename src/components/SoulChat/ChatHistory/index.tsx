"use client";

import React from "react";
import { Box, Typography, Avatar } from "@mui/material";

import { useSoulChatStore } from "../../../stores/SoulChatStore";
import ChatIcon from "./svg/chat";
import { Chat } from "@/types";
import { useSearchParams } from "next/navigation";
import { MESSAGE_TYPE, MESSAGE_TYPE_API, ROUTES } from "@/constant";
import { useTranslations } from "next-intl";
import { useRouter } from "@/i18n/routing";
export default function ChatHistory({
  className,
  onClose,
}: {
  className?: string;
  onClose?: () => void;
}) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations("AiChatPage");
  const chat_id = searchParams.get("id");
  const [isLoading, setIsLoading] = React.useState(false);
  const {
    historyChatList,
    cleanUpOpt,
    currentChat,
    chatDetail,
    getHistoryChatList,
  } = useSoulChatStore();

  const handleScroll = async (e: React.UIEvent<HTMLDivElement>) => {
    const bottom =
      e.currentTarget.scrollHeight - e.currentTarget.scrollTop ===
      e.currentTarget.clientHeight;
    if (bottom && !isLoading) {
      setIsLoading(true);
      try {
        await getHistoryChatList();
      } finally {
        setIsLoading(false);
      }
    }
  };

  const judgeIsOtherType = ({
    type,
    content,
  }: {
    type: string;
    content: string;
  }) => {
    switch (type) {
      case MESSAGE_TYPE.image:
        return "[Photo]";
      case MESSAGE_TYPE.mind_reading:
        return "[Mind Reading]";
      case MESSAGE_TYPE_API.shortcuts_video:
      case MESSAGE_TYPE.video:
        return "[Video]";
      case MESSAGE_TYPE.video_error:
        return "[Video Error]";
      default:
        return content;
    }
  };
  // 获取最新消息
  const getLatestMessage = (item: Chat) => {
    if (item.id !== chat_id) {
      return judgeIsOtherType({
        type: item.last_message_type,
        content: item.latest_message,
      });
    }

    if (currentChat?.chat_id === chat_id && chatDetail.length > 0) {
      const lastMessageItem = chatDetail
        .slice()
        .reverse()
        .find((msg) => msg?.content);

      if (lastMessageItem) {
        return judgeIsOtherType({
          type: lastMessageItem.type || "",
          content: lastMessageItem.content,
        });
      }
    }
    return judgeIsOtherType({
      type: item.last_message_type,
      content: item.latest_message,
    });
  };
  return (
    <Box
      sx={{
        width: 280,
        borderRight: "1px solid rgba(255,255,255,0.1)",
        flexDirection: "column",
        maxHeight: {
          sm: "calc(100vh)",
        },
      }}
      className={className}
    >
      <Typography
        sx={{
          p: 4,
          display: "flex",
          alignItems: "center",
          gap: 1,
        }}
      >
        <ChatIcon />
        <span className="font-bold">{t("chatHistory")}</span>
      </Typography>

      <Box sx={{ flex: 1, overflowY: "auto", px: 2 }} onScroll={handleScroll}>
        {historyChatList.map((item) => (
          <Box
            key={item.id}
            sx={{
              py: 1,
              px: 2,
              display: "flex",
              alignItems: "center",
              textAlign: "left",
              borderRadius: 4,
              gap: 2,
              "&:hover": {
                bgcolor: "rgba(255,255,255,0.1)",
                cursor: "pointer",
              },
              backgroundColor:
                chat_id === item.id ? "rgba(255,255,255,0.1)" : "transparent",
            }}
            onClick={() => {
              onClose?.();
              cleanUpOpt();
              router.push(`${ROUTES.AI_CHAT}?id=${item.id}`);
            }}
          >
            <Avatar src={item.icon} />
            <Box>
              <div className="line-clamp-1">{item.bot_name}</div>
              <Typography
                variant="body2"
                sx={{ opacity: 0.7 }}
                className="line-clamp-1"
              >
                {getLatestMessage(item)}
              </Typography>
            </Box>
          </Box>
        ))}
        {isLoading && (
          <Box sx={{ textAlign: "center", py: 2 }}>
            <Typography variant="body2" sx={{ opacity: 0.7 }}>
              loading...
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
}
