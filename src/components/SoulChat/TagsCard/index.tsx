import React from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { Link } from "@/i18n/routing";

const TagsCard = ({ tags }: { tags: string[] }) => {
  return (
    <div className="flex  justify-between w-full overflow-x-auto overflow-y-hidden h-[calc(2*2rem+0.5rem)]">
      <div className="flex flex-wrap gap-2">
        {tags.map((tag) => (
          <Link
            key={tag}
            href={`/tags/${tag}`}
            className="h-8 bg-secondary-color px-2 py-1 whitespace-nowrap rounded-2xl text-sm text-secondary-light-color cursor-pointer hover:bg-secondary-color/80"
          >
            {tag}
          </Link>
        ))}
      </div>
      <Link
        href="/tags"
        className="w-[25px] h-[25px] flex items-center justify-center bg-secondary-light-color rounded-full text-secondary-color cursor-pointer"
      >
        <ExpandMoreIcon />
      </Link>
    </div>
  );
};

export default TagsCard;
