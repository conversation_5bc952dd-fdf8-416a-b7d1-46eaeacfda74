"use client";
import React from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";
import { useRouter } from "@/i18n/routing";
import { ROUTES } from "@/constant";
import { useTranslations } from "next-intl";

const SubscribeModal = ({ open, setOpen }: { open: boolean; setOpen: any }) => {
  const t = useTranslations("SubscribePage");
  const router = useRouter();
  return (
    <Modal open={open}>
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "#1E1F24",
          borderRadius: 3,
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.4)",
          p: {
            xs: 3,
            sm: 4,
          },
          color: "#fff",
          border: "1px solid rgba(255, 255, 255, 0.1)",
        }}
      >
        <div className="sm:text-2xl text-xl font-bold mb-4 text-white">
          {t("pleaseSubscribeToContinue")}
        </div>

        <div className="sm:text-base text-sm  mb-6 text-gray-300">
          {t("youDoNotHaveEnoughCoins")}
        </div>

        <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
          <MuiButton
            sx={{
              color: "rgba(255, 255, 255, 0.7)",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
              fontSize: {
                xs: "12px",
                sm: "14px",
              },
            }}
            onClick={() => setOpen(false)}
          >
            {t("cancel")}
          </MuiButton>
          <MuiButton
            variant="contained"
            sx={{
              backgroundColor: "var(--primary-color)",
              textTransform: "none",
              borderRadius: 1.5,
              px: 3,
            }}
            onClick={() => router.push(ROUTES.SUBSCRIBE)}
          >
            {t("subscribe")}
          </MuiButton>
        </Box>
      </Box>
    </Modal>
  );
};

export default SubscribeModal;
