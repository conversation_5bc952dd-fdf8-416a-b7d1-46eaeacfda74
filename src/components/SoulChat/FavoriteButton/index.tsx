"use client";

import React, { useEffect } from "react";
import { apiService } from "@/service/Api";
import { Heart as HeartIcon } from "lucide-react";
const FavoriteButton = ({ templateId }: { templateId: string }) => {
  const [isFavorite, setIsFavorite] = React.useState(false);

  const fetchIsFavorite = async () => {
    const res = await apiService.getIsBotTemplateFavorited(templateId);
    setIsFavorite(res.data.is_favorited || false);
  };

  useEffect(() => {
    fetchIsFavorite();
  }, []);
  return (
    <HeartIcon
      fill={isFavorite ? "var(--primary-color)" : "transparent"}
      color="var(--primary-color)"
      onClick={() => {
        apiService
          .cancelOrCollectTemplate({
            template_id: templateId,
            is_favorite: !isFavorite,
          })
          .then(() => {
            fetchIsFavorite();
          });
      }}
      style={{ cursor: "pointer" }}
    />
  );
};

export default FavoriteButton;
