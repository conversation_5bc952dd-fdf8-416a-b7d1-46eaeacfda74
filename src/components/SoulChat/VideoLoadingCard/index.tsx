"use client";

import React, { useState, useEffect } from "react";
import { Box, Typography, CircularProgress } from "@mui/material";
import { styled } from "@mui/material/styles";

interface VideoLoadingCardProps {
  /** 是否显示加载卡片 */
  show?: boolean;
  /** 自定义宽度 */
  width?: number | string;
  /** 自定义高度 */
  height?: number | string;
  /** 自定义加载文本 */
  loadingText?: string;
  /** 进度更新间隔（毫秒） */
  progressInterval?: number;
  /** 最大进度值 */
  maxProgress?: number;
  /** 完成回调 */
  onComplete?: () => void;
}

const StyledCard = styled(Box)(({ theme }) => ({
  background: "linear-gradient(135deg, #8B5A8C 0%, #A855A8 50%, #C2185B 100%)",
  borderRadius: "16px",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  overflow: "hidden",
  boxShadow: "0 8px 32px rgba(0, 0, 0, 0.3)",
  "&::before": {
    content: '""',
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background:
      "linear-gradient(135deg, rgba(202, 60, 118, 0.1) 0%, rgba(202, 60, 160, 0.2) 100%)",
    zIndex: 1,
  },
}));

const ContentWrapper = styled(Box)({
  position: "relative",
  zIndex: 2,
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  gap: "24px",
  padding: "32px",
});

const ProgressWrapper = styled(Box)({
  position: "relative",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
});

const ProgressText = styled(Typography)({
  position: "absolute",
  color: "white",
  fontWeight: "bold",
  fontSize: "18px",
  zIndex: 3,
});

const LoadingText = styled(Typography)({
  color: "white",
  fontSize: "18px",
  fontWeight: 500,
  textAlign: "center",
  letterSpacing: "0.5px",
});

const VideoLoadingCard: React.FC<VideoLoadingCardProps> = ({
  show = true,
  width = 240,
  height = 320,
  loadingText = "I need to prepare.",
  progressInterval = 1000,
  maxProgress = 99,
  onComplete,
}) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (!show) {
      setProgress(0);
      return;
    }

    const timer = setInterval(() => {
      setProgress((prevProgress) => {
        const increment = Math.random() * 2 + 0.5;
        const newProgress = Math.min(prevProgress + increment, maxProgress);

        if (newProgress >= maxProgress) {
          clearInterval(timer);
          if (onComplete) {
            onComplete();
          }
        }

        return newProgress;
      });
    }, progressInterval);

    return () => clearInterval(timer);
  }, [show, progressInterval, maxProgress, onComplete]);

  if (!show) {
    return null;
  }

  return (
    <StyledCard
      sx={{
        width: typeof width === "number" ? `${width}px` : width,
        height: typeof height === "number" ? `${height}px` : height,
      }}
    >
      <ContentWrapper>
        <ProgressWrapper>
          <CircularProgress
            variant="determinate"
            value={progress}
            size={80}
            thickness={4}
            sx={{
              color: "white",
              "& .MuiCircularProgress-circle": {
                strokeLinecap: "round",
              },
            }}
          />
          <ProgressText>{Math.round(progress)}%</ProgressText>
        </ProgressWrapper>

        <LoadingText>{loadingText}</LoadingText>
      </ContentWrapper>
    </StyledCard>
  );
};

export default VideoLoadingCard;
