"use client";

import React, { useState } from "react";
import AddButton from "../AddButton";
import AddSoulmateDialog from "../AddSoulmateDialog";
import { useUserStores } from "@/stores/UserStores";
import { useRouter } from "@/i18n/routing";
import { ROUTES } from "@/constant";

const AddSoulmatesButton = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { userEntity } = useUserStores();
  const router = useRouter();
  return (
    <>
      <AddButton
        onClick={() => {
          router.push(ROUTES.CREATE);
        }}
        isPro={true}
      />
      {isOpen && (
        <AddSoulmateDialog
          open={isOpen}
          onClose={() => {
            setIsOpen(false);
          }}
        />
      )}
    </>
  );
};

export default AddSoulmatesButton;
