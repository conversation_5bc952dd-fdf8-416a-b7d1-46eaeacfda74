import { Box, Typography } from "@mui/material";

const LoadingResponse = () => {
  return (
    <Box
      sx={{
        backgroundColor: "rgba(0, 0, 0, 0.3)",
        backdropFilter: "blur(8px)",
        borderRadius: "24px",
        padding: "24px",
        textAlign: "center",
        color: "white",
        position: "relative",
        border: "1px solid rgba(255, 255, 255, 0.1)",
        boxShadow: `
          inset 4px 0 20px rgba(var(--primary-color-pure), 0.15),
          inset -4px 0 20px rgba(var(--primary-color-pure), 0.15),
          inset 0 4px 20px rgba(var(--primary-color-pure), 0.15),
          inset 0 -4px 20px rgba(var(--primary-color-pure), 0.15)
        `,
        mb: 1,
      }}
    >
      <Typography
        sx={{
          fontWeight: 500,
          marginBottom: "12px",
        }}
      >
        We&apos;re Crafting a Response for You...
      </Typography>
      <Box
        sx={{
          width: "100px",
          height: "6px",
          backgroundColor: "rgba(255, 255, 255, 0.1)",
          borderRadius: "2px",
          margin: "0 auto",
          position: "relative",
          overflow: "hidden",
        }}
      >
        <Box
          sx={{
            position: "absolute",
            left: 0,
            top: 0,
            height: "100%",
            width: "30%",
            backgroundColor: "var(--primary-color)",
            borderRadius: "4px",
            animation: "loading 1.5s infinite",
            "@keyframes loading": {
              "0%": {
                left: "-40%",
              },
              "100%": {
                left: "100%",
              },
            },
          }}
        />
      </Box>
    </Box>
  );
};
export default LoadingResponse;
