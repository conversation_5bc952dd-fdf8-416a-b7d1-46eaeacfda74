import React from "react";
import ImageCarousel from "@/components/SoulChat/ImageCarousel";
import { Box, Typography, Chip } from "@mui/material";

const RightSideBar = ({
  showRightPanel,
  currentChat,
  pic_generate_loading,
}: {
  showRightPanel: boolean;
  currentChat: any;
  pic_generate_loading: boolean;
}) => {
  return (
    <Box
      sx={{
        width: showRightPanel ? 268 : 0,
        borderLeft: "1px solid rgba(255,255,255,0.1)",
        overflowY: "auto",
        maxHeight: "calc(100vh - 60px)",
        overflowX: "hidden",
        transition: "width 0.3s ease",
        opacity: showRightPanel ? 1 : 0,
      }}
      className="xl:flex hidden"
    >
      <Box
        sx={{
          textAlign: "center",
          width: 268,
          visibility: showRightPanel ? "visible" : "hidden",
        }}
      >
        <ImageCarousel
          images={currentChat?.photo_array || []}
          loading={pic_generate_loading}
          videoUrl={currentChat?.video_url || ""}
        />
        <Typography variant="h6" sx={{ pt: 2, px: 2, textAlign: "left" }}>
          {currentChat?.title}
        </Typography>
        <Typography
          sx={{
            mb: 2,
            textAlign: "left",
            color: "rgba(255,255,255,0.6)",
            fontSize: 14,
            px: 2,
            width: 268,
          }}
        >
          {currentChat?.description}
        </Typography>
        <Box
          sx={{
            display: "flex",
            flexWrap: "wrap",
            gap: 1,
            pb: 6,
            px: 2,
            width: 268,
          }}
        >
          {currentChat?.character_tags?.hobbies
            ?.concat(currentChat?.character_tags?.personality)
            ?.map((tag: string) => (
              <Chip
                key={tag}
                label={tag}
                sx={{
                  bgcolor: "rgba(255,255,255,0.1)",
                  color: "white",
                }}
              />
            ))}
        </Box>
      </Box>
    </Box>
  );
};

export default RightSideBar;
