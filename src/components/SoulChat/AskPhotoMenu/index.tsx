"use client";

import { useState } from "react";
import { useSoulChatStore } from "@/stores/SoulChatStore";
import { Menu, MenuItem, Typography, Box, Tabs, Tab } from "@mui/material";
import { Error as ErrorIcon } from "@mui/icons-material";
import { styled } from "@mui/material/styles";
import { firebaseLogEvent } from "@/utils/utils";
import {
  EVENT_ACTION_INTERACT_MENU_CLICK,
  EVENT_ACTION_INTERACT_MENU_DANCING_CLICK,
  EVENT_ACTION_INTERACT_MENU_READ_MIND_CLICK,
} from "@/utils/event";

interface PhotoMenuProps {
  anchorEl: HTMLElement | null;
  onClose: () => void;
  onSelect: (prompt: string, id?: number) => void;
  handleSendMindReading: (desc: string) => void;
  handleSendDancing: (desc: string, title: string) => void;
}

type TabType = "Interact" | "Ask" | "Story";

const StyledTab = styled(Tab)(() => ({
  color: "rgb(156 163 175)",
  minHeight: "40px",
  padding: "8px 16px",
  textTransform: "none",
  fontSize: "0.875rem",
  fontWeight: 500,
  minWidth: "auto",
  "&.Mui-selected": {
    color: "#fff",
    borderBottom: "2px solid #fff",
  },
  "&:hover:not(.Mui-selected)": {
    color: "rgba(255, 255, 255, 0.8)",
  },
}));

const StyledTabs = styled(Tabs)({
  minHeight: "40px",
  "& .MuiTabs-indicator": {
    display: "none",
  },
  "& .MuiTabs-flexContainer": {
    justifyContent: "space-around",
  },
});

interface TabPanelProps {
  children?: React.ReactNode;
  value: TabType;
  activeTab: TabType;
}

function TabPanel({ children, value, activeTab }: TabPanelProps) {
  return (
    <div hidden={value !== activeTab}>
      {value === activeTab && <Box>{children}</Box>}
    </div>
  );
}

const PhotoMenu = ({
  anchorEl,
  onClose,
  onSelect,
  handleSendMindReading,
  handleSendDancing,
}: PhotoMenuProps) => {
  const { suggestImageTemplates, interactList } = useSoulChatStore();
  const [activeTab, setActiveTab] = useState<TabType>("Interact");

  const otherPrompts = [
    "Send me...",
    "Show me...",
    "Send...",
    "Can I see...",
    "Send me a photo of...",
  ];

  const handleTabChange = (_event: React.SyntheticEvent, newValue: TabType) => {
    setActiveTab(newValue);
  };

  return (
    <Menu
      anchorEl={anchorEl}
      open={Boolean(anchorEl)}
      onClose={onClose}
      anchorOrigin={{
        vertical: "top",
        horizontal: "left",
      }}
      transformOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      slotProps={{
        paper: {
          sx: {
            bgcolor: "#27272a",
            color: "white",
            width: 280,
            mb: 1,
            borderRadius: 2,
            "& .MuiMenuItem-root": {
              py: 1,
              "&:hover": {
                borderRadius: 2,
                bgcolor: "rgba(255,255,255,0.1)",
              },
            },
          },
        },
      }}
    >
      <Box sx={{ borderBottom: "1px solid rgba(255,255,255,0.1)" }}>
        <StyledTabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
        >
          <StyledTab label="Interact" value="Interact" />
          <StyledTab label="Ask" value="Ask" />
          <StyledTab label="Story" value="Story" />
        </StyledTabs>
      </Box>

      <Box sx={{ height: 400, overflowY: "auto" }}>
        <TabPanel value="Interact" activeTab={activeTab}>
          {interactList.map((item) => (
            <MenuItem
              key={item.id}
              onClick={() => {
                firebaseLogEvent(EVENT_ACTION_INTERACT_MENU_CLICK);
                if (item.title === "Mind Reading") {
                  firebaseLogEvent(EVENT_ACTION_INTERACT_MENU_READ_MIND_CLICK);
                  handleSendMindReading(item.desc);
                } else if (["Dancing", "Pole Dancing"].includes(item.title)) {
                  firebaseLogEvent(EVENT_ACTION_INTERACT_MENU_DANCING_CLICK);
                  handleSendDancing(item.desc, item.title);
                }
                onClose();
              }}
              sx={{ px: 1.5 }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <span className="text-sm">{item.emoji}</span>
                <Box>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {item.title}
                  </Typography>
                  <Typography variant="caption" sx={{ color: "#888" }}>
                    {item.desc}
                  </Typography>
                </Box>
              </Box>
            </MenuItem>
          ))}
        </TabPanel>

        <TabPanel value="Ask" activeTab={activeTab}>
          <Box sx={{ px: 1, py: 1 }}>
            <Typography variant="h6">✨ Popular</Typography>
          </Box>

          {suggestImageTemplates.map((option) => (
            <MenuItem
              key={option.id}
              onClick={() => onSelect(option.title, option.id)}
              sx={{ px: 1.5 }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <span className="text-sm">{option.emoji}</span>
                <span className="text-sm">{option.title}</span>
              </Box>
            </MenuItem>
          ))}

          <Box sx={{ borderTop: "1px solid rgba(255,255,255,0.1)", my: 1 }} />

          {otherPrompts.map((prompt) => (
            <MenuItem
              key={prompt}
              onClick={() => onSelect(prompt)}
              className="text-sm"
            >
              <span className="text-sm">{prompt}</span>
            </MenuItem>
          ))}

          <Box sx={{ p: 1 }}>
            <Typography
              variant="caption"
              sx={{ color: "#888", display: "flex", alignItems: "center" }}
            >
              <ErrorIcon sx={{ fontSize: 16, mr: 1 }} />
              <span>Start your prompt with these words to ask for images.</span>
            </Typography>
          </Box>
        </TabPanel>

        <TabPanel value="Story" activeTab={activeTab}>
          <Box sx={{ p: 2, textAlign: "center" }}>
            <Typography variant="body2" sx={{ color: "#888" }}>
              coming soon
            </Typography>
          </Box>
        </TabPanel>
      </Box>
    </Menu>
  );
};

export default PhotoMenu;
