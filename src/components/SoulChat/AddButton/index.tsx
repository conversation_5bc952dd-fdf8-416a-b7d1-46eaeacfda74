import { Box, Fab } from "@mui/material";
import { Add as AddIcon } from "@mui/icons-material";

const AddButton = ({
  onClick,
  isPro,
}: {
  onClick: () => void;
  isPro?: boolean;
}) => {
  return (
    <Box
      sx={{
        position: "relative",
        width: {
          sm: 69,
          xs: 60,
        },
        height: {
          sm: 69,
          xs: 60,
        },
      }}
      onClick={onClick}
    >
      <Fab
        sx={{
          width: "100%",
          height: "100%",
          bgcolor: "grey.700",
          "&:hover": {
            bgcolor: "grey.600",
          },
        }}
      >
        <AddIcon sx={{ color: "white", fontSize: 28 }} />
      </Fab>
      {isPro && (
        <Box
          sx={{
            position: "absolute",
            top: -2,
            right: -10,
            bgcolor: "error.main",
            color: "white",
            fontSize: 12,
            fontWeight: "bold",
            px: 1,
            py: 0.2,
            borderRadius: 6,
            minWidth: 35,
            textAlign: "center",
            zIndex: 1051,
          }}
        >
          Pro
        </Box>
      )}
    </Box>
  );
};

export default AddButton;
