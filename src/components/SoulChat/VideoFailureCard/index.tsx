"use client";

import React from "react";

interface VideoFailureCardProps {
  onRetry?: () => void;
  width?: number | string;
  height?: number | string;
  errorMessage?: string;
  retryText?: string;
}

const VideoFailureCard: React.FC<VideoFailureCardProps> = ({
  onRetry,
  width = 240,
  height = 320,
  errorMessage = "Video generation failed",
  retryText = "Try Again",
}) => {
  return (
    <div
      className="relative flex flex-col items-center justify-center overflow-hidden rounded-2xl shadow-2xl"
      style={{
        width: typeof width === "number" ? `${width}px` : width,
        height: typeof height === "number" ? `${height}px` : height,
        background:
          "linear-gradient(135deg, #8B5A8C 0%, #A855A8 50%, #C2185B 100%)",
      }}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-red-500/20 via-red-600/30 to-red-700/40 z-10" />

      <div className="relative z-20 flex flex-col items-center justify-center gap-6 p-8">
        <div className="relative">
          <div className="absolute inset-0 w-16 h-16 border-4 border-red-300/30 rounded-full animate-ping" />

          <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center border-2 border-red-400">
            <svg
              className="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={3}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </div>
        </div>

        <div className="text-center">
          <h3 className="text-white text-lg font-semibold mb-2">
            Oops! Something went wrong
          </h3>
          <p className="text-white/80 text-sm leading-relaxed">
            {errorMessage}
          </p>
        </div>

        {onRetry && (
          <button
            onClick={onRetry}
            className="group relative px-6 py-3 bg-white/10 hover:bg-white/20 border border-white/30 hover:border-white/50 rounded-full text-white font-medium text-sm transition-all duration-300 ease-in-out transform hover:scale-105 active:scale-95"
          >
            <div className="flex items-center gap-2">
              <svg
                className="w-4 h-4 group-hover:rotate-180 transition-transform duration-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              <span>{retryText}</span>
            </div>

            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-opacity duration-300" />
          </button>
        )}
      </div>

      <div className="absolute top-4 right-4 w-2 h-2 bg-red-400/60 rounded-full animate-pulse" />
      <div className="absolute bottom-6 left-6 w-1 h-1 bg-red-300/40 rounded-full animate-pulse delay-500" />
      <div className="absolute top-1/3 left-4 w-1.5 h-1.5 bg-red-500/50 rounded-full animate-pulse delay-1000" />
    </div>
  );
};

export default VideoFailureCard;
