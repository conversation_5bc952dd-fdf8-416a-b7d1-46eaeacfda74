"use client";
import React from "react";
import { apiService, updateToken } from "@/service/Api";
import { useUserStores } from "@/stores/UserStores";
import { firebaseLogEvent } from "@/utils/utils";
import { EVENT_ACTION_SOULMATES_CREATE_CHAT } from "@/utils/event";
import { generateUUID } from "@/utils/generate-uuid";
import { ROUTES, SOURCE_ID_KEY, USER_UUID } from "@/constant";
import { useRouter } from "@/i18n/routing";

interface StartChatButtonProps {
  templateId: string;
  templateName?: string;
}

export const StartChatButton = ({
  templateId,
  templateName,
}: StartChatButtonProps) => {
  const router = useRouter();
  const { userEntity } = useUserStores();
  const [isLoading, setIsLoading] = React.useState(false);

  const handleClick = async () => {
    if (isLoading) return;
    setIsLoading(true);
    try {
      if (!userEntity) {
        // 匿名登录
        const uuid = generateUUID();
        localStorage.setItem(USER_UUID, uuid);
        const info = await apiService.GetSkipLoginUserInfo({
          user_id: uuid,
        });
        updateToken(info.data.token || "");
      }
      apiService
        .createChat({ bot_template: templateId })
        .then((res) => {
          if (res.code === 200 && res.data.id) {
            const sourceId = localStorage.getItem(SOURCE_ID_KEY);
            firebaseLogEvent(EVENT_ACTION_SOULMATES_CREATE_CHAT, {
              page_path: EVENT_ACTION_SOULMATES_CREATE_CHAT,
              source_id: sourceId,
              character_id: templateId,
              character_name: templateName,
            });
            router.push(`${ROUTES.AI_CHAT}?id=${res.data.id}`);
          }
          setIsLoading(false);
        })
        .catch(() => {
          setIsLoading(false);
        });
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleClick}
      disabled={isLoading}
      className="px-6 py-3 rounded-lg bg-gradient-to-r from-[#CA3C76] to-[#FF6B9D] hover:opacity-90 transition-opacity disabled:opacity-50 relative"
    >
      {isLoading ? (
        <>
          <span className="opacity-0">Backing...</span>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
        </>
      ) : (
        "Back to chat"
      )}
    </button>
  );
};
