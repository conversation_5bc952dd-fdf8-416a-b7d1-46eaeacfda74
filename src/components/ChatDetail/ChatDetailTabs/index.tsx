"use client";

import { useState } from "react";
import { Tabs, Tab, Box } from "@mui/material";
import { styled } from "@mui/material/styles";
import { Favorite as FavoriteIcon } from "@mui/icons-material";

type TabType = "PeopleAlsoLike";

interface TabPanelProps {
  children?: React.ReactNode;
  value: TabType;
  activeTab: TabType;
}

const StyledTab = styled(Tab)(({ theme }) => ({
  color: "rgb(156 163 175)",
  minHeight: "48px",
  padding: "8px 16px",
  borderRadius: "12px",
  textTransform: "none",
  fontSize: "0.875rem",
  fontWeight: 500,
  display: "flex",
  flexDirection: "row",
  gap: "8px",
  alignItems: "center",
  "& .MuiTab-iconWrapper": {
    marginBottom: "0",
    marginRight: "4px",
  },
  "&.Mui-selected": {
    color: "#fff",
    backgroundColor: "var(--primary-color)",
  },
  "&:hover:not(.Mui-selected)": {
    backgroundColor: "rgba(255, 255, 255, 0.08)",
  },
}));

const StyledTabs = styled(Tabs)({
  minHeight: "48px",
  "& .MuiTabs-indicator": {
    display: "none",
  },
  "& .MuiTabs-flexContainer": {
    gap: "12px",
  },
});

function TabPanel({ children, value, activeTab }: TabPanelProps) {
  return (
    <div role="tabpanel" hidden={value !== activeTab} id={`tabpanel-${value}`}>
      {value === activeTab && <Box>{children}</Box>}
    </div>
  );
}

interface ChatDetailTabsProps {
  SoulmateCardComponent: React.ReactNode;
}

export const ChatDetailTabs = ({
  SoulmateCardComponent,
}: ChatDetailTabsProps) => {
  const [activeTab, setActiveTab] = useState<TabType>("PeopleAlsoLike");

  const handleChange = (_event: React.SyntheticEvent, newValue: TabType) => {
    setActiveTab(newValue);
  };

  return (
    <div className="px-4 sm:px-8">
      <Box sx={{ width: "100%" }}>
        <Box>
          <StyledTabs
            value={activeTab}
            onChange={handleChange}
            variant="scrollable"
            scrollButtons="auto"
          >
            <StyledTab
              icon={<FavoriteIcon />}
              label="People Also Like"
              value="PeopleAlsoLike"
              iconPosition="start"
            />
          </StyledTabs>
        </Box>

        <TabPanel value="PeopleAlsoLike" activeTab={activeTab}>
          <div className="mt-4">{SoulmateCardComponent}</div>
        </TabPanel>
      </Box>
    </div>
  );
};
