import React from "react";
import { Box, Modal, Typography, But<PERSON> } from "@mui/material";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/constant";
interface ReminderModalProps {
  open: boolean;
  onClose: () => void;
  isEdit?: boolean;
  callback?: () => void;
}

const ReminderModal: React.FC<ReminderModalProps> = ({
  open,
  onClose,
  isEdit,
  callback,
}) => {
  const router = useRouter();
  return (
    <Modal open={open}>
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "#1E1F24",
          borderRadius: 3,
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.4)",
          p: {
            xs: 1.5,
            sm: 3,
          },
          color: "#fff",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          maxWidth: "400px",
          width: "90%",
        }}
      >
        <div className="flex flex-col items-center text-center gap-4">
          <InfoOutlinedIcon
            sx={{
              fontSize: {
                xs: 36,
                sm: 48,
              },
              color: "var(--primary-color)",
            }}
          />

          <Typography
            variant="h6"
            component="h2"
            className="font-bold"
            sx={{
              fontSize: {
                xs: "1rem",
                sm: "1.25rem",
              },
            }}
          >
            Template Under Review
          </Typography>

          <Typography
            className="text-gray-300"
            sx={{
              fontSize: {
                xs: "0.875rem",
                sm: "1rem",
              },
            }}
          >
            Your template has been submitted for review. Once approved,
            you&apos;ll be able to start chatting.
          </Typography>

          <Button
            onClick={() => {
              onClose();

              if (isEdit) {
                callback?.();
              } else {
                router.push(ROUTES.HOME);
              }
            }}
            variant="contained"
            sx={{
              mt: 2,
              bgcolor: "var(--primary-color)",
              fontSize: {
                xs: "0.875rem",
                sm: "1rem",
              },
              "&:hover": {
                bgcolor: "var(--primary-color)",
                opacity: 0.9,
              },
            }}
          >
            I Understand
          </Button>
        </div>
      </Box>
    </Modal>
  );
};

export default ReminderModal;
