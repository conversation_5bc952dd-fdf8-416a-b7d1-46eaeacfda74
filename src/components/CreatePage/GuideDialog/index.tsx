import { getDomainTitle } from "@/utils/domain";
import { Button, DialogActions } from "@mui/material";
import { DialogContent, DialogTitle } from "@mui/material";
import { Dialog } from "@mui/material";
import { useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";

const GuideDialog = ({
  openGuideDialog,
  setOpenGuideDialog,
}: {
  openGuideDialog: boolean;
  setOpenGuideDialog: (open: boolean) => void;
}) => {
  const t = useTranslations("createPage");
  const [domainTitle, setDomainTitle] = useState("");
  useEffect(() => {
    setDomainTitle(getDomainTitle(window.location.hostname));
  }, []);
  return (
    <Dialog
      open={openGuideDialog}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: "#1A1B1E",
          color: "white",
          borderRadius: "16px",
          backgroundImage:
            "linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.4)",
        },
      }}
    >
      <DialogTitle
        sx={{
          color: "white",
          textAlign: "center",
          fontSize: { xs: 20, sm: 24 },
          fontWeight: 600,
          pt: 4,
          pb: 2,
        }}
      >
        {t("chatbotCreationGuidelines")}
      </DialogTitle>
      <DialogContent sx={{ px: { xs: 2, sm: 4 } }}>
        <div className="text-white/80 space-y-4">
          <p className="mb-6 text-base leading-relaxed">
            {t("chatbotCreationGuidelinesDescription", {
              website: domainTitle,
            })}
          </p>
          <ol className="list-none space-y-4">
            {[
              t("ProhibitionRule1"),
              t("ProhibitionRule2"),
              t("ProhibitionRule3"),
              t("ProhibitionRule4"),
              t("ProhibitionRule5"),
            ].map((rule, index) => (
              <li key={index} className="flex items-start gap-3 text-base">
                <span className="flex items-center justify-center w-6 h-6 rounded-full bg-primary-color/20 text-primary-color shrink-0">
                  {index + 1}
                </span>
                <span className="leading-relaxed">{rule}</span>
              </li>
            ))}
          </ol>
        </div>
      </DialogContent>
      <DialogActions sx={{ p: 3 }}>
        <Button
          onClick={() => {
            setOpenGuideDialog(false);
          }}
          variant="contained"
          fullWidth
          sx={{
            backgroundColor: "var(--primary-color)",
            color: "white",
            py: 1.5,
            borderRadius: "8px",
            textTransform: "none",
            fontSize: "16px",
            "&:hover": {
              backgroundColor: "var(--primary-color-dark)",
            },
          }}
        >
          {t("agree")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default GuideDialog;
