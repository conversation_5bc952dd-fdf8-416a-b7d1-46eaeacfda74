"use client";

import { useEffect, useState, useCallback } from "react";
import { useForm } from "react-hook-form";
import Image from "next/image";
import {
  Add as AddIcon,
  PanoramaFishEye as PanoramaFishEyeIcon,
  CheckCircleOutline as CheckCircleOutlineIcon,
  Search as SearchIcon,
  Close as CloseIcon,
} from "@mui/icons-material";
import { Button, CircularProgress, Popover } from "@mui/material";
import { apiService } from "@/service/Api";
import { useLocale, useTranslations } from "next-intl";
import { useCreateCustomCharacter } from "@/stores/createCustomCharacter";
import { NSFW_TYPE, ROUTES, SOURCE_ID_KEY, VISIBILITY_TYPE } from "@/constant";
import { showToast } from "@/utils/toast";
import {
  EVENT_ACTION_SOULMATES_CREATE,
  EVENT_ACTION_SOULMATES_CREATE_CHAT,
} from "@/utils/event";
import { firebaseLogEvent } from "@/utils/utils";
import { useRouter } from "@/i18n/routing";
import GuideDialog from "@/components/CreatePage/GuideDialog";
import UpgradeDialog from "@/components/CreatePage/UpgradeDialog";
import { useUserStores } from "@/stores/UserStores";
import { debounce } from "lodash";
import ReminderModal from "@/components/ReminderModal";

type FormData = {
  icon: string | null; // 头像
  bot_name: string; // 角色名称
  description: string; // 介绍
  character_tags: {
    personality: string; // 个性
    tags_desc: string; // 标签
  };
  welcome_message: string; // 欢迎语
  scenario: string; // 情景
  example_conversations: {
    user: string;
    character: string;
  }[];

  is_nsfw: typeof NSFW_TYPE.LIMITED | typeof NSFW_TYPE.LIMITLESS;
  is_public: typeof VISIBILITY_TYPE.PUBLIC | typeof VISIBILITY_TYPE.PRIVATE;
};

const CustomCharacterCreateForm = ({
  openGuideDialogDefault = true,
  defaultData = {} as any,
  isEdit = false,
  callback,
}: {
  openGuideDialogDefault?: boolean;
  defaultData?: any;
  isEdit?: boolean;
  callback?: () => void;
}) => {
  const t = useTranslations("createPage");
  const router = useRouter();
  const { example_conversations, is_nsfw, is_public, ...rest } = defaultData;
  const is_public_default =
    is_public === false ? VISIBILITY_TYPE.PRIVATE : VISIBILITY_TYPE.PUBLIC;
  const is_nsfw_default =
    is_nsfw === false ? NSFW_TYPE.LIMITED : NSFW_TYPE.LIMITLESS;

  const { register, handleSubmit, setValue, watch } = useForm<FormData>({
    defaultValues: {
      example_conversations: example_conversations
        ? example_conversations
            ?.split("\n\n")
            ?.filter((conversation: string) => conversation.trim())
            ?.map((conversation: string) => {
              const [userLine, charLine] = conversation.split("\n");
              return {
                user: userLine?.replace("{{user}}: ", "") || "",
                character: charLine?.replace("{{char}}: ", "") || "",
              };
            })
        : [{ user: "", character: "" }],
      is_nsfw: is_nsfw_default ?? NSFW_TYPE.LIMITLESS,
      is_public: is_public_default ?? VISIBILITY_TYPE.PUBLIC,
      ...rest,
    },
  });
  const [avatarPreview, setAvatarPreview] = useState<string | null>(
    defaultData.icon || null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [searchValue, setSearchValue] = useState("");
  const { getTags, tags } = useCreateCustomCharacter();
  const [openGuideDialog, setOpenGuideDialog] = useState(
    openGuideDialogDefault
  );
  const [openUpgradeDialog, setOpenUpgradeDialog] = useState(false);
  const { userEntity } = useUserStores();
  const locale = useLocale();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [openReminderModal, setOpenReminderModal] = useState(false);

  useEffect(() => {
    getTags(locale);
  }, []);

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      try {
        const reader = new FileReader();
        reader.onloadend = () => {
          setAvatarPreview(reader.result as string);
        };
        reader.readAsDataURL(file);
        setIsLoading(true);
        const res = await apiService.uploadImage({ file });
        if (res.code !== 200) {
          setAvatarPreview(null);
          showToast(res.message || "Failed to upload image", "error");
          return;
        }
        setValue("icon", res.data.icon_url);
      } catch (error) {
        setAvatarPreview(null);
        showToast(
          "Failed to upload image, the image is too large, must be less than 1MB",
          "error"
        );
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleRequiredFiled = (data: FormData) => {
    const {
      icon,
      bot_name,
      description,
      character_tags,
      welcome_message,
      scenario,
    } = data;
    // 校验必填项
    if (!icon) {
      showToast("Please upload an avatar", "error");
      return false;
    }
    if (
      !bot_name ||
      !description ||
      !character_tags.personality ||
      !character_tags.tags_desc?.length ||
      !welcome_message ||
      !scenario
    ) {
      showToast("Please fill in all required fields", "error");
      return false;
    }
    return true;
  };

  const handleSubmitWithDebounce = useCallback(
    debounce(async (data: FormData) => {
      if (isSubmitting) return;
      setIsSubmitting(true);
      try {
        // 校验必填项
        const isRequired = handleRequiredFiled(data);
        if (!isRequired) {
          return;
        }

        const {
          icon,
          bot_name,
          description,
          character_tags,
          is_nsfw,
          is_public,
          scenario,
          welcome_message,
          example_conversations,
        } = data;

        let example_conversations_str = "";
        example_conversations.forEach((item) => {
          if (item.user && item.character) {
            example_conversations_str += `{{user}}: ${item.user}\n{{char}}: ${item.character}\n\n`;
          }
        });

        const params = {
          icon: icon || "",
          bot_name,
          description,
          scenario,
          welcome_message,
          character_tags: {
            personality: [character_tags.personality],
            tags_desc: character_tags.tags_desc
              ?.split(",")
              ?.filter((tag) => tag.trim() !== "")
              ?.map((tag) => tag.trim()),
            is_nsfw: is_nsfw === NSFW_TYPE.LIMITED ? false : true,
            is_public: is_public === VISIBILITY_TYPE.PUBLIC ? true : false,
          },
          example_conversations: example_conversations_str,
        };
        let res;
        if (isEdit) {
          res = await apiService.editCustomTemplate({
            ...params,
            template_id: defaultData.id,
          });
        } else {
          res = await apiService.createCustomCharacter(params);
        }
        if (res.code === 200) {
          firebaseLogEvent(EVENT_ACTION_SOULMATES_CREATE);
          showToast(`${isEdit ? "Edit" : "Create"} successfully`, "success");
          if (isEdit) {
            if (data.is_public === VISIBILITY_TYPE.PRIVATE) {
              callback?.();
            } else {
              setOpenReminderModal(true);
            }
          } else {
            if (data.is_public === VISIBILITY_TYPE.PRIVATE) {
              await createChatId(res.data.id);
            } else {
              setOpenReminderModal(true);
            }
          }
        } else {
          showToast(
            `${isEdit ? "Edit" : "Create"} failed: ${res.message}`,
            "error"
          );
        }
      } finally {
        setIsSubmitting(false);
      }
    }, 500),
    [isSubmitting, handleRequiredFiled]
  );

  const handleFormSubmit = (data: FormData) => {
    handleSubmitWithDebounce(data);
  };

  const createChatId = async (id: string) => {
    const res = await apiService.createChat({ bot_template: id });
    if (res.code === 200 && res.data.id) {
      const sourceId = localStorage.getItem(SOURCE_ID_KEY);
      firebaseLogEvent(EVENT_ACTION_SOULMATES_CREATE_CHAT, {
        page_path: EVENT_ACTION_SOULMATES_CREATE_CHAT,
        source_id: sourceId,
        character_id: id,
        character_name: "custom character",
      });
      router.push(`${ROUTES.AI_CHAT}?id=${res.data.id}`);
    }
  };

  const addDialogue = () => {
    const currentDialogues = watch("example_conversations") || [];
    setValue("example_conversations", [
      ...currentDialogues,
      { user: "", character: "" },
    ]);
  };

  const handleColorReturn = (isThemeColor: boolean) => {
    return isThemeColor ? "text-primary-color font-bold" : "text-white/30";
  };

  const handleFocus = (event: React.MouseEvent<HTMLDivElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handlePrivateVisibilityClick = (
    e: React.MouseEvent<HTMLLabelElement>
  ) => {
    e.stopPropagation();
    e.preventDefault();
    if ((userEntity?.entitlement?.member_level?.[0] || 0) <= 0) {
      setOpenUpgradeDialog(true);
    } else {
      setValue("is_public", VISIBILITY_TYPE.PRIVATE);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white sm:p-6 p-3">
      <form
        onSubmit={handleSubmit(handleFormSubmit)}
        className="max-w-2xl mx-auto space-y-6"
      >
        {/* 头像上传区域 */}
        <div className="flex justify-center">
          <div className="relative w-32 h-44 bg-white/5 rounded-xl">
            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
              id="icon-upload"
            />
            <label
              htmlFor="icon-upload"
              className="group cursor-pointer flex items-center justify-center w-full h-full rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-colors backdrop-blur-sm"
              tabIndex={0}
              role="button"
              aria-label="上传头像"
            >
              {avatarPreview ? (
                <div className="relative w-full h-full">
                  <Image
                    src={avatarPreview}
                    alt="Avatar preview"
                    fill
                    className="object-cover rounded-2xl"
                  />
                  {isLoading && (
                    <div className="absolute inset-0 bg-gray-800/50 backdrop-blur-sm rounded-2xl flex items-center justify-center">
                      <CircularProgress />
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center text-primary-color">
                  <AddIcon sx={{ fontSize: 55 }} />
                </div>
              )}
            </label>
          </div>
        </div>

        {/* Character Name */}
        <div className="space-y-2">
          <div className="text-primary-color">
            {t("characterName")} <span className="!text-red-600">*</span>
          </div>
          <input
            {...register("bot_name")}
            className="input-style"
            placeholder={t("characterNamePlaceholder")}
          />
        </div>

        {/* Introduction */}
        <div className="space-y-2">
          <div className="text-primary-color">
            {t("introduction")}
            <span className="!text-red-600 ml-1">*</span>
          </div>
          <input
            {...register("description")}
            className="input-style"
            placeholder={t("introductionPlaceholder")}
          />
        </div>

        {/* Personality */}
        <div className="space-y-2">
          <div className="text-primary-color">
            {t("personality")}
            <span className="!text-red-600 ml-1">*</span>
          </div>
          <textarea
            {...register("character_tags.personality")}
            className="input-style"
            placeholder={t("personalityPlaceholder")}
            rows={3}
          />
        </div>

        {/* Tags */}
        <div className="space-y-2">
          <div className="text-primary-color">
            {t("tags")}
            <span className="!text-red-600 ml-1">*</span>
          </div>
          <div
            className="min-h-10 input-style flex max-h-20 overflow-y-auto"
            onClick={(e) => handleFocus(e)}
          >
            <div className="flex flex-wrap gap-2">
              {watch("character_tags.tags_desc")
                ?.split(",")
                .filter((tag) => tag.trim() !== "")
                ?.map((tag, index) => (
                  <span
                    key={index}
                    className="bg-white/10 px-3 py-1 rounded-full text-white/70 text-sm"
                  >
                    {tag.trim()}
                    <span
                      className="text-white/30 ml-2 cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        const currentValue = watch("character_tags.tags_desc");
                        const newValue = currentValue?.replace(tag, "");
                        setValue("character_tags.tags_desc", newValue);
                      }}
                    >
                      <CloseIcon sx={{ fontSize: 16 }} />
                    </span>
                  </span>
                ))}
            </div>
            {!watch("character_tags.tags_desc")?.length && (
              <p className="text-white/30">{t("tagsPlaceholder")}</p>
            )}
          </div>

          <Popover
            open={Boolean(anchorEl)}
            anchorEl={anchorEl}
            onClose={handleClose}
            disableRestoreFocus
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "left",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "left",
            }}
            PaperProps={{
              sx: {
                width: "288px",
                backgroundColor: "#0f0d0d",
                color: "white",
                mt: 1,
              },
            }}
          >
            <div className="p-2">
              <div className="relative">
                <SearchIcon
                  sx={{
                    position: "absolute",
                    left: "8px",
                    top: "50%",
                    transform: "translateY(-50%)",
                    color: "rgba(255, 255, 255, 0.5)",
                    fontSize: "20px",
                  }}
                />
                <input
                  type="text"
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  className="w-full bg-black/30 rounded p-2 pl-9 outline-none"
                  placeholder="Search tag..."
                />
              </div>
            </div>

            <div className="px-2 pb-2 max-h-40 overflow-y-auto">
              <div className="text-sm text-gray-400 mb-2">
                {t("suggestions")}
              </div>
              {tags
                .filter((tag) =>
                  tag.label.toLowerCase().includes(searchValue.toLowerCase())
                )
                .map((tag) => (
                  <div
                    key={tag.label}
                    className="px-2 py-1.5 hover:bg-white/10 rounded cursor-pointer"
                    onClick={(e) => {
                      const currentValue = watch("character_tags.tags_desc");
                      if (currentValue?.includes(tag.label)) {
                        return;
                      }
                      const newValue = currentValue
                        ? `${currentValue}, ${tag.label}`
                        : tag.label;
                      setValue("character_tags.tags_desc", newValue);
                    }}
                  >
                    {tag.label}
                  </div>
                ))}
            </div>
          </Popover>
        </div>

        {/* Initial Message (Greeting)* */}
        <div className="space-y-2">
          <div className="text-primary-color">
            {t("initialMessage")}
            <span className="!text-red-600 ml-1">*</span>
          </div>
          <input
            {...register("welcome_message")}
            className="input-style"
            placeholder={t("initialMessagePlaceholder", {
              user: watch("bot_name") || "{{user}}",
              char: "{{char}}",
            })}
          />
        </div>

        {/* scenario */}
        <div className="space-y-2">
          <div className="text-primary-color">
            {t("scenario")}
            <span className="!text-red-600 ml-1">*</span>
          </div>
          <textarea
            {...register("scenario")}
            className="input-style"
            placeholder={t("scenarioPlaceholder")}
            rows={3}
          />
        </div>

        {/* Example Dialogue */}
        <div className="space-y-2">
          <div className="text-primary-color">{t("exampleDialogue")}</div>
          <div className="text-xs text-white/30">
            {t("exampleDialogueInfo")}
          </div>
          <div className="space-y-4 bg-primary-color/10 p-6 rounded-lg">
            {watch("example_conversations")?.map((_, index) => (
              <div key={index} className="space-y-4">
                <div className="relative flex justify-end">
                  {index > 0 && (
                    <Image
                      src="/clearIcon.svg"
                      alt="delete"
                      width={20}
                      height={20}
                      className="cursor-pointer mr-3"
                      onClick={() => {
                        const currentDialogues =
                          watch("example_conversations") || [];
                        currentDialogues.splice(index, 1);
                        setValue("example_conversations", currentDialogues);
                      }}
                    />
                  )}
                  <input
                    {...register(`example_conversations.${index}.user`)}
                    className="input-style !w-1/2"
                    placeholder={t("exampleDialogueUser")}
                  />
                </div>
                <div className="relative">
                  <input
                    {...register(`example_conversations.${index}.character`)}
                    className="input-style !bg-primary-color/50"
                    placeholder={t("exampleDialogueCharacter")}
                  />
                </div>
              </div>
            ))}
            <button
              type="button"
              onClick={addDialogue}
              className="flex justify-center items-center w-full mt-4"
            >
              <span className="text-primary-color">+{t("add")}</span>
            </button>
          </div>
        </div>

        {/* Rating */}
        <div className="space-y-2">
          <div className="text-primary-color">{t("rating")}</div>
          <div className="flex gap-4">
            <label className="flex-1 flex items-center gap-2 bg-white/5 p-4 rounded-lg cursor-pointer">
              <input
                type="radio"
                {...register("is_nsfw")}
                value={NSFW_TYPE.LIMITED}
                className="hidden"
              />
              {watch("is_nsfw") === NSFW_TYPE.LIMITED ? (
                <CheckCircleOutlineIcon className={handleColorReturn(true)} />
              ) : (
                <PanoramaFishEyeIcon className={handleColorReturn(false)} />
              )}
              <span
                className={handleColorReturn(
                  watch("is_nsfw") === NSFW_TYPE.LIMITED
                )}
              >
                {t("ratingLimited")}
              </span>
            </label>

            <label className="flex-1 flex items-center gap-2 bg-white/5 p-4 rounded-lg cursor-pointer">
              <input
                type="radio"
                {...register("is_nsfw")}
                value={NSFW_TYPE.LIMITLESS}
                className="hidden"
              />
              {watch("is_nsfw") === NSFW_TYPE.LIMITLESS ? (
                <CheckCircleOutlineIcon className={handleColorReturn(true)} />
              ) : (
                <PanoramaFishEyeIcon className={handleColorReturn(false)} />
              )}
              <span
                className={handleColorReturn(
                  watch("is_nsfw") === NSFW_TYPE.LIMITLESS
                )}
              >
                {t("ratingLimitless")}
              </span>
            </label>
          </div>
          <div className="text-sm text-white/30">
            {t("ratingLimitlessDescription")}
          </div>
        </div>

        {/* Visibility */}
        <div className="space-y-2">
          <div className="text-primary-color">{t("visibility")}</div>
          <div className="flex gap-4">
            <label className="flex-1 flex items-center gap-2 bg-white/5 p-4 rounded-lg cursor-pointer">
              <input
                type="radio"
                {...register("is_public")}
                value={VISIBILITY_TYPE.PUBLIC}
                className="hidden"
              />
              {watch("is_public") === VISIBILITY_TYPE.PUBLIC ? (
                <CheckCircleOutlineIcon className={handleColorReturn(true)} />
              ) : (
                <PanoramaFishEyeIcon className={handleColorReturn(false)} />
              )}
              <span
                className={handleColorReturn(
                  watch("is_public") === VISIBILITY_TYPE.PUBLIC
                )}
              >
                {t("visibilityPublic")}
              </span>
            </label>

            <label
              className="flex-1 flex items-center gap-2 bg-white/5 p-4 rounded-lg cursor-pointer"
              onClick={handlePrivateVisibilityClick}
            >
              <input
                type="radio"
                {...register("is_public")}
                value={VISIBILITY_TYPE.PRIVATE}
                className="hidden"
              />
              {watch("is_public") === VISIBILITY_TYPE.PRIVATE ? (
                <CheckCircleOutlineIcon className={handleColorReturn(true)} />
              ) : (
                <PanoramaFishEyeIcon className={handleColorReturn(false)} />
              )}
              <span
                className={handleColorReturn(
                  watch("is_public") === VISIBILITY_TYPE.PRIVATE
                )}
              >
                {t("visibilityPrivate")}
              </span>
            </label>
          </div>
        </div>
        <Button
          type="submit"
          variant="contained"
          disabled={isSubmitting}
          sx={{
            width: "100%",
            backgroundColor: "var(--primary-color)",
            "&:hover": {
              backgroundColor: "var(--primary-color-dark)",
            },
            "&:disabled": {
              backgroundColor: "var(--primary-color-dark)",
              opacity: 0.7,
            },
          }}
        >
          {isSubmitting ? (
            <div className="flex items-center justify-center gap-2">
              <CircularProgress size={20} sx={{ color: "white" }} />
              <span className="text-white">
                {isEdit ? t("editing") : t("creating")}
              </span>
            </div>
          ) : isEdit ? (
            t("edit")
          ) : (
            t("create")
          )}
        </Button>
      </form>
      <GuideDialog
        openGuideDialog={openGuideDialog}
        setOpenGuideDialog={setOpenGuideDialog}
      />
      <UpgradeDialog
        open={openUpgradeDialog}
        onClose={() => setOpenUpgradeDialog(false)}
      />
      <ReminderModal
        open={openReminderModal}
        onClose={() => setOpenReminderModal(false)}
        isEdit={isEdit}
        callback={callback}
      />
    </div>
  );
};

export default CustomCharacterCreateForm;
