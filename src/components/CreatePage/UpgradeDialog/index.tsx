import { Dialog, DialogContent, IconButton } from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";
import { useTranslations } from "next-intl";
import React from "react";
import { ROUTES } from "@/constant";
import { useRouter } from "@/i18n/routing";

interface UpgradeDialogProps {
  open: boolean;
  onClose: () => void;
}

const UpgradeDialog = ({ open, onClose }: UpgradeDialogProps) => {
  const t = useTranslations("createPage");
  const router = useRouter();

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: "#1A1B1E",
          color: "white",
          borderRadius: "16px",
          backgroundImage:
            "linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.4)",
        },
      }}
    >
      <IconButton
        onClick={onClose}
        sx={{
          position: "absolute",
          right: 8,
          top: 8,
          color: "white",
        }}
      >
        <CloseIcon />
      </IconButton>

      <DialogContent sx={{ p: 4, textAlign: "center" }}>
        <h2 className="text-xl font-semibold mb-4">{t("upgrade")}</h2>

        <div className="text-5xl mb-4">🤫</div>

        <p className="text-white/80 mb-6">{t("upgradeDescription")}</p>

        <button
          onClick={() => {
            onClose();
            router.push(ROUTES.SUBSCRIBE);
          }}
          className="w-full bg-primary-color hover:bg-primary-color/80 text-white py-3 px-6 rounded-lg transition-colors"
        >
          {t("upgradeButton")}
        </button>
      </DialogContent>
    </Dialog>
  );
};

export default UpgradeDialog;
