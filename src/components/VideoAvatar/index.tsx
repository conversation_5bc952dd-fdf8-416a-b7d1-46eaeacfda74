"use client";

import React, { useRef, useEffect } from "react";
import {
  useDeviceDetection,
  getVideoAutoPlayStrategy,
} from "@/hooks/useDeviceDetection";

interface VideoAvatarProps {
  src: string;
  className?: string;
  style?: React.CSSProperties;
  poster?: string;
  width?: number | string;
  height?: number | string;
}

const VideoAvatar: React.FC<VideoAvatarProps> = ({
  src,
  className = "",
  style = {},
  poster,
  width,
  height,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const deviceInfo = useDeviceDetection();
  const { autoPlay, shouldAttemptPlay } = getVideoAutoPlayStrategy(deviceInfo);

  useEffect(() => {
    const video = videoRef.current;
    if (!video || !shouldAttemptPlay) return;

    // 尝试播放视频
    const attemptPlay = async () => {
      try {
        await video.play();
      } catch (error) {
        console.debug("Video autoplay failed:", error);
      }
    };

    const timer = setTimeout(attemptPlay, 100);

    return () => clearTimeout(timer);
  }, [shouldAttemptPlay, src]);

  const posterUrl =
    poster || (src.includes(".mp4") ? src.replace(".mp4", ".jpg") : undefined);

  return (
    <video
      ref={videoRef}
      src={src}
      autoPlay={autoPlay}
      loop
      muted
      playsInline // 防止 iOS 全屏播放
      poster={posterUrl}
      className={className}
      style={{
        width: width || style.width,
        height: height || style.height,
        minWidth: width || style.width,
        minHeight: height || style.height,
        objectFit: "cover",
        ...style,
      }}
      onLoadStart={(e) => {
        const video = e.currentTarget;
        if (width)
          video.style.width = typeof width === "number" ? `${width}px` : width;
        if (height)
          video.style.height =
            typeof height === "number" ? `${height}px` : height;
      }}
      onError={(e) => {
        console.error("Video failed to load:", e);
      }}
    />
  );
};

export default VideoAvatar;
