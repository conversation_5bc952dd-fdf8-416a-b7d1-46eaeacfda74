import React from "react";
import { headers } from "next/headers";

import { apiService } from "@/service/Api";
import CardBox from "../SoulChat/SoulmateCard/CardBox";
import ReloadBtn from "./Comps/ReloadBtn";

async function fetchHomeList({
  locale = "en",
  interested_in,
}: {
  locale: string;
  interested_in?: string;
}) {
  const homeCardList = (
    await apiService.getHomeList({
      locale,
      interested_in,
    })
  )?.data;
  return {
    homeCards: homeCardList?.bot_templates || [],
    total_count: homeCardList?.total_count || 0,
  };
}
const HomeCard = async ({ locale }: { locale: string }) => {
  const cookies = (await headers()).get("cookie") || "";
  const interested_in = cookies
    .split(";")
    .find((c) => c.trim().startsWith("interested_in="));
  const { homeCards } = await fetchHomeList({
    locale,
    interested_in: interested_in?.split("=")[1],
  });
  return (
    <>
      <div className="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4 py-3">
        {homeCards?.map((soulmate, index) => (
          <CardBox key={soulmate.id} soulmate={soulmate} index={index} />
        ))}
      </div>
      <ReloadBtn />
    </>
  );
};

export default HomeCard;
