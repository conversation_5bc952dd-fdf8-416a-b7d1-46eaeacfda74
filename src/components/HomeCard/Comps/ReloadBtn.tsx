"use client";

import React, { useEffect, useState, useTransition } from "react";
import { Button } from "@mui/material";
import { Refresh as RefreshIcon } from "@mui/icons-material";

import { useRouter } from "@/i18n/routing";
import { useLoadingToast } from "@/hooks/useLoadingToast";

const ReloadBtn = () => {
  const router = useRouter();
  const { showLoading, dismissLoading } = useLoadingToast();
  const [isPending, startTransition] = useTransition();
  const [loadingId, setLoadingId] = useState<string | null>(null);

  useEffect(() => {
    if (!isPending && loadingId) {
      dismissLoading(loadingId);
    }
  }, [isPending, loadingId]);

  const handleReload = () => {
    startTransition(() => {
      router.replace("/");
      router.refresh();
    });
    const toastId = showLoading();
    setLoadingId(toastId);
  };

  return (
    <div className="flex justify-center items-center mt-4">
      <Button
        variant="contained"
        className="flex items-center gap-2 !bg-[#3c3c42] hover:!bg-[#4a4a52] text-white px-4 py-2 rounded-md"
        onClick={handleReload}
        disabled={isPending}
      >
        <RefreshIcon />
        {isPending ? "loading..." : "Reload"}
      </Button>
    </div>
  );
};

export default ReloadBtn;
