"use client";

import { TemplateGenderEnum, TemplateStyleEnum } from "@/types";
import React from "react";
import {
  Mars,
  Venus,
  LucideTransgender,
  PawPrint,
  LucideUser,
} from "lucide-react";
import { useRouter } from "@/i18n/routing";
import { useTranslations } from "next-intl";

// 定义选项的类型
interface CategoryOption {
  value: TemplateGenderEnum | TemplateStyleEnum;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
}

const CategorySearch = ({
  interested_in,
  style,
}: {
  interested_in: TemplateGenderEnum | undefined;
  style: TemplateStyleEnum | undefined;
}) => {
  const router = useRouter();
  const t = useTranslations("CategorySearch");
  const genderOptions: CategoryOption[] = [
    { value: TemplateGenderEnum.Female, label: t("Female"), icon: Venus },
    { value: TemplateGenderEnum.Male, label: t("Male"), icon: Mars },
    {
      value: TemplateGenderEnum.Trans,
      label: t("Trans"),
      icon: LucideTransgender,
    },
  ];

  const styleOptions: CategoryOption[] = [
    {
      value: TemplateStyleEnum.Realistic,
      label: t("Realistic"),
      icon: LucideUser,
    },
    { value: TemplateStyleEnum.Anime, label: t("Anime"), icon: PawPrint },
  ];
  const renderStyle = (
    current: TemplateGenderEnum | TemplateStyleEnum | undefined,
    compare: TemplateGenderEnum | TemplateStyleEnum
  ) => {
    if (current === compare) {
      return "bg-pink-600 text-white";
    }
    return "bg-gradient-to-r from-gray-700 to-gray-900 text-white shadow-md hover:shadow-lg transition-all duration-300";
  };

  // 通用的按钮渲染组件
  const RenderButton = ({
    option,
    currentValue,
    onClick,
  }: {
    option: CategoryOption;
    currentValue: TemplateGenderEnum | TemplateStyleEnum | undefined;
    onClick: (value: TemplateGenderEnum | TemplateStyleEnum) => void;
  }) => {
    const Icon = option.icon;
    return (
      <button
        className={`flex items-center gap-2 sm:px-4 sm:py-2 p-2 py-1 rounded-2xl border-2 border-transparent hover:border-pink-600 ${renderStyle(
          currentValue,
          option.value
        )} transition-all duration-300`}
        onClick={() => onClick(option.value)}
      >
        <Icon className="w-6 h-6" />
        <span>{option.label}</span>
      </button>
    );
  };

  const handleClick = (
    key: string,
    value: TemplateGenderEnum | TemplateStyleEnum
  ) => {
    const currentParams = new URLSearchParams(window.location.search);
    currentParams.set("page", "1");
    if (currentParams.has(key) && currentParams.get(key) === value) {
      currentParams.delete(key);
    } else {
      currentParams.set(key, value);
    }
    router.push(`?${currentParams.toString()}`);
  };

  return (
    <div className="flex flex-col md:flex-row gap-4 mb-6 p-4 pt-0 bg-gradient-to-b from-gray-800 to-black rounded-lg shadow-xl">
      {/* 性别选择 */}
      <div className="flex gap-4 justify-center pr-4 md:border-r-2 md:border-primary-color">
        {genderOptions.map((option) => (
          <RenderButton
            key={option.value}
            option={option}
            currentValue={interested_in}
            onClick={() => {
              handleClick("interested_in", option.value);
            }}
          />
        ))}
      </div>
      {/* 风格选择 */}
      <div className="flex gap-4 justify-center">
        {styleOptions.map((option) => (
          <RenderButton
            key={option.value}
            option={option}
            currentValue={style}
            onClick={() => {
              handleClick("style", option.value);
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default CategorySearch;
