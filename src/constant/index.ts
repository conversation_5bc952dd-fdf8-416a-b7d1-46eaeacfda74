export const INVITE_CODE_KEY = "code";
export const STORAGE_USER_INFO = "USER_INFO";
export const SOURCE_ID_KEY = "sourceId";
export const STORAGE_TOKEN = "TOKEN";
export const STORAGE_CHAT_NSFW = "CHAT_NSFW";

export const ROUTES = {
  AI_CHAT: "/ai-chat",
  SOUL_CHAT: "/soul-chat",
  TAGS: "/tags",
  HOME: "/",
  CREATE: "/create",
  TERMS_OF_SERVICE: "/terms-of-service",
  PRIVACY_POLICY: "/privacy-policy",
  RETURN_POLICY: "/return-policy",
  COMMUNITY_GUIDELINES: "/community-guidelines",
  UNDERAGE_POLICY: "/underage-policy",
  CONTENT_REMOVAL_POLICY: "/content-removal-policy",
  BLOCKED_CONTENT_POLICY: "/blocked-content-policy",
  DMCA_POLICY: "/dmca-policy",
  COMPLAINT_POLICY: "/complaint-policy",
  USC_2257_EXEMPTION: "/usc-2257-exemption",
  LOGI<PERSON>: "/login",
  SUBSCRIBE: "/subscribe",
  ACCOUNT_INFO: "/account-info",
  COINS: "/coins",
  CHAT_DETAIL: "/chatDetail",
  CREATE_IMAGE: "/create-image",
  MY_CREATIONS: "/my-creations",
};

export const SCENE = {
  CREATE_IMAGE: "create_image",
  CREATE_CHARACTER: "create_character",
};

export const HOBBIES_AND_PERSONALITY = {
  HOBBIES: "hobbies",
  PERSONALITY: "personality",
};

export const HOBBIES_AND_PERSONALITY_ARR = [
  HOBBIES_AND_PERSONALITY.HOBBIES,
  HOBBIES_AND_PERSONALITY.PERSONALITY,
];

export const PROJECT_STATUS = {
  GENERATING: "generating",
  GENERATED: "image_generated",
  FAILED: "failed",
};

export const APP_ID_DELORIS = "deloris";

export const AUTO_PLAY_KEY = "isAutoPlaying";

export const COST_TYPE = {
  IMAGE_IN_CHAT: "image_in_chat",
  CUSTOM_CHARACTER: "custom_character",
  CREATE_IMAGE_NORMAL: "create_image_normal",
};

export const NEED_SUB_TEXT = '"actions": ["SUBSCRIBE"]';
export const SIGN_UP_TEXT = '"actions": ["Sign up"]';
export const BUY_TEXT = '"actions": ["Buy"]';

export enum TaskType {
  video = "video",
  image = "image",
  imageInChat = "imageInChat",
}

export const STORAGE_FACE_MODEL_LIST = "FACE_MODEL_LIST";

export enum FaceModelStatus {
  "uncreate" = 0,
  "created" = 1,
  "uploaded" = 2,
  "face_detected" = 3,
}

export enum SubscriptionChannel {
  Stripe = "Stripe",
}

export const DOMAIN = {
  NSFWCHAT: "www.nsfwchat.app",
  PERCHANCEAI: "www.perchanceai.chat",
  EHE: "www.ehentai.chat",
  SWEETAI: "www.sweetai.chat",
};

export const SOULMATE_PAGE_SIZE = 20;

export const USER_UUID = "user_uuid";

export const EQUAL_OR_INCLUDE = {
  EQUAL: "equal",
  INCLUDES: "includes",
};

export const NSFW_TYPE = {
  LIMITED: "Limited",
  LIMITLESS: "Limitless",
};

export const VISIBILITY_TYPE = {
  PUBLIC: "Public",
  PRIVATE: "Private",
};

export const BILLING_CYCLE = {
  MONTHLY: "monthly",
  YEARLY: "yearly",
} as const;

export const LOGIN_CODE = [100001, 100002, 100003, 401, 403];

export const SUBSCRIBE_PRODUCT_PRO = "Deluxe";

export const SUBSCRIPTION_TYPE = {
  AUTO_RENEWABLE: "auto_renewable",
  NON_RENEWABLE: "non_renewable",
} as const;

export const STORAGE_KEY = {
  GENDER: "gender",
  STYLE: "style",
};

export const PUBLIC_STATUS = {
  PENDING: 0,
  APPROVED: 1,
  REJECTED: 2,
} as const;

export const MESSAGE_TYPE = {
  image: "image",
  mind_reading: "mind_reading",
  video: "video",
  video_error: "video_error",
};

export const MESSAGE_TYPE_API = {
  shortcuts_mind_reading: "shortcuts_mind_reading",
  shortcuts_video: "shortcuts_video",
};
