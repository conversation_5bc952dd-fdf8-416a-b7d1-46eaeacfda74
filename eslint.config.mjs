import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      "@typescript-eslint/no-explicit-any": "off", // 全局禁用
      "@typescript-eslint/no-unused-vars": [
        "off", // 设置为 "off", "warn", 或 "error"
        {
          args: "none", // 忽略未使用的函数参数
          varsIgnorePattern: "none", // 忽略名称以 `_` 开头的变量
        },
      ],
      "react-hooks/exhaustive-deps": "off",
    },
  },
];

export default eslintConfig;
