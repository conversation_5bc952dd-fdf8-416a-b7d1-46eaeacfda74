import type { Config } from "tailwindcss";

export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        "dark-bg": "rgb(25,27,32)",
        "primary-color": "rgb(202, 60, 118)",
        "secondary-color": "rgb(202, 60, 160)",
        "secondary-light-color": "rgb(237, 147, 213)",
        "primary-light-color": "rgb(237, 147, 183)",
        gray: "rgb(43, 45, 49)",
        "gray-d": "#ccc",
        icon: "rgb(202, 60, 118)",
        "gray-light": "#334155",
        "gray-dark": "#1e293b",
      },
      backgroundImage: {
        "price-banner":
          "url('https://s3.us-east-2.amazonaws.com/www.deloris-ai.com/bgi.avif')",
      },
    },
  },
  plugins: [],
} satisfies Config;
