{"name": "soulchat", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3000 --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/icons-material": "^6.3.0", "@mui/material": "^6.3.0", "@react-oauth/google": "^0.12.1", "@tiptap/core": "^2.11.5", "@tiptap/extension-code": "^2.11.5", "@tiptap/extension-code-block-lowlight": "^2.11.5", "@tiptap/extension-heading": "^2.11.5", "@tiptap/extension-highlight": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/extension-strike": "^2.11.5", "@tiptap/extension-subscript": "^2.11.5", "@tiptap/extension-superscript": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@vercel/functions": "^2.0.0", "axios": "^1.7.9", "clsx": "^2.1.1", "driver.js": "^1.3.5", "file-saver": "^2.0.5", "firebase": "^11.1.0", "jszip": "^3.10.1", "lodash": "^4.17.21", "lowlight": "^3.3.0", "lucide-react": "^0.479.0", "next": "15.1.3", "next-auth": "5.0.0-beta.25", "next-intl": "^3.26.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.4.1", "react-image-lightbox": "^5.1.4", "react-markdown": "^9.0.1", "react-responsive": "^10.0.0", "rehype-raw": "^7.0.0", "uuid": "^11.0.4", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.13", "@types/node": "^20", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5.7.2"}, "packageManager": "pnpm@10.5.0+sha512.11106a5916c7406fe4b8cb8e3067974b8728f47308a4f5ac5e850304afa6f57e2847d7950dfe78877d8d36bfb401d381c4215db3a4c3547ffa63c14333a6fa51"}